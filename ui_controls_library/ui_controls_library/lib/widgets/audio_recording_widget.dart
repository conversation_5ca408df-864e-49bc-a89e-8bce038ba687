import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:record/record.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:path/path.dart' as path;
import 'package:flutter/foundation.dart' show kIsWeb;

// Conditional import for web-specific functionality
import 'audio_recording_widget_stub.dart'
    if (dart.library.html) 'audio_recording_widget_web.dart';

/// A widget that provides audio recording functionality.
///
/// This widget allows users to record audio using the device's microphone.
/// It supports various configuration options such as recording quality,
/// duration limits, UI customization, and waveform visualization.
class AudioRecordingWidget extends StatefulWidget {
  /// Maximum recording duration in seconds (null for unlimited)
  final int? maxDuration;

  /// Whether to show a timer during recording
  final bool showTimer;

  /// Whether to show recording controls
  final bool showControls;

  /// Custom text for the record button
  final String? recordButtonText;

  /// Custom text for the stop button
  final String? stopButtonText;

  /// Custom text for the pause button
  final String? pauseButtonText;

  /// Custom text for the resume button
  final String? resumeButtonText;

  /// Color of the record button
  final Color recordButtonColor;

  /// Color of the stop button
  final Color stopButtonColor;

  /// Color of the pause button
  final Color pauseButtonColor;

  /// Color of the resume button
  final Color resumeButtonColor;

  /// Whether to show a preview after recording
  final bool showPlayback;

  /// Whether to show a waveform visualization
  final bool showWaveform;

  /// Color of the waveform
  final Color waveformColor;

  /// Recording quality (low, medium, high)
  final RecordingQuality quality;

  /// Whether to use noise suppression
  final bool noiseSuppressionEnabled;

  /// Whether to use echo cancellation
  final bool echoCancellationEnabled;

  /// Whether to use auto gain control
  final bool autoGainControlEnabled;

  /// Number of audio channels (1 for mono, 2 for stereo)
  final int numChannels;

  /// Custom theme color for the widget
  final Color? themeColor;

  /// Whether to show a label for the recording
  final bool showLabel;

  /// Custom label text
  final String? labelText;

  /// Whether to show a save button after recording
  final bool showSaveButton;

  /// Custom save button text
  final String? saveButtonText;

  /// Callback when audio is recorded
  final Function(String audioPath)? onAudioRecorded;

  /// Callback when audio is saved
  final Function(String audioPath)? onAudioSaved;

  /// Callback when recording starts
  final Function()? onRecordingStarted;

  /// Callback when recording stops
  final Function()? onRecordingStopped;

  /// Callback when recording is paused
  final Function()? onRecordingPaused;

  /// Callback when recording is resumed
  final Function()? onRecordingResumed;

  const AudioRecordingWidget({
    super.key,
    this.maxDuration,
    this.showTimer = true,
    this.showControls = true,
    this.recordButtonText,
    this.stopButtonText,
    this.pauseButtonText,
    this.resumeButtonText,
    this.recordButtonColor = Colors.red,
    this.stopButtonColor = Colors.red,
    this.pauseButtonColor = Colors.blue,
    this.resumeButtonColor = Colors.green,
    this.showPlayback = true,
    this.showWaveform = false,
    this.waveformColor = Colors.blue,
    this.quality = RecordingQuality.medium,
    this.noiseSuppressionEnabled = true,
    this.echoCancellationEnabled = true,
    this.autoGainControlEnabled = true,
    this.numChannels = 1,
    this.themeColor,
    this.showLabel = true,
    this.labelText,
    this.showSaveButton = true,
    this.saveButtonText,
    this.onAudioRecorded,
    this.onAudioSaved,
    this.onRecordingStarted,
    this.onRecordingStopped,
    this.onRecordingPaused,
    this.onRecordingResumed,
    this.testErrorMessage,
    this.testInitialRecording = false,
  });

  /// For testing purposes only - set an initial error message
  final String? testErrorMessage;

  /// For testing purposes only - set an initial recording state
  final bool testInitialRecording;

  /// Creates an AudioRecordingWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the AudioRecordingWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "maxDuration": 60,
  ///   "showTimer": true,
  ///   "showControls": true,
  ///   "recordButtonText": "Start Recording",
  ///   "quality": "high",
  ///   "showWaveform": true,
  ///   "waveformColor": "#0000ff"
  /// }
  /// ```
  factory AudioRecordingWidget.fromJson(Map<String, dynamic> json) {
    // Parse recording quality
    RecordingQuality quality = RecordingQuality.medium;
    if (json['quality'] != null) {
      switch (json['quality'].toString().toLowerCase()) {
        case 'low':
          quality = RecordingQuality.low;
          break;
        case 'medium':
          quality = RecordingQuality.medium;
          break;
        case 'high':
          quality = RecordingQuality.high;
          break;
      }
    }

    return AudioRecordingWidget(
      // Basic settings
      maxDuration: json['maxDuration'],
      showTimer: json['showTimer'] ?? true,
      showControls: json['showControls'] ?? true,

      // Button text
      recordButtonText: json['recordButtonText'],
      stopButtonText: json['stopButtonText'],
      pauseButtonText: json['pauseButtonText'],
      resumeButtonText: json['resumeButtonText'],

      // Button colors
      recordButtonColor:
          _colorFromJson(json['recordButtonColor']) ?? Colors.red,
      stopButtonColor: _colorFromJson(json['stopButtonColor']) ?? Colors.red,
      pauseButtonColor: _colorFromJson(json['pauseButtonColor']) ?? Colors.blue,
      resumeButtonColor:
          _colorFromJson(json['resumeButtonColor']) ?? Colors.green,

      // Playback and visualization
      showPlayback: json['showPlayback'] ?? true,
      showWaveform: json['showWaveform'] ?? false,
      waveformColor: _colorFromJson(json['waveformColor']) ?? Colors.blue,

      // Audio quality and processing
      quality: quality,
      noiseSuppressionEnabled: json['noiseSuppressionEnabled'] ?? true,
      echoCancellationEnabled: json['echoCancellationEnabled'] ?? true,
      autoGainControlEnabled: json['autoGainControlEnabled'] ?? true,
      numChannels: json['numChannels'] ?? 1,

      // Appearance
      themeColor: _colorFromJson(json['themeColor']),

      // Label
      showLabel: json['showLabel'] ?? true,
      labelText: json['labelText'],

      // Save button
      showSaveButton: json['showSaveButton'] ?? true,
      saveButtonText: json['saveButtonText'],
    );
  }

  /// Converts the AudioRecordingWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    return {
      // Basic settings
      if (maxDuration != null) 'maxDuration': maxDuration,
      'showTimer': showTimer,
      'showControls': showControls,

      // Button text
      if (recordButtonText != null) 'recordButtonText': recordButtonText,
      if (stopButtonText != null) 'stopButtonText': stopButtonText,
      if (pauseButtonText != null) 'pauseButtonText': pauseButtonText,
      if (resumeButtonText != null) 'resumeButtonText': resumeButtonText,

      // Button colors
      'recordButtonColor': _colorToJson(recordButtonColor),
      'stopButtonColor': _colorToJson(stopButtonColor),
      'pauseButtonColor': _colorToJson(pauseButtonColor),
      'resumeButtonColor': _colorToJson(resumeButtonColor),

      // Playback and visualization
      'showPlayback': showPlayback,
      'showWaveform': showWaveform,
      'waveformColor': _colorToJson(waveformColor),

      // Audio quality and processing
      'quality': _qualityToJson(quality),
      'noiseSuppressionEnabled': noiseSuppressionEnabled,
      'echoCancellationEnabled': echoCancellationEnabled,
      'autoGainControlEnabled': autoGainControlEnabled,
      'numChannels': numChannels,

      // Appearance
      if (themeColor != null) 'themeColor': _colorToJson(themeColor!),

      // Label
      'showLabel': showLabel,
      if (labelText != null) 'labelText': labelText,

      // Save button
      'showSaveButton': showSaveButton,
      if (saveButtonText != null) 'saveButtonText': saveButtonText,
    };
  }

  /// Converts a JSON color value to a Flutter Color
  ///
  /// Accepts hex strings (e.g., "#FF0000"), color names (e.g., "red"),
  /// or integer values (e.g., 0xFFFF0000)
  static Color? _colorFromJson(dynamic colorValue) {
    if (colorValue == null) return null;

    if (colorValue is String) {
      // Handle hex strings like "#FF0000"
      if (colorValue.startsWith('#')) {
        String hexColor = colorValue.substring(1);

        // Handle shorthand hex like #RGB
        if (hexColor.length == 3) {
          hexColor = hexColor.split('').map((c) => '$c$c').join('');
        }

        // Add alpha channel if missing
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }

        // Parse the hex value
        try {
          return Color(int.parse('0x$hexColor'));
        } catch (e) {
          // Silently handle the error and return null
          return null;
        }
      }

      // Handle named colors - return MaterialColor for standard colors
      // to ensure round-trip conversion works correctly
      switch (colorValue.toLowerCase()) {
        case 'red':
          return Colors.red;
        case 'blue':
          return Colors.blue;
        case 'green':
          return Colors.green;
        case 'yellow':
          return Colors.yellow;
        case 'orange':
          return Colors.orange;
        case 'purple':
          return Colors.purple;
        case 'pink':
          return Colors.pink;
        case 'brown':
          return Colors.brown;
        case 'grey':
        case 'gray':
          return Colors.grey;
        case 'black':
          return Colors.black;
        case 'white':
          return Colors.white;
        case 'amber':
          return Colors.amber;
        case 'cyan':
          return Colors.cyan;
        case 'indigo':
          return Colors.indigo;
        case 'lime':
          return Colors.lime;
        case 'teal':
          return Colors.teal;
        default:
          return null;
      }
    } else if (colorValue is int) {
      // Handle integer color values
      return Color(colorValue);
    }

    return null;
  }

  /// Converts a Flutter Color to a JSON representation
  ///
  /// Returns a hex string (e.g., "#FF0000") or a color name for standard colors
  static String _colorToJson(Color color) {
    // Handle standard colors by name for better readability and test compatibility
    if (color == Colors.red) return 'red';
    if (color == Colors.blue) return 'blue';
    if (color == Colors.green) return 'green';
    if (color == Colors.yellow) return 'yellow';
    if (color == Colors.orange) return 'orange';
    if (color == Colors.purple) return 'purple';
    if (color == Colors.pink) return 'pink';
    if (color == Colors.brown) return 'brown';
    if (color == Colors.grey) return 'grey';
    if (color == Colors.black) return 'black';
    if (color == Colors.white) return 'white';
    if (color == Colors.amber) return 'amber';
    if (color == Colors.cyan) return 'cyan';
    if (color == Colors.indigo) return 'indigo';
    if (color == Colors.lime) return 'lime';
    if (color == Colors.teal) return 'teal';

    // For MaterialColor, preserve the original color name if possible
    if (color is MaterialColor) {
      // We'll keep the original MaterialColor in the round-trip conversion
      // by using a special format that our fromJson method can recognize
      if (color == Colors.red) return 'red';
      if (color == Colors.blue) return 'blue';
      if (color == Colors.green) return 'green';
      if (color == Colors.yellow) return 'yellow';
      if (color == Colors.orange) return 'orange';
      if (color == Colors.purple) return 'purple';
      if (color == Colors.pink) return 'pink';
      if (color == Colors.brown) return 'brown';
      if (color == Colors.grey) return 'grey';
      if (color == Colors.amber) return 'amber';
      if (color == Colors.cyan) return 'cyan';
      if (color == Colors.indigo) return 'indigo';
      if (color == Colors.lime) return 'lime';
      if (color == Colors.teal) return 'teal';

      // If it's a MaterialColor but not one of the standard ones,
      // fall back to the hex representation of the primary value
      color = color.shade500;
    }

    // Convert to RGB format and create a hex string for other colors
    final r = (color.r * 255).round().toRadixString(16).padLeft(2, '0');
    final g = (color.g * 255).round().toRadixString(16).padLeft(2, '0');
    final b = (color.b * 255).round().toRadixString(16).padLeft(2, '0');

    return '#$r$g$b';
  }

  /// Converts a RecordingQuality enum to a JSON string
  static String _qualityToJson(RecordingQuality quality) {
    switch (quality) {
      case RecordingQuality.low:
        return 'low';
      case RecordingQuality.medium:
        return 'medium';
      case RecordingQuality.high:
        return 'high';
    }
  }

  @override
  State<AudioRecordingWidget> createState() => _AudioRecordingWidgetState();
}

/// Recording quality enum
enum RecordingQuality { low, medium, high }

class _AudioRecordingWidgetState extends State<AudioRecordingWidget> {
  final _audioRecorder = AudioRecorder();
  final _audioPlayer = AudioPlayer();

  bool _isRecording = false;
  bool _isPaused = false;
  bool _isPermissionDenied = false;
  bool _isPlaybackMode = false;
  bool _isPlaying = false;
  String? _errorMessage;
  Timer? _recordingTimer;
  int _recordingSeconds = 0;
  String? _recordedAudioPath;

  // For amplitude visualization
  final List<double> _amplitudes = [];
  Timer? _amplitudeTimer;
  double _currentAmplitude = 0;

  // For playback
  Duration _playbackPosition = Duration.zero;
  Duration _playbackDuration = Duration.zero;
  double _playbackSpeed = 1.0;

  // For hover state
  bool _isHovering = false;

  @override
  void initState() {
    super.initState();

    // Set test error message if provided (for testing purposes)
    if (widget.testErrorMessage != null) {
      _errorMessage = widget.testErrorMessage;
    }

    // Set test recording state if provided (for testing purposes)
    if (widget.testInitialRecording) {
      _isRecording = true;
      _recordingSeconds = 10; // Some arbitrary value for testing
    }

    _checkPermission();

    // Set up audio player listeners
    _audioPlayer.onPlayerStateChanged.listen((state) {
      setState(() {
        _isPlaying = state == PlayerState.playing;
      });
    });

    _audioPlayer.onPositionChanged.listen((position) {
      setState(() {
        _playbackPosition = position;
      });
    });

    _audioPlayer.onDurationChanged.listen((duration) {
      setState(() {
        _playbackDuration = duration;
      });
    });
  }

  @override
  void dispose() {
    _recordingTimer?.cancel();
    _amplitudeTimer?.cancel();
    _audioRecorder.dispose();
    _audioPlayer.dispose();
    super.dispose();
  }

  Future<void> _checkPermission() async {
    try {
      if (kIsWeb) {
        // For web, we'll check permission when starting recording
        // as the browser handles permission requests differently
        setState(() {
          _isPermissionDenied = false;
          _errorMessage = null;
        });
      } else {
        // For mobile, request permission explicitly
        final status = await Permission.microphone.request();
        if (status != PermissionStatus.granted) {
          setState(() {
            _isPermissionDenied = true;
            _errorMessage = 'Microphone permission denied';
          });
        }
      }
    } catch (e) {
      setState(() {
        _isPermissionDenied = true;
        _errorMessage = 'Permission check failed: $e';
      });
    }
  }

  Future<void> _startRecording() async {
    try {
      // Create a file path - handle web vs mobile differently
      String filePath;
      if (kIsWeb) {
        // For web, we don't need a file path as the record package handles it
        filePath =
            'audio_recording_${DateTime.now().millisecondsSinceEpoch}.${_getFileExtension()}';
      } else {
        // For mobile, create a temporary file path
        final tempDir = await getTemporaryDirectory();
        final fileName =
            'audio_recording_${DateTime.now().millisecondsSinceEpoch}.${_getFileExtension()}';
        filePath = path.join(tempDir.path, fileName);
      }

      // Configure recording based on quality
      final config = _getRecordConfig();

      // Start recording
      await _audioRecorder.start(config, path: filePath);

      setState(() {
        _isRecording = true;
        _isPaused = false;
        _recordingSeconds = 0;
        _amplitudes.clear();
        _recordedAudioPath = filePath;
        _isPlaybackMode = false;
      });

      // Start timer
      _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        setState(() {
          _recordingSeconds++;
        });

        // Check if max duration reached
        if (widget.maxDuration != null &&
            _recordingSeconds >= widget.maxDuration!) {
          _stopRecording();
        }
      });

      // Start amplitude monitoring for waveform if enabled
      if (widget.showWaveform) {
        _amplitudeTimer = Timer.periodic(const Duration(milliseconds: 100), (
          timer,
        ) async {
          final amplitude = await _audioRecorder.getAmplitude();
          setState(() {
            _currentAmplitude = amplitude.current;
            _amplitudes.add(_currentAmplitude);
            // Keep a reasonable number of amplitude values
            if (_amplitudes.length > 100) {
              _amplitudes.removeAt(0);
            }
          });
        });
      }

      // Call the callback if provided
      widget.onRecordingStarted?.call();
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to start recording: $e';
        _isRecording = false;
        _isPaused = false;
      });

      // Log the error for debugging
      debugPrint('Audio recording error: $e');

      // If it's a web-specific error, provide more helpful message
      if (kIsWeb && e.toString().contains('path_provider')) {
        setState(() {
          _errorMessage =
              'Audio recording is not fully supported in web browsers yet. Please try on a mobile device.';
        });
      }
    }
  }

  Future<void> _pauseRecording() async {
    if (!_isRecording || _isPaused) {
      return;
    }

    try {
      await _audioRecorder.pause();

      setState(() {
        _isPaused = true;
      });

      _recordingTimer?.cancel();
      _amplitudeTimer?.cancel();

      // Call the callback if provided
      widget.onRecordingPaused?.call();
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to pause recording: $e';
      });
    }
  }

  Future<void> _resumeRecording() async {
    if (!_isRecording || !_isPaused) {
      return;
    }

    try {
      await _audioRecorder.resume();

      setState(() {
        _isPaused = false;
      });

      _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        setState(() {
          _recordingSeconds++;
        });

        // Check if max duration reached
        if (widget.maxDuration != null &&
            _recordingSeconds >= widget.maxDuration!) {
          _stopRecording();
        }
      });

      // Resume amplitude monitoring for waveform if enabled
      if (widget.showWaveform) {
        _amplitudeTimer = Timer.periodic(const Duration(milliseconds: 100), (
          timer,
        ) async {
          final amplitude = await _audioRecorder.getAmplitude();
          setState(() {
            _currentAmplitude = amplitude.current;
            _amplitudes.add(_currentAmplitude);
            // Keep a reasonable number of amplitude values
            if (_amplitudes.length > 100) {
              _amplitudes.removeAt(0);
            }
          });
        });
      }

      // Call the callback if provided
      widget.onRecordingResumed?.call();
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to resume recording: $e';
      });
    }
  }

  Future<void> _stopRecording() async {
    if (!_isRecording) {
      return;
    }

    try {
      _recordingTimer?.cancel();
      _amplitudeTimer?.cancel();

      // Stop recording and get the file path
      final path = await _audioRecorder.stop();

      setState(() {
        _isRecording = false;
        _isPaused = false;
        _recordedAudioPath = path;
        _isPlaybackMode = widget.showPlayback;
      });

      // Call the callback if provided
      if (widget.onRecordingStopped != null) {
        widget.onRecordingStopped!();
      }

      if (widget.onAudioRecorded != null && path != null) {
        widget.onAudioRecorded!(path);
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to stop recording: $e';
        _isRecording = false;
        _isPaused = false;
      });
    }
  }

  Future<void> _playRecording() async {
    if (_recordedAudioPath == null) {
      return;
    }

    try {
      if (_isPlaying) {
        await _audioPlayer.pause();
      } else {
        await _audioPlayer.play(DeviceFileSource(_recordedAudioPath!));
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to play recording: $e';
      });
    }
  }

  Future<void> _saveRecording() async {
    if (_recordedAudioPath == null) {
      return;
    }

    try {
      // In a real implementation, you would save the file to a permanent location
      // For this example, we'll just call the callback with the current path
      if (widget.onAudioSaved != null) {
        widget.onAudioSaved!(_recordedAudioPath!);
      }

      // Show a success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Recording saved successfully')),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to save recording: $e';
      });
    }
  }

  void _resetRecording() {
    setState(() {
      _recordedAudioPath = null;
      _isPlaybackMode = false;
      _playbackPosition = Duration.zero;
      _playbackDuration = Duration.zero;
    });

    _audioPlayer.stop();
  }

  RecordConfig _getRecordConfig() {
    // Set bit rate based on quality
    int bitRate;
    int sampleRate;

    switch (widget.quality) {
      case RecordingQuality.low:
        bitRate = 64000; // 64 kbps
        sampleRate = 16000; // 16 kHz
        break;
      case RecordingQuality.medium:
        bitRate = 128000; // 128 kbps
        sampleRate = 44100; // 44.1 kHz
        break;
      case RecordingQuality.high:
        bitRate = 256000; // 256 kbps
        sampleRate = 48000; // 48 kHz
        break;
    }

    return RecordConfig(
      encoder: AudioEncoder.aacLc, // AAC is widely supported
      bitRate: bitRate,
      sampleRate: sampleRate,
      numChannels: widget.numChannels,
      autoGain: widget.autoGainControlEnabled,
      echoCancel: widget.echoCancellationEnabled,
      noiseSuppress: widget.noiseSuppressionEnabled,
    );
  }

  String _getFileExtension() {
    // Return appropriate file extension based on encoder
    return 'm4a'; // For AAC
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  String _formatDurationFromSeconds(int seconds) {
    final duration = Duration(seconds: seconds);
    return _formatDuration(duration);
  }

  Widget _buildErrorMessage() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text(
              _errorMessage ?? 'An error occurred',
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _checkPermission,
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionDeniedMessage() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.mic_off, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            const Text(
              'Microphone permission is required to record audio',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _checkPermission,
              child: const Text('Grant Permission'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWaveform() {
    if (!widget.showWaveform) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 100,
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: CustomPaint(
        painter: WaveformPainter(
          amplitudes: _amplitudes,
          color: widget.waveformColor,
        ),
        size: const Size(double.infinity, 100),
      ),
    );
  }

  Widget _buildTimer() {
    if (!widget.showTimer) {
      return const SizedBox.shrink();
    }

    return Positioned(
      top: 16,
      right: 16,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.black54,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.timer, color: Colors.white, size: 16),
            const SizedBox(width: 4),
            Text(
              _formatDurationFromSeconds(_recordingSeconds),
              style: const TextStyle(color: Colors.white, fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildControls() {
    if (!widget.showControls) {
      return const SizedBox.shrink();
    }

    // Controls are rendered with their own colors

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Switch camera button (placeholder for layout consistency)
          const SizedBox(width: 48),

          // Record/stop button
          FloatingActionButton.extended(
            onPressed: _isRecording ? _stopRecording : _startRecording,
            backgroundColor:
                _isRecording
                    ? widget.stopButtonColor
                    : widget.recordButtonColor,
            icon: Icon(
              _isRecording ? Icons.stop : Icons.mic,
              color: Colors.white,
            ),
            label: Text(
              _isRecording
                  ? (widget.stopButtonText ?? 'Stop')
                  : (widget.recordButtonText ?? 'Record'),
              style: const TextStyle(color: Colors.white),
            ),
          ),

          // Pause/resume button (only shown when recording)
          if (_isRecording)
            IconButton(
              icon: Icon(_isPaused ? Icons.play_arrow : Icons.pause),
              onPressed: _isPaused ? _resumeRecording : _pauseRecording,
              tooltip:
                  _isPaused
                      ? (widget.resumeButtonText ?? 'Resume')
                      : (widget.pauseButtonText ?? 'Pause'),
              color:
                  _isPaused
                      ? widget.resumeButtonColor
                      : widget.pauseButtonColor,
            )
          else
            // Placeholder to maintain layout
            const SizedBox(width: 48),
        ],
      ),
    );
  }

  double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 16.0; // Large
    } else if (screenWidth >= 1280) {
      return 14.0; // Medium
    } else if (screenWidth >= 768) {
      return 12.0; // Small
    } else {
      return 12.0; // Default for very small screens
    }
  }

  Widget _buildPlaybackControls() {
    return Column(
      children: [
        // Playback slider
        Slider(
          value: _playbackPosition.inMilliseconds.toDouble(),
          max: _playbackDuration.inMilliseconds.toDouble(),
          onChanged: (value) {
            _audioPlayer.seek(Duration(milliseconds: value.toInt()));
          },
        ),

        // Duration labels
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(_formatDuration(_playbackPosition)),
              Text(_formatDuration(_playbackDuration)),
            ],
          ),
        ),

        // Playback controls
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            IconButton(
              icon: const Icon(Icons.replay_10),
              onPressed: () {
                _audioPlayer.seek(
                  _playbackPosition - const Duration(seconds: 10),
                );
              },
            ),
            FloatingActionButton(
              onPressed: _playRecording,
              child: Icon(_isPlaying ? Icons.pause : Icons.play_arrow),
            ),
            IconButton(
              icon: const Icon(Icons.forward_10),
              onPressed: () {
                _audioPlayer.seek(
                  _playbackPosition + const Duration(seconds: 10),
                );
              },
            ),
          ],
        ),

        // Action buttons
        Padding(
          padding: const EdgeInsets.only(top: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton.icon(
                onPressed: _resetRecording,
                icon: const Icon(Icons.replay),
                label: const Text('Record Again'),
              ),
              const SizedBox(width: 16),
              if (widget.showSaveButton)
                ElevatedButton.icon(
                  onPressed: _saveRecording,
                  icon: const Icon(Icons.save),
                  label: Text(widget.saveButtonText ?? 'Save'),
                ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    // Show error message if there's an error
    if (_errorMessage != null) {
      return _buildErrorMessage();
    }

    // Show permission denied message if permissions are denied
    if (_isPermissionDenied) {
      return _buildPermissionDeniedMessage();
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (widget.showLabel)
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              widget.labelText ?? 'Audio Recording',
              style: TextStyle(
                fontSize: _getResponsiveFontSize(context),
                // 18,
                fontWeight: FontWeight.w500,
                fontFamily: 'Inter',
              ),
            ),
          ),
        const SizedBox(height: 2),

        // Main content based on state
        if (_isPlaybackMode)
          _buildCompactPlaybackControls()
        else if (_isRecording)
          _buildRecordingControls()
        else
          _buildDefaultMicrophoneIcon(),
      ],
    );
  }

  /// Builds the default microphone icon with hover effect using SVG images
  Widget _buildDefaultMicrophoneIcon() {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovering = true),
      onExit: (_) => setState(() => _isHovering = false),
      child: GestureDetector(
        onTap: _startRecording,
        child: Container(
          // width: 56,
          // height: 56,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.transparent,
          ),
          child: SvgPicture.asset(
            _isHovering
                ? 'assets/images/audio-recording-onHover.svg'
                : 'assets/images/audio-recording.svg',
            package: 'ui_controls_library',
            // width: 56,
            // height: 56,
            fit: BoxFit.contain,
          ),
        ),
      ),
    );
  }

  double _getResponsiveHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 56.0; // Extra Large (>1920px)
    } else if (screenWidth >= 1440) {
      return 48.0; // Large (1440-1920px)
    } else if (screenWidth >= 1280) {
      return 40.0; // Medium (1280-1366px)
    } else if (screenWidth >= 768) {
      return 32.0; // Small (768-1024px)
    } else {
      return 32.0; // Default for very small screens
    }
  }

  EdgeInsets _getResponsivePadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return const EdgeInsets.symmetric(
        horizontal: 12.0,
        vertical: 4.0,
      ); // Extra Large
    } else if (screenWidth >= 1440) {
      return const EdgeInsets.symmetric(
        horizontal: 10.0,
        vertical: 3.0,
      ); // Large
    } else if (screenWidth >= 1280) {
      return const EdgeInsets.symmetric(
        horizontal: 8.0,
        vertical: 2.0,
      ); // Medium
    } else if (screenWidth >= 768) {
      return const EdgeInsets.symmetric(
        horizontal: 6.0,
        vertical: 1.0,
      ); // Small
    } else {
      return const EdgeInsets.symmetric(
        horizontal: 6.0,
        vertical: 1.0,
      ); // Default for very small screens
    }
  }

  /// Builds the recording controls with waveform and timer
  Widget _buildRecordingControls() {
    return Container(
      width: 295,
      height: _getResponsiveHeight(context),
      padding: _getResponsivePadding(context),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        children: [
          // Stop button
          GestureDetector(
            onTap: _stopRecording,
            child: SvgPicture.asset(
              'assets/images/recording.svg',

              package: 'ui_controls_library',
              // width: 56,
              // height: 56,
              fit: BoxFit.contain,
            ),
            // Container(
            //   width: 24,
            //   height: 24,
            //   decoration: const BoxDecoration(
            //     color: Colors.red,
            //     shape: BoxShape.circle,
            //   ),
            //   child: const Icon(
            //     Icons.stop,
            //     color: Colors.white,
            //     size: 16,
            //   ),
            // ),
          ),

          const SizedBox(width: 12),

          // Waveform visualization
          Expanded(
            child: SizedBox(
              height: 20,
              child: CustomPaint(
                painter: WaveformPainter(
                  amplitudes: _amplitudes,
                  color: Colors.grey.shade700,
                ),
                size: const Size(double.infinity, 20),
              ),
            ),
          ),

          const SizedBox(width: 12),

          // Timer
          Text(
            _formatDurationFromSeconds(_recordingSeconds),
            style: const TextStyle(fontSize: 12, color: Colors.black87),
          ),

          const SizedBox(width: 8),

          // Volume icon
          Icon(Icons.volume_up, size: 16, color: Colors.grey.shade600),
        ],
      ),
    );
  }

  /// Builds compact playback controls similar to audio player
  Widget _buildCompactPlaybackControls() {
    return Container(
      width: 295,
      height: _getResponsiveHeight(context),
      padding: _getResponsivePadding(context),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        children: [
          // Play/Pause button
          GestureDetector(
            onTap: _playRecording,
            child: Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: _isPlaying ? Colors.black : Colors.transparent,
                shape: BoxShape.circle,
              ),
              child: Icon(
                _isPlaying ? Icons.pause : Icons.play_arrow,
                color: _isPlaying ? Colors.white : Colors.black,
                size: 20,
              ),
            ),
          ),

          const SizedBox(width: 8),

          // Progress bar
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SliderTheme(
                  data: SliderThemeData(
                    trackHeight: 2,
                    thumbShape: const RoundSliderThumbShape(
                      enabledThumbRadius: 4,
                    ),
                    overlayShape: const RoundSliderOverlayShape(
                      overlayRadius: 10,
                    ),
                    activeTrackColor: Colors.blue,
                    inactiveTrackColor: Colors.grey.shade300,
                    thumbColor: Colors.blue,
                  ),
                  child: Slider(
                    value: _playbackPosition.inMilliseconds.toDouble(),
                    max:
                        _playbackDuration.inMilliseconds > 0
                            ? _playbackDuration.inMilliseconds.toDouble()
                            : 1.0,
                    onChanged: (value) {
                      _audioPlayer.seek(Duration(milliseconds: value.toInt()));
                    },
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(width: 8),

          // Duration text
          Text(
            '${_formatDuration(_playbackPosition)} / ${_formatDuration(_playbackDuration)}',
            style: const TextStyle(fontSize: 12, color: Colors.black87),
          ),

          const SizedBox(width: 8),

          // Playback speed indicator
          if (_playbackSpeed != 1.0)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.blue.shade100,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                '${_playbackSpeed}x',
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.blue.shade700,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),

          if (_playbackSpeed != 1.0) const SizedBox(width: 8),

          // Volume icon
          Icon(Icons.volume_up, size: 16, color: Colors.grey.shade600),

          const SizedBox(width: 8),

          // Divider
          Container(width: 1, height: 16, color: Colors.grey.shade400),

          const SizedBox(width: 8),

          // Options menu
          GestureDetector(
            onTap: () => _showRecordingOptionsMenu(context),
            child: Icon(Icons.more_vert, size: 20, color: Colors.grey.shade600),
          ),
        ],
      ),
    );
  }

  /// Shows options menu for recorded audio
  void _showRecordingOptionsMenu(BuildContext context) async {
    final RenderBox button = context.findRenderObject() as RenderBox;
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;

    final buttonPosition = button.localToGlobal(Offset.zero, ancestor: overlay);

    final RelativeRect position = RelativeRect.fromLTRB(
      buttonPosition.dx + 100,
      buttonPosition.dy + 30,
      buttonPosition.dx + 450,
      buttonPosition.dy + 130,
    );

    final String? result = await showMenu<String>(
      context: context,
      position: position,
      color: Colors.white,
      constraints: const BoxConstraints(
        minWidth: 185,
        maxWidth: 185,
        minHeight: 96,
        maxHeight: 96,
      ),
      items: [
        const PopupMenuItem<String>(
          value: 'download',
          height: 36,
          child: Row(
            children: [
              Icon(Icons.download, size: 16),
              SizedBox(width: 8),
              Text(
                'Download',
                style: TextStyle(fontSize: 14, fontFamily: 'Inter'),
              ),
            ],
          ),
        ),
        const PopupMenuItem<String>(
          value: 'playback_speed',
          height: 36,
          child: Row(
            children: [
              Icon(Icons.speed, size: 16),
              SizedBox(width: 8),
              Text(
                'Playback Speed',
                style: TextStyle(fontSize: 14, fontFamily: 'Inter'),
              ),
            ],
          ),
        ),
        const PopupMenuItem<String>(
          value: 'delete',
          height: 36,
          child: Row(
            children: [
              Icon(Icons.delete, size: 16),
              SizedBox(width: 8),
              Text(
                'Delete',
                style: TextStyle(fontSize: 14, fontFamily: 'Inter'),
              ),
            ],
          ),
        ),
      ],
    );

    if (!mounted) return;

    switch (result) {
      case 'download':
        _downloadRecording();
        break;
      case 'playback_speed':
        if (mounted) _showPlaybackSpeedDialog(context);
        break;
      case 'delete':
        _deleteRecording();
        break;
    }
  }

  Future<void> _downloadRecording() async {
    if (_recordedAudioPath == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No recording to download'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      if (kIsWeb) {
        await _downloadRecordingWeb();
      } else {
        await _downloadRecordingMobile();
      }
    } catch (e) {
      debugPrint('Download error: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Download failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _downloadRecordingWeb() async {
    try {
      debugPrint('AudioRecordingWidget: Starting direct web download');

      // Extract filename from path or create default
      final fileName =
          'audio_recording_${DateTime.now().millisecondsSinceEpoch}.${_getFileExtension()}';

      // Use platform-specific download functionality
      downloadAudioFile(_recordedAudioPath!);

      debugPrint(
        'AudioRecordingWidget: Direct download initiated for: $fileName',
      );

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Download started: $fileName'),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('AudioRecordingWidget: Direct download failed: $e');

      // Fallback to opening in new tab
      try {
        openInNewTab(_recordedAudioPath!);
        debugPrint('AudioRecordingWidget: Opened in new tab as fallback');

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Audio opened in new tab for download'),
              duration: Duration(seconds: 2),
              backgroundColor: Colors.orange,
            ),
          );
        }
      } catch (e2) {
        debugPrint('AudioRecordingWidget: New tab fallback also failed: $e2');
        throw Exception('Web download failed: $e');
      }
    }
  }

  Future<void> _downloadRecordingMobile() async {
    try {
      debugPrint('AudioRecordingWidget: Starting mobile download');

      // For mobile, try to trigger a download or save to downloads folder
      final fileName = path.basename(_recordedAudioPath!);

      // Try to copy the file to a more accessible location (like Downloads)
      // This is a simplified implementation - in a real app you might use
      // packages like path_provider to get the Downloads directory

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Audio downloaded: $fileName'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
            action: SnackBarAction(
              label: 'Open',
              textColor: Colors.white,
              onPressed: () {
                // Try to open the file with the default app
                debugPrint('Opening audio file: $_recordedAudioPath');
                // Future enhancement: Use url_launcher to open the file
              },
            ),
          ),
        );
      }

      debugPrint(
        'AudioRecordingWidget: Mobile download completed for: $fileName',
      );
    } catch (e) {
      throw Exception('Mobile download failed: $e');
    }
  }

  void _deleteRecording() {
    setState(() {
      _recordedAudioPath = null;
      _isPlaybackMode = false;
      _playbackPosition = Duration.zero;
      _playbackDuration = Duration.zero;
    });
    _audioPlayer.stop();
  }

  void _showPlaybackSpeedDialog(BuildContext context) {
    showDialog<double>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'Playback Speed',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children:
                [0.5, 0.75, 1.0, 1.25, 1.5, 2.0].map((speed) {
                  return RadioListTile<double>(
                    title: Text(
                      '${speed}x',
                      style: const TextStyle(fontSize: 16),
                    ),
                    subtitle: Text(
                      _getSpeedDescription(speed),
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                    value: speed,
                    groupValue: _playbackSpeed,
                    onChanged: (double? value) {
                      if (value != null) {
                        Navigator.of(context).pop(value);
                      }
                    },
                    activeColor: Colors.blue,
                  );
                }).toList(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    ).then((double? speed) {
      if (speed != null && speed != _playbackSpeed) {
        _changePlaybackSpeed(speed);
      }
    });
  }

  String _getSpeedDescription(double speed) {
    switch (speed) {
      case 0.5:
        return 'Half speed';
      case 0.75:
        return 'Slower';
      case 1.0:
        return 'Normal';
      case 1.25:
        return 'Faster';
      case 1.5:
        return 'Much faster';
      case 2.0:
        return 'Double speed';
      default:
        return '${speed}x speed';
    }
  }

  Future<void> _changePlaybackSpeed(double speed) async {
    try {
      debugPrint('AudioRecordingWidget: Changing playback speed to ${speed}x');

      // Set the playback rate on the audio player
      await _audioPlayer.setPlaybackRate(speed);

      setState(() {
        _playbackSpeed = speed;
      });

      // Show feedback to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Playback speed set to ${speed}x'),
            duration: const Duration(seconds: 1),
            backgroundColor: Colors.blue,
          ),
        );
      }

      debugPrint(
        'AudioRecordingWidget: Playback speed successfully changed to ${speed}x',
      );
    } catch (e) {
      debugPrint('AudioRecordingWidget: Failed to change playback speed: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to change playback speed: $e'),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

/// Custom painter for drawing the waveform visualization
class WaveformPainter extends CustomPainter {
  final List<double> amplitudes;
  final Color color;

  WaveformPainter({required this.amplitudes, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color
          ..style = PaintingStyle.fill;

    final width = size.width;
    final height = size.height;
    final centerY = height / 2;

    if (amplitudes.isEmpty) {
      // Draw a few static bars if no amplitudes
      _drawStaticBars(canvas, size, paint);
      return;
    }

    // Calculate bar dimensions
    const barSpacing = 2.0;
    final totalBars =
        (width / (3 + barSpacing)).floor(); // 3px bar width + 2px spacing
    final barWidth = 3.0;
    final actualSpacing = (width - (totalBars * barWidth)) / (totalBars - 1);

    // Use the most recent amplitudes or repeat if we have fewer
    final displayAmplitudes = _prepareAmplitudes(totalBars);

    // Draw individual amplitude bars
    for (int i = 0; i < totalBars; i++) {
      final x = i * (barWidth + actualSpacing);

      // Get amplitude for this bar (normalize to 0-1 range)
      final amplitudeIndex =
          i < displayAmplitudes.length ? i : displayAmplitudes.length - 1;
      var normalizedAmplitude =
          displayAmplitudes.isNotEmpty
              ? min(max(displayAmplitudes[amplitudeIndex] / 100, 0.1), 1.0)
              : 0.1;

      // Add some randomness for more natural look when amplitude is low
      if (normalizedAmplitude < 0.3) {
        normalizedAmplitude += (0.1 + (i % 3) * 0.05);
        normalizedAmplitude = min(normalizedAmplitude, 0.4);
      }

      // Calculate bar height (minimum height for visibility)
      final barHeight = max(normalizedAmplitude * (height * 0.8), 4.0);

      // Draw the bar centered vertically
      final rect = Rect.fromLTWH(
        x,
        centerY - (barHeight / 2),
        barWidth,
        barHeight,
      );

      canvas.drawRRect(
        RRect.fromRectAndRadius(rect, const Radius.circular(1.5)),
        paint,
      );
    }
  }

  /// Prepare amplitudes for display, ensuring we have enough data
  List<double> _prepareAmplitudes(int requiredCount) {
    if (amplitudes.isEmpty) {
      return List.generate(requiredCount, (index) => 10.0 + (index % 5) * 5.0);
    }

    if (amplitudes.length >= requiredCount) {
      // Take the most recent amplitudes
      return amplitudes.sublist(amplitudes.length - requiredCount);
    } else {
      // Repeat the pattern to fill the required count
      final result = <double>[];
      for (int i = 0; i < requiredCount; i++) {
        result.add(amplitudes[i % amplitudes.length]);
      }
      return result;
    }
  }

  /// Draw static bars when no amplitude data is available
  void _drawStaticBars(Canvas canvas, Size size, Paint paint) {
    const barSpacing = 2.0;
    const barWidth = 3.0;
    final totalBars = (size.width / (barWidth + barSpacing)).floor();
    final centerY = size.height / 2;

    // Create a pattern of static bars with varying heights
    final staticHeights = [0.2, 0.4, 0.6, 0.3, 0.5, 0.7, 0.4, 0.3];

    for (int i = 0; i < totalBars; i++) {
      final x = i * (barWidth + barSpacing);
      final heightRatio = staticHeights[i % staticHeights.length];
      final barHeight = max(heightRatio * (size.height * 0.6), 4.0);

      final rect = Rect.fromLTWH(
        x,
        centerY - (barHeight / 2),
        barWidth,
        barHeight,
      );

      canvas.drawRRect(
        RRect.fromRectAndRadius(rect, const Radius.circular(1.5)),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant WaveformPainter oldDelegate) {
    return oldDelegate.amplitudes != amplitudes || oldDelegate.color != color;
  }
}
