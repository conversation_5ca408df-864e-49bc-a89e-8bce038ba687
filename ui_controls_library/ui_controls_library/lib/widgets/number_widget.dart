import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// A widget for inputting and displaying numeric values (both integers and decimals).
///
/// This widget provides a text field for entering numeric values with
/// optional features like increment/decrement buttons, validation,
/// formatting, and styling options.
class NumberWidget extends StatefulWidget {
  /// The initial value of the number input.
  final num? initialValue;

  /// The minimum allowed value.
  final num? minValue;

  /// The maximum allowed value.
  final num? maxValue;

  /// The step value for increment and decrement.
  final num stepValue;

  /// Whether to show increment and decrement buttons.
  final bool showButtons;

  /// The size of the increment and decrement buttons.
  final double buttonSize;

  /// The icon for the increment button.
  final IconData incrementIcon;

  /// The icon for the decrement button.
  final IconData decrementIcon;

  /// The color of the increment and decrement buttons.
  final Color? buttonColor;

  /// The color of the icons in the increment and decrement buttons.
  final Color? buttonIconColor;

  /// The width of the widget.
  final double? width;

  /// The height of the widget.
  final double? height;

  /// The text style for the input field.
  final TextStyle? textStyle;

  /// The decoration for the input field.
  final InputDecoration? decoration;

  /// The prefix text to display before the input.
  final String? prefix;

  /// The suffix text to display after the input.
  final String? suffix;

  /// The label text to display above the input.
  final String? label;

  /// The style for the label text.
  final TextStyle? labelStyle;

  /// The hint text to display when the input is empty.
  final String? hint;

  /// The error text to display when the input is invalid.
  final String? errorText;

  /// Whether the input is enabled.
  final bool enabled;

  /// Whether the input is read-only.
  final bool readOnly;

  /// The text alignment for the input.
  final TextAlign textAlign;

  /// The border radius for the input field.
  final double borderRadius;

  /// The border color for the input field.
  final Color? borderColor;

  /// The background color for the input field.
  final Color? backgroundColor;

  /// The padding for the input field.
  final EdgeInsetsGeometry padding;

  /// The margin for the widget.
  final EdgeInsetsGeometry margin;

  /// The layout direction of the widget (horizontal or vertical).
  final Axis direction;

  /// Whether to show a thousands separator.
  final bool useThousandsSeparator;

  /// The character to use as a thousands separator.
  final String thousandsSeparator;

  /// Whether to allow negative values.
  final bool allowNegative;

  /// Whether to show a clear button.
  final bool showClearButton;

  /// The number of decimal places to display.
  final int decimalPlaces;

  /// Whether to allow decimal values.
  final bool allowDecimal;

  /// The callback that is called when the value changes.
  final ValueChanged<num?>? onChanged;

  /// The callback that is called when the user submits the value.
  final ValueChanged<num?>? onSubmitted;

  /// Callback for mouse hover
  ///
  /// This function is called when the mouse pointer enters or exits the widget.
  /// The parameter is true when the mouse enters and false when it exits.
  final void Function(bool)? onHover;

  /// Callback for keyboard focus
  ///
  /// This function is called when the widget gains or loses keyboard focus.
  /// The parameter is true when focus is gained and false when it is lost.
  final void Function(bool)? onFocus;

  /// Color when the widget is hovered
  final Color? hoverColor;

  /// Color when the widget is focused
  final Color? focusColor;

  /// Tooltip text to display on hover
  final String? tooltip;

  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to enable haptic/audio feedback
  ///
  /// If true, the widget will provide haptic and/or audio feedback when interacted with.
  final bool enableFeedback;

  /// Callback for double tap gesture
  ///
  /// This function is called when the widget is double-tapped.
  final VoidCallback? onDoubleTap;

  /// Callback for long press gesture
  ///
  /// This function is called when the widget is long-pressed.
  final VoidCallback? onLongPress;

  /// JSON data for additional configuration
  ///
  /// This can include any custom configuration data for the number widget.
  final dynamic jsonConfig;

  /// Whether to use custom validation rules from JSON config
  final bool useJsonValidation;

  /// Whether to use custom styling from JSON config
  final bool useJsonStyling;

  /// Whether to use custom behavior from JSON config
  final bool useJsonBehavior;

  /// Whether to use custom formatting from JSON config
  final bool useJsonFormatting;

  /// Custom validation message from JSON config
  final String? jsonValidationMessage;

  /// Custom number format from JSON config
  final String? jsonNumberFormat;

  /// Custom unit type from JSON config (e.g., "currency", "percentage", "measurement")
  final String? jsonUnitType;

  /// Creates a number widget.
  const NumberWidget({
    super.key,
    this.initialValue,
    this.minValue,
    this.maxValue,
    this.stepValue = 1,
    this.showButtons = true,
    this.buttonSize = 36.0,
    this.incrementIcon = Icons.add,
    this.decrementIcon = Icons.remove,
    this.buttonColor,
    this.buttonIconColor,
    this.width,
    this.height,
    this.textStyle,
    this.decoration,
    this.prefix,
    this.suffix,
    this.label,
    this.labelStyle,
    this.hint,
    this.errorText,
    this.enabled = true,
    this.readOnly = false,
    this.textAlign = TextAlign.center,
    this.borderRadius = 4.0,
    this.borderColor,
    this.backgroundColor,
    this.padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
    this.margin = EdgeInsets.zero,
    this.direction = Axis.horizontal,
    this.useThousandsSeparator = false,
    this.thousandsSeparator = ',',
    this.allowNegative = true,
    this.showClearButton = false,
    this.decimalPlaces = 2,
    this.allowDecimal = true,
    this.onChanged,
    this.onSubmitted,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.hoverColor,
    this.focusColor,
    this.tooltip,
    this.semanticsLabel,
    this.enableFeedback = true,
    this.onDoubleTap,
    this.onLongPress,
    // JSON configuration properties
    this.jsonConfig,
    this.useJsonValidation = false,
    this.useJsonStyling = false,
    this.useJsonBehavior = false,
    this.useJsonFormatting = false,
    this.jsonValidationMessage,
    this.jsonNumberFormat,
    this.jsonUnitType,
  });

  /// Creates a NumberWidget from a JSON map
  ///
  /// This factory constructor allows creating a NumberWidget from a JSON map,
  /// making it easy to configure the widget from dynamic data.
  factory NumberWidget.fromJson(Map<String, dynamic> json) {
    // Parse basic numeric properties
    num? initialValue;
    if (json['initialValue'] != null) {
      if (json['initialValue'] is num) {
        initialValue = json['initialValue'] as num;
      } else {
        initialValue = num.tryParse(json['initialValue'].toString());
      }
    }

    num? minValue;
    if (json['minValue'] != null) {
      if (json['minValue'] is num) {
        minValue = json['minValue'] as num;
      } else {
        minValue = num.tryParse(json['minValue'].toString());
      }
    }

    num? maxValue;
    if (json['maxValue'] != null) {
      if (json['maxValue'] is num) {
        maxValue = json['maxValue'] as num;
      } else {
        maxValue = num.tryParse(json['maxValue'].toString());
      }
    }

    num stepValue = 1;
    if (json['stepValue'] != null) {
      if (json['stepValue'] is num) {
        stepValue = json['stepValue'] as num;
      } else {
        stepValue = num.tryParse(json['stepValue'].toString()) ?? 1;
      }
    }

    // Parse boolean properties
    final showButtons = json['showButtons'] != false;
    final enabled = json['enabled'] != false;
    final readOnly = json['readOnly'] == true;
    final useThousandsSeparator = json['useThousandsSeparator'] == true;
    final allowNegative = json['allowNegative'] != false;
    final showClearButton = json['showClearButton'] == true;
    final allowDecimal = json['allowDecimal'] != false;
    final enableFeedback = json['enableFeedback'] != false;

    // Parse JSON configuration properties
    final useJsonValidation = json['useJsonValidation'] == true;
    final useJsonStyling = json['useJsonStyling'] == true;
    final useJsonBehavior = json['useJsonBehavior'] == true;
    final useJsonFormatting = json['useJsonFormatting'] == true;

    // Parse numeric values
    final buttonSize =
        json['buttonSize'] != null
            ? double.tryParse(json['buttonSize'].toString()) ?? 36.0
            : 36.0;
    final borderRadius =
        json['borderRadius'] != null
            ? double.tryParse(json['borderRadius'].toString()) ?? 4.0
            : 4.0;
    final decimalPlaces =
        json['decimalPlaces'] != null
            ? int.tryParse(json['decimalPlaces'].toString()) ?? 2
            : 2;

    // Parse width and height
    double? width;
    if (json['width'] != null) {
      if (json['width'].toString().toLowerCase() == 'infinity' ||
          json['width'].toString().toLowerCase() == 'double.infinity') {
        width = double.infinity;
      } else {
        width = double.tryParse(json['width'].toString());
      }
    }

    double? height;
    if (json['height'] != null) {
      height = double.tryParse(json['height'].toString());
    }

    // Parse color properties
    Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;

      if (colorValue is String) {
        // Try to parse as hex
        if (colorValue.startsWith('#')) {
          try {
            final hexValue = int.parse(colorValue.substring(1), radix: 16);
            if (colorValue.length == 7) {
              // #RRGGBB format
              return Color(0xFF000000 | hexValue);
            } else if (colorValue.length == 9) {
              // #AARRGGBB format
              return Color(hexValue);
            }
          } catch (e) {
            // Ignore parsing errors and return null
          }
        }

        // Try to match color names
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Color(0xFF0058FF);
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          default:
            return null;
        }
      } else if (colorValue is int) {
        return Color(colorValue);
      }

      return null;
    }

    final buttonColor = parseColor(json['buttonColor']);
    final buttonIconColor = parseColor(json['buttonIconColor']);
    final borderColor = parseColor(json['borderColor']);
    final backgroundColor = parseColor(json['backgroundColor']);
    final hoverColor = parseColor(json['hoverColor']);
    final focusColor = parseColor(json['focusColor']);

    // Parse text style
    TextStyle? textStyle;
    if (json['textStyle'] != null && json['textStyle'] is Map) {
      final textStyleMap = json['textStyle'] as Map<String, dynamic>;

      final fontSize =
          textStyleMap['fontSize'] != null
              ? double.tryParse(textStyleMap['fontSize'].toString())
              : null;

      final fontWeight =
          textStyleMap['fontWeight'] != null
              ? (textStyleMap['fontWeight'].toString().toLowerCase() == 'bold'
                  ? FontWeight.bold
                  : (textStyleMap['fontWeight'].toString().toLowerCase() ==
                          'light'
                      ? FontWeight.w300
                      : FontWeight.normal))
              : null;

      final color = parseColor(textStyleMap['color']);

      textStyle = TextStyle(
        fontSize: fontSize,
        fontWeight: fontWeight,
        color: color,
      );
    }

    // Parse label style
    TextStyle? labelStyle;
    if (json['labelStyle'] != null && json['labelStyle'] is Map) {
      final labelStyleMap = json['labelStyle'] as Map<String, dynamic>;

      final fontSize =
          labelStyleMap['fontSize'] != null
              ? double.tryParse(labelStyleMap['fontSize'].toString())
              : null;

      final fontWeight =
          labelStyleMap['fontWeight'] != null
              ? (labelStyleMap['fontWeight'].toString().toLowerCase() == 'bold'
                  ? FontWeight.bold
                  : (labelStyleMap['fontWeight'].toString().toLowerCase() ==
                          'light'
                      ? FontWeight.w300
                      : FontWeight.normal))
              : null;

      final color = parseColor(labelStyleMap['color']);

      labelStyle = TextStyle(
        fontSize: fontSize,
        fontWeight: fontWeight,
        color: color,
      );
    }

    // Parse text alignment
    TextAlign textAlign = TextAlign.center;
    if (json['textAlign'] != null) {
      final alignStr = json['textAlign'].toString().toLowerCase();
      switch (alignStr) {
        case 'start':
          textAlign = TextAlign.start;
          break;
        case 'end':
          textAlign = TextAlign.end;
          break;
        case 'left':
          textAlign = TextAlign.left;
          break;
        case 'right':
          textAlign = TextAlign.right;
          break;
        case 'justify':
          textAlign = TextAlign.justify;
          break;
        case 'center':
        default:
          textAlign = TextAlign.center;
          break;
      }
    }

    // Parse direction
    Axis direction = Axis.horizontal;
    if (json['direction'] != null) {
      final dirStr = json['direction'].toString().toLowerCase();
      if (dirStr == 'vertical') {
        direction = Axis.vertical;
      }
    }

    // Parse icons
    IconData parseIconData(dynamic iconValue, IconData defaultIcon) {
      if (iconValue == null) return defaultIcon;

      if (iconValue is int) {
        // return IconData(iconValue, fontFamily: 'MaterialIcons');
        return defaultIcon;
      } else if (iconValue is String) {
        switch (iconValue.toLowerCase()) {
          case 'add':
            return Icons.add;
          case 'remove':
            return Icons.remove;
          case 'plus':
            return Icons.add;
          case 'minus':
            return Icons.remove;
          case 'increment':
            return Icons.add;
          case 'decrement':
            return Icons.remove;
          case 'up':
            return Icons.arrow_upward;
          case 'down':
            return Icons.arrow_downward;
          default:
            return defaultIcon;
        }
      }

      return defaultIcon;
    }

    final incrementIcon = parseIconData(json['incrementIcon'], Icons.add);
    final decrementIcon = parseIconData(json['decrementIcon'], Icons.remove);

    // Parse padding and margin
    EdgeInsetsGeometry parsePadding(
      dynamic paddingValue,
      EdgeInsetsGeometry defaultPadding,
    ) {
      if (paddingValue == null) {
        return defaultPadding;
      }

      if (paddingValue is Map) {
        final left =
            double.tryParse(paddingValue['left']?.toString() ?? '12.0') ?? 12.0;
        final top =
            double.tryParse(paddingValue['top']?.toString() ?? '8.0') ?? 8.0;
        final right =
            double.tryParse(paddingValue['right']?.toString() ?? '12.0') ??
            12.0;
        final bottom =
            double.tryParse(paddingValue['bottom']?.toString() ?? '8.0') ?? 8.0;

        return EdgeInsets.fromLTRB(left, top, right, bottom);
      }

      return defaultPadding;
    }

    final padding = parsePadding(
      json['padding'],
      const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
    );

    final margin = parsePadding(json['margin'], EdgeInsets.zero);

    // Parse string properties
    final prefix = json['prefix']?.toString();
    final suffix = json['suffix']?.toString();
    final label = json['label']?.toString();
    final hint = json['hint']?.toString();
    final errorText = json['errorText']?.toString();
    final thousandsSeparator = json['thousandsSeparator']?.toString() ?? ',';
    final tooltip = json['tooltip']?.toString();
    final semanticsLabel = json['semanticsLabel']?.toString();
    final jsonValidationMessage = json['jsonValidationMessage']?.toString();
    final jsonNumberFormat = json['jsonNumberFormat']?.toString();
    final jsonUnitType = json['jsonUnitType']?.toString();

    // Parse decoration
    InputDecoration? decoration;
    if (json['decoration'] != null && json['decoration'] is Map) {
      final decorationMap = json['decoration'] as Map<String, dynamic>;

      decoration = InputDecoration(
        labelText: decorationMap['labelText']?.toString(),
        hintText: decorationMap['hintText']?.toString(),
        errorText: decorationMap['errorText']?.toString(),
        prefixText: decorationMap['prefixText']?.toString(),
        suffixText: decorationMap['suffixText']?.toString(),
        filled: decorationMap['filled'] == true,
        fillColor: parseColor(decorationMap['fillColor']),
        border:
            decorationMap['border'] == 'outline'
                ? OutlineInputBorder(
                  borderRadius: BorderRadius.circular(
                    decorationMap['borderRadius'] != null
                        ? double.tryParse(
                              decorationMap['borderRadius'].toString(),
                            ) ??
                            borderRadius
                        : borderRadius,
                  ),
                )
                : null,
      );
    }

    // Create the widget with all parsed properties
    return NumberWidget(
      initialValue: initialValue,
      minValue: minValue,
      maxValue: maxValue,
      stepValue: stepValue,
      showButtons: showButtons,
      buttonSize: buttonSize,
      incrementIcon: incrementIcon,
      decrementIcon: decrementIcon,
      buttonColor: buttonColor,
      buttonIconColor: buttonIconColor,
      width: width,
      height: height,
      textStyle: textStyle,
      decoration: decoration,
      prefix: prefix,
      suffix: suffix,
      label: label,
      labelStyle: labelStyle,
      hint: hint,
      errorText: errorText,
      enabled: enabled,
      readOnly: readOnly,
      textAlign: textAlign,
      borderRadius: borderRadius,
      borderColor: borderColor,
      backgroundColor: backgroundColor,
      padding: padding,
      margin: margin,
      direction: direction,
      useThousandsSeparator: useThousandsSeparator,
      thousandsSeparator: thousandsSeparator,
      allowNegative: allowNegative,
      showClearButton: showClearButton,
      decimalPlaces: decimalPlaces,
      allowDecimal: allowDecimal,
      // Advanced interaction properties
      hoverColor: hoverColor,
      focusColor: focusColor,
      tooltip: tooltip,
      semanticsLabel: semanticsLabel,
      enableFeedback: enableFeedback,
      // JSON configuration properties
      jsonConfig: json,
      useJsonValidation: useJsonValidation,
      useJsonStyling: useJsonStyling,
      useJsonBehavior: useJsonBehavior,
      useJsonFormatting: useJsonFormatting,
      jsonValidationMessage: jsonValidationMessage,
      jsonNumberFormat: jsonNumberFormat,
      jsonUnitType: jsonUnitType,
    );
  }

  @override
  NumberWidgetState createState() => NumberWidgetState();
}

class NumberWidgetState extends State<NumberWidget> {
  late TextEditingController _controller;
  num? _currentValue;
  String? _errorText;
  bool _isHovered = false;
  bool _hasFocus = false;
  Map<String, dynamic>? _parsedJsonConfig;
  List<String>? _jsonValidationRules;
  String? _jsonNumberFormatPattern;

  @override
  void initState() {
    super.initState();
    _currentValue = widget.initialValue;

    // Parse JSON configuration if provided
    if (widget.jsonConfig != null) {
      _parseJsonConfig();
    }

    // Format the initial value using JSON format if specified
    if (widget.useJsonFormatting && _jsonNumberFormatPattern != null) {
      _controller = TextEditingController(
        text: _formatValueWithPattern(_currentValue),
      );
    } else {
      _controller = TextEditingController(text: _formatValue(_currentValue));
    }
  }

  /// Parses the JSON configuration
  void _parseJsonConfig() {
    try {
      if (widget.jsonConfig is String) {
        _parsedJsonConfig =
            jsonDecode(widget.jsonConfig as String) as Map<String, dynamic>;
      } else if (widget.jsonConfig is Map) {
        _parsedJsonConfig = Map<String, dynamic>.from(widget.jsonConfig as Map);
      }

      // Extract validation rules if needed
      if (widget.useJsonValidation &&
          _parsedJsonConfig != null &&
          _parsedJsonConfig!.containsKey('validationRules')) {
        final rules = _parsedJsonConfig!['validationRules'];
        if (rules is List) {
          _jsonValidationRules = List<String>.from(
            rules.map((rule) => rule.toString()),
          );
        }
      }

      // Extract number format pattern if needed
      if (widget.useJsonFormatting &&
          _parsedJsonConfig != null &&
          _parsedJsonConfig!.containsKey('numberFormat')) {
        _jsonNumberFormatPattern =
            _parsedJsonConfig!['numberFormat'].toString();
      }
    } catch (e) {
      debugPrint('Error parsing JSON config: $e');
    }
  }

  /// Handles hover state changes
  void _onHoverChange(bool isHovered) {
    setState(() {
      _isHovered = isHovered;

      // Call onHover callback if provided
      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }
    });
  }

  /// Handles focus state changes
  void _onFocusChange(bool hasFocus) {
    setState(() {
      _hasFocus = hasFocus;

      // Call onFocus callback if provided
      if (widget.onFocus != null) {
        widget.onFocus!(hasFocus);
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  /// Formats a number value using the standard formatting
  String _formatValue(num? value) {
    if (value == null) return '';

    if (widget.allowDecimal) {
      // Format with decimal places
      final double doubleValue = value.toDouble();

      // Format with fixed decimal places
      String formatted = doubleValue.toStringAsFixed(widget.decimalPlaces);

      // Remove trailing zeros after decimal point if needed
      if (widget.decimalPlaces > 0) {
        formatted = formatted.replaceAll(RegExp(r'\.?0+$'), '');
      }

      if (widget.useThousandsSeparator) {
        // Add thousands separator
        final parts = formatted.split('.');
        final integerPart = parts[0];
        final decimalPart = parts.length > 1 ? '.${parts[1]}' : '';

        final buffer = StringBuffer();
        final integerDigits = integerPart.replaceAll('-', '');
        final isNegative = integerPart.startsWith('-');

        if (isNegative) {
          buffer.write('-');
        }

        for (int i = 0; i < integerDigits.length; i++) {
          if (i > 0 && (integerDigits.length - i) % 3 == 0) {
            buffer.write(widget.thousandsSeparator);
          }
          buffer.write(integerDigits[i]);
        }

        buffer.write(decimalPart);
        return buffer.toString();
      }

      return formatted;
    } else {
      // Format as integer
      final int intValue = value.toInt();

      if (widget.useThousandsSeparator) {
        // Format with thousands separator
        final String valueStr = intValue.toString();
        final StringBuffer result = StringBuffer();

        if (intValue < 0) {
          result.write('-');
        }

        final String digits = valueStr.replaceAll(RegExp(r'[^0-9]'), '');
        final int length = digits.length;

        for (int i = 0; i < length; i++) {
          if (i > 0 && (length - i) % 3 == 0) {
            result.write(widget.thousandsSeparator);
          }
          result.write(digits[i]);
        }

        return result.toString();
      } else {
        // No formatting
        return intValue.toString();
      }
    }
  }

  /// Formats a number value using a custom pattern from JSON
  String _formatValueWithPattern(num? value) {
    if (value == null || _jsonNumberFormatPattern == null) {
      return _formatValue(value);
    }

    try {
      String result = value.toString();
      final pattern = _jsonNumberFormatPattern!;

      // Handle currency format
      if (pattern.contains('currency')) {
        // Extract currency symbol if specified
        String currencySymbol = '\$';
        final symbolMatch = RegExp(r'symbol:([^,]+)').firstMatch(pattern);
        if (symbolMatch != null) {
          currencySymbol = symbolMatch.group(1) ?? '\$';
        }

        // Format the number with decimal places
        int decimalPlaces = widget.decimalPlaces;
        final decimalMatch = RegExp(r'decimal:(\d+)').firstMatch(pattern);
        if (decimalMatch != null) {
          decimalPlaces =
              int.tryParse(decimalMatch.group(1) ?? '') ?? widget.decimalPlaces;
        }

        if (value is double) {
          result = value.toStringAsFixed(decimalPlaces);
        }

        // Add thousands separator
        final parts = result.split('.');
        final integerPart = parts[0];
        final decimalPart = parts.length > 1 ? '.${parts[1]}' : '';

        final buffer = StringBuffer();
        final integerDigits = integerPart.replaceAll('-', '');
        final isNegative = integerPart.startsWith('-');

        if (isNegative) {
          buffer.write('-');
        }

        for (int i = 0; i < integerDigits.length; i++) {
          if (i > 0 && (integerDigits.length - i) % 3 == 0) {
            buffer.write(widget.thousandsSeparator);
          }
          buffer.write(integerDigits[i]);
        }

        buffer.write(decimalPart);
        result = buffer.toString();

        // Add currency symbol based on position
        if (pattern.contains('position:suffix')) {
          result = '$result $currencySymbol';
        } else {
          result = '$currencySymbol $result';
        }
      }
      // Handle percentage format
      else if (pattern.contains('percentage')) {
        // Format the number with decimal places
        int decimalPlaces = widget.decimalPlaces;
        final decimalMatch = RegExp(r'decimal:(\d+)').firstMatch(pattern);
        if (decimalMatch != null) {
          decimalPlaces =
              int.tryParse(decimalMatch.group(1) ?? '') ?? widget.decimalPlaces;
        }

        // Convert to percentage value if needed
        if (pattern.contains('multiply:true')) {
          value = value * 100;
        }

        if (value is double) {
          result = value.toStringAsFixed(decimalPlaces);
        } else {
          result = value.toString();
        }

        // Add percentage symbol
        result = '$result%';
      }
      // Handle custom number format
      else if (pattern.contains('format:')) {
        final formatMatch = RegExp(r'format:([^,]+)').firstMatch(pattern);
        if (formatMatch != null) {
          final format = formatMatch.group(1) ?? '';

          // Handle decimal places
          int decimalPlaces = widget.decimalPlaces;
          final decimalMatch = RegExp(r'decimal:(\d+)').firstMatch(pattern);
          if (decimalMatch != null) {
            decimalPlaces =
                int.tryParse(decimalMatch.group(1) ?? '') ??
                widget.decimalPlaces;
          }

          if (value is double) {
            result = value.toStringAsFixed(decimalPlaces);
          }

          // Add thousands separator if needed
          if (format.contains('#,###') || pattern.contains('thousands:true')) {
            final parts = result.split('.');
            final integerPart = parts[0];
            final decimalPart = parts.length > 1 ? '.${parts[1]}' : '';

            final buffer = StringBuffer();
            final integerDigits = integerPart.replaceAll('-', '');
            final isNegative = integerPart.startsWith('-');

            if (isNegative) {
              buffer.write('-');
            }

            for (int i = 0; i < integerDigits.length; i++) {
              if (i > 0 && (integerDigits.length - i) % 3 == 0) {
                buffer.write(widget.thousandsSeparator);
              }
              buffer.write(integerDigits[i]);
            }

            buffer.write(decimalPart);
            result = buffer.toString();
          }

          // Add prefix if specified
          final prefixMatch = RegExp(r'prefix:([^,]+)').firstMatch(pattern);
          if (prefixMatch != null) {
            final prefix = prefixMatch.group(1) ?? '';
            result = '$prefix$result';
          }

          // Add suffix if specified
          final suffixMatch = RegExp(r'suffix:([^,]+)').firstMatch(pattern);
          if (suffixMatch != null) {
            final suffix = suffixMatch.group(1) ?? '';
            result = '$result$suffix';
          }
        }
      }

      return result;
    } catch (e) {
      debugPrint('Error formatting with pattern: $e');
      return _formatValue(value);
    }
  }

  /// Parses a string value to a number
  num? _parseValue(String text) {
    if (text.isEmpty) return null;

    // Remove thousands separators if used
    if (widget.useThousandsSeparator) {
      text = text.replaceAll(widget.thousandsSeparator, '');
    }

    if (widget.allowDecimal) {
      return double.tryParse(text);
    } else {
      return int.tryParse(text);
    }
  }

  void _validateAndUpdateValue(num? value) {
    if (value == null) {
      setState(() {
        _errorText = null;
        _currentValue = null;
      });
      widget.onChanged?.call(null);
      return;
    }

    // Check min/max constraints
    if (widget.minValue != null && value < widget.minValue!) {
      setState(() {
        _errorText = 'Value must be at least ${widget.minValue}';
        _currentValue = widget.minValue;
      });
    } else if (widget.maxValue != null && value > widget.maxValue!) {
      setState(() {
        _errorText = 'Value must be at most ${widget.maxValue}';
        _currentValue = widget.maxValue;
      });
    } else {
      setState(() {
        _errorText = null;
        _currentValue = value;
      });
    }

    // Update text field if needed
    final String formattedValue = _formatValue(_currentValue);
    if (_controller.text != formattedValue) {
      _controller.value = TextEditingValue(
        text: formattedValue,
        selection: TextSelection.collapsed(offset: formattedValue.length),
      );
    }

    widget.onChanged?.call(_currentValue);
  }

  void _incrementValue() {
    if (!widget.enabled || widget.readOnly) return;

    final num newValue = (_currentValue ?? 0) + widget.stepValue;
    _validateAndUpdateValue(newValue);
  }

  void _decrementValue() {
    if (!widget.enabled || widget.readOnly) return;

    final num newValue = (_currentValue ?? 0) - widget.stepValue;

    // Check if negative values are allowed
    if (!widget.allowNegative && newValue < 0) {
      _validateAndUpdateValue(0);
    } else {
      _validateAndUpdateValue(newValue);
    }
  }

  @override
  Widget build(BuildContext context) {
    const double buttonHeight = 56.0;
    const Color buttonColor = Color(0xFF0058FF); // Primary color

    return MouseRegion(
      onEnter: (_) => _onHoverChange(true),
      onExit: (_) => _onHoverChange(false),
      child: Tooltip(
        message: widget.tooltip ?? '',
        child: Semantics(
          label: widget.semanticsLabel,
          child: Padding(
            padding: widget.margin,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Label above the integrated layout
                if (widget.label != null)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 8.0),
                    child: Text(
                      widget.label!,
                      style:
                          widget.labelStyle ??
                          TextStyle(
                            fontSize: 18.0,
                            fontWeight: FontWeight.w500,
                            color: Colors.black,
                          ),
                    ),
                  ),

                // Integrated layout: [- button] [text field] [+ button]
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Decrement button (-)
                    Container(
                      height: buttonHeight,
                      width: 56.0,
                      decoration: BoxDecoration(
                        color: widget.buttonColor ?? buttonColor,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(widget.borderRadius),
                          bottomLeft: Radius.circular(widget.borderRadius),
                        ),
                      ),
                      child: IconButton(
                        icon: Icon(
                          widget.decrementIcon,
                          color: widget.buttonIconColor ?? Colors.white,
                          size: 20.0,
                        ),
                        onPressed:
                            widget.enabled && !widget.readOnly
                                ? _decrementValue
                                : null,
                      ),
                    ),

                    // Text field in the middle
                    Container(
                      height: buttonHeight,
                      width: 80.0,
                      decoration: BoxDecoration(
                        color: widget.backgroundColor ?? Colors.white,
                      ),
                      alignment: Alignment.center,
                      child: TextField(
                        controller: _controller,
                        keyboardType: TextInputType.numberWithOptions(
                          decimal: widget.allowDecimal,
                          signed: widget.allowNegative,
                        ),
                        textAlign: TextAlign.center,
                        textAlignVertical: TextAlignVertical.center,
                        style:
                            widget.textStyle ??
                            TextStyle(
                              fontSize: 18.0,
                              fontWeight: FontWeight.w500,
                              color: Colors.black,
                            ),
                        decoration: InputDecoration(
                          border: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          disabledBorder: InputBorder.none,
                          errorBorder: InputBorder.none,
                          focusedErrorBorder: InputBorder.none,
                          contentPadding: EdgeInsets.zero,
                          filled: false,
                          isDense: true,
                        ),
                        enabled: widget.enabled,
                        readOnly: widget.readOnly,
                        inputFormatters: [
                          // Allow only valid numeric input
                          TextInputFormatter.withFunction((oldValue, newValue) {
                            // Allow empty value
                            if (newValue.text.isEmpty) return newValue;

                            // Create a pattern that matches valid input
                            String pattern = r'^';

                            // Add optional minus sign if negative values are allowed
                            if (widget.allowNegative) {
                              pattern += r'-?';
                            }

                            // Add pattern for digits with optional thousands separators and decimal point
                            if (widget.allowDecimal) {
                              if (widget.useThousandsSeparator) {
                                final escapedSeparator = RegExp.escape(
                                  widget.thousandsSeparator,
                                );
                                pattern +=
                                    r'\d{1,3}(?:' +
                                    escapedSeparator +
                                    r'\d{3})*';
                              } else {
                                pattern += r'\d+';
                              }

                              // Add optional decimal part
                              pattern += r'(?:\.\d*)?';
                            } else {
                              if (widget.useThousandsSeparator) {
                                final escapedSeparator = RegExp.escape(
                                  widget.thousandsSeparator,
                                );
                                pattern +=
                                    r'\d{1,3}(?:' +
                                    escapedSeparator +
                                    r'\d{3})*';
                              } else {
                                pattern += r'\d+';
                              }
                            }

                            pattern += r'$';

                            if (RegExp(pattern).hasMatch(newValue.text)) {
                              return newValue;
                            }

                            return oldValue;
                          }),
                        ],
                        onChanged: (text) {
                          final num? value = _parseValue(text);
                          _validateAndUpdateValue(value);
                        },
                        onSubmitted: (text) {
                          final num? value = _parseValue(text);
                          _validateAndUpdateValue(value);
                          widget.onSubmitted?.call(_currentValue);
                        },
                      ),
                    ),

                    // Increment button (+)
                    Container(
                      height: buttonHeight,
                      width: 60.0,
                      decoration: BoxDecoration(
                        color: widget.buttonColor ?? buttonColor,
                        borderRadius: BorderRadius.only(
                          topRight: Radius.circular(widget.borderRadius),
                          bottomRight: Radius.circular(widget.borderRadius),
                        ),
                      ),
                      child: IconButton(
                        icon: Icon(
                          widget.incrementIcon,
                          color: widget.buttonIconColor ?? Colors.white,
                          size: 20.0,
                        ),
                        onPressed:
                            widget.enabled && !widget.readOnly
                                ? _incrementValue
                                : null,
                      ),
                    ),
                  ],
                ),

                // Error text below if needed
                if (_errorText != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 4.0),
                    child: Text(
                      _errorText!,
                      style: TextStyle(fontSize: 12.0, color: Colors.red),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
