import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import '../services/auth_service.dart';
import '../services/dio_client.dart';
import '../services/web_audio_cache_service.dart';
import '../utils/logger.dart';
import 'dart:async';

class TextToSpeechService {
  final Dio _dio = DioClient().client;
  final AuthService _authService = AuthService();

  // API endpoint - using the new adapter endpoint
  final String _ttsAdapterUrl = 'http://10.26.1.52:8001/adapters';

  // Singleton instance
  static final TextToSpeechService _instance = TextToSpeechService._internal();

  // Factory constructor
  factory TextToSpeechService() => _instance;

  // Internal constructor
  TextToSpeechService._internal() {
    // Initialize the cache cleanup timer
    _startCacheCleanupTimer();
  }

  // In-memory cache for audio bytes
  final Map<String, Uint8List> _audioCache = {};

  // Cache metadata to track when each item was added
  final Map<String, DateTime> _cacheTimestamps = {};

  // Cache configuration
  final Duration _cacheExpiration =
      Duration(hours: 1); // Cache items expire after 1 hour
  final int _maxCacheSize = 50; // Maximum number of items in cache

  // Timer for cache cleanup
  Timer? _cleanupTimer;

  /// Generate a unique output filename with timestamp
  String _generateOutputFilename() {
    final now = DateTime.now();
    final timestamp = DateFormat('yyyyMMdd_HHmmss').format(now);
    return 'response_$timestamp.wav';
  }

  /// Generate a cache key for a text-to-speech request
  String _generateCacheKey({
    required String text,
    required String targetLanguage,
    required String voice,
  }) {
    // Create a unique key based on the request parameters
    return '$text|$targetLanguage|$voice';
  }

  /// Start the cache cleanup timer
  void _startCacheCleanupTimer() {
    // Run cleanup every 15 minutes
    _cleanupTimer = Timer.periodic(Duration(minutes: 15), (_) {
      _cleanupExpiredCache();
    });
  }

  /// Clean up expired cache items
  void _cleanupExpiredCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    // Find expired items
    _cacheTimestamps.forEach((key, timestamp) {
      if (now.difference(timestamp) > _cacheExpiration) {
        expiredKeys.add(key);
      }
    });

    // Remove expired items
    for (final key in expiredKeys) {
      _audioCache.remove(key);
      _cacheTimestamps.remove(key);
      Logger.info('Removed expired cache item: $key');
    }

    // If cache is still too large, remove oldest items
    if (_audioCache.length > _maxCacheSize) {
      final sortedEntries = _cacheTimestamps.entries.toList()
        ..sort((a, b) => a.value.compareTo(b.value));

      final itemsToRemove = _audioCache.length - _maxCacheSize;
      for (var i = 0; i < itemsToRemove; i++) {
        if (i < sortedEntries.length) {
          final key = sortedEntries[i].key;
          _audioCache.remove(key);
          _cacheTimestamps.remove(key);
          Logger.info('Removed oldest cache item: $key');
        }
      }
    }

    Logger.info(
        'Cache cleanup completed. Current cache size: ${_audioCache.length}');
  }

  /// Add an item to the cache
  void _addToCache(String key, Uint8List audioBytes) {
    _audioCache[key] = audioBytes;
    _cacheTimestamps[key] = DateTime.now();

    // If cache exceeds max size, remove oldest item
    if (_audioCache.length > _maxCacheSize) {
      final oldestKey = _cacheTimestamps.entries
          .reduce((a, b) => a.value.isBefore(b.value) ? a : b)
          .key;

      _audioCache.remove(oldestKey);
      _cacheTimestamps.remove(oldestKey);
      Logger.info('Cache full, removed oldest item: $oldestKey');
    }
  }

  /// Check if an item exists in the cache
  bool _isCached(String key) {
    return _audioCache.containsKey(key);
  }

  /// Get an item from the cache
  Uint8List? _getFromCache(String key) {
    if (_isCached(key)) {
      // Update the timestamp to mark it as recently used
      _cacheTimestamps[key] = DateTime.now();
      return _audioCache[key];
    }
    return null;
  }

  /// Get audio bytes directly from cache or API
  ///
  /// This is the primary method to get audio bytes for text-to-speech
  /// It checks the cache first, and if not found, makes a direct API call
  Future<Uint8List?> getAudioBytes({
    required String text,
    required String targetLanguage,
    String voice = 'S1',
  }) async {
    try {
      // Generate cache key
      final cacheKey = _generateCacheKey(
        text: text,
        targetLanguage: targetLanguage,
        voice: voice,
      );

      // Check if we already have this audio in cache
      if (_isCached(cacheKey)) {
        Logger.info('Retrieved audio bytes from cache for: $cacheKey');
        return _getFromCache(cacheKey);
      }

      // Not in cache, need to make API call
      Logger.info('Audio not in cache, fetching from API: $text');

      // Get the token
      final token = await _authService.getValidToken();

      if (token == null) {
        Logger.error('No valid token available for text-to-speech');
        return null;
      }

      // Prepare request payload with new adapter structure
      final payload = {
        'adapter_config': {
          'service': 'tts',
          'operation': 'download',
          'version': 'v1',
        },
        'parameters': {
          'text': text,
          'voice': voice,
          'target_lang': targetLanguage,
        },
      };

      // Set up headers
      final headers = {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };

      // Make API call to get audio bytes
      final response = await _dio.post(
        _ttsAdapterUrl,
        data: payload,
        options: Options(
          headers: headers,
          responseType: ResponseType.bytes,
        ),
      );

      // Check if the response is successful
      if (response.statusCode == 200) {
        // Get the audio bytes
        final audioBytes = Uint8List.fromList(response.data as List<int>);

        // Add to cache
        _addToCache(cacheKey, audioBytes);
        Logger.info('Added audio bytes to cache with key: $cacheKey');

        return audioBytes;
      } else if (response.statusCode == 401) {
        // Handle 401 Unauthorized - try to refresh token and retry
        Logger.info(
            'Received 401 Unauthorized, attempting to refresh token and retry');

        final newToken = await _authService.getValidToken();
        if (newToken != null) {
          Logger.info(
              'Token refreshed successfully, retrying text-to-speech request');

          // Update headers with new token
          final retryHeaders = {
            'Authorization': 'Bearer $newToken',
            'Content-Type': 'application/json',
          };

          // Retry the request
          final retryResponse = await _dio.post(
            _ttsAdapterUrl,
            data: payload,
            options: Options(
              headers: retryHeaders,
              responseType: ResponseType.bytes,
            ),
          );

          if (retryResponse.statusCode == 200) {
            Logger.info('Text-to-speech successful after retry');

            final audioBytes =
                Uint8List.fromList(retryResponse.data as List<int>);

            // Add to cache
            _addToCache(cacheKey, audioBytes);
            Logger.info('Added audio bytes to cache with key: $cacheKey');

            return audioBytes;
          } else {
            final errorMessage =
                'Text-to-speech failed after retry: ${retryResponse.statusCode}';
            Logger.error(errorMessage);

            // Display alert with response message for non-200 status codes
            if (retryResponse.data != null &&
                retryResponse.data is Map &&
                retryResponse.data.containsKey('message')) {
              Logger.error('TTS Error: ${retryResponse.data['message']}');
            }

            return null;
          }
        } else {
          Logger.error('Failed to refresh token for text-to-speech');
          return null;
        }
      } else {
        final errorMessage = 'Text-to-speech failed: ${response.statusCode}';
        Logger.error(errorMessage);

        // Display alert with response message for non-200 status codes
        if (response.data != null &&
            response.data is Map &&
            response.data.containsKey('message')) {
          Logger.error('TTS Error: ${response.data['message']}');
        }

        return null;
      }
    } catch (e) {
      Logger.error('Error getting audio bytes: $e');

      // Handle specific error types for better user experience
      String errorMessage =
          'An error occurred during text-to-speech conversion';

      if (e.toString().contains('DioException')) {
        if (e.toString().contains('SocketException')) {
          errorMessage =
              'Network error. Please check your internet connection.';
        } else if (e.toString().contains('Connection refused')) {
          errorMessage =
              'Text-to-speech service is unreachable. Please try again later.';
        } else if (e.toString().contains('404')) {
          errorMessage =
              'Text-to-speech service not found. Please contact support.';
        } else if (e.toString().contains('timeout')) {
          errorMessage = 'Request timed out. Please try again.';
        }
      }

      Logger.error('TTS Error: $errorMessage');
      return null;
    }
  }

  /// Process audio bytes and return a playable path
  Future<String?> _processAudioBytes(
      Uint8List audioBytes, String filename) async {
    String localPath;

    if (kIsWeb) {
      // For web, we need to create a Blob URL for better playback
      try {
        // Create a unique cache key for this audio file
        final cacheKey =
            'audio_${DateTime.now().millisecondsSinceEpoch}_$filename';

        // Get the audio cache service
        final webAudioCacheService = WebAudioCacheService();

        // Store the audio data and get a blob URL
        final blobUrl =
            await webAudioCacheService.storeAudioData(cacheKey, audioBytes);

        // Use the blob URL for playback
        localPath = blobUrl;

        // Log that we're using a blob URL for web
        Logger.info('Using blob URL for web: $localPath');

        return localPath;
      } catch (e) {
        // If caching fails, fall back to a temporary solution
        Logger.warning('Could not create blob URL for web: $e');
        // We can't use a direct URL here since we're using the adapter endpoint
        // Instead, we'll return null and let the caller handle it
        return null;
      }
    } else {
      // For mobile platforms, save the file locally
      try {
        // Get temporary directory to save the file
        final tempDir = await getTemporaryDirectory();
        localPath = '${tempDir.path}/$filename';

        // Save the file locally
        final file = File(localPath);
        await file.writeAsBytes(audioBytes);

        Logger.info('Audio file saved locally: $localPath');
      } catch (e) {
        // If we can't save to a temporary directory, return null
        Logger.warning('Could not save file locally: $e');
        return null;
      }
    }

    Logger.info('Audio file processed successfully: $localPath');
    return localPath;
  }

  /// Process audio bytes and return a playable path
  /// This is useful for saving audio to a file or creating a blob URL
  Future<String?> processAudioBytes({
    required Uint8List audioBytes,
    required String text,
    required String targetLanguage,
    String voice = 'S1',
  }) async {
    // Generate a filename
    final filename = _generateOutputFilename();

    return _processAudioBytes(audioBytes, filename);
  }

  /// Dispose resources
  void dispose() {
    _cleanupTimer?.cancel();
    _audioCache.clear();
    _cacheTimestamps.clear();
  }
}
