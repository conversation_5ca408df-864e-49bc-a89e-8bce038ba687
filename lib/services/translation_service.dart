import 'package:dio/dio.dart';
import 'package:nsl/models/multimedia/translation_response_model.dart';
import 'package:nsl/services/auth_service.dart';
import 'package:nsl/services/dio_client.dart';
import 'package:nsl/utils/logger.dart';

/// Service for handling translation API calls
class TranslationService {
  final Dio _dio = DioClient().client;
  final AuthService _authService = AuthService();

  // Base URL for the API
  final String _baseUrl = 'http://10.26.1.52:8001/adapters';

  /// Translate text to the target language
  ///
  /// [text] - The text to translate
  /// [sourceLang] - The source language code (e.g., 'en', 'hi', 'te')
  /// [targetLang] - The target language code (e.g., 'fr', 'es', 'hi', 'te')
  ///
  /// Returns a Map with:
  /// - success: Whether the translation was successful
  /// - translatedText: The translated text (if successful)
  /// - originalText: The original text (in case translation fails)
  /// - message: Error message (if unsuccessful)
  /// - model: The TranslationResponseModel object (if successful)
  Future<Map<String, dynamic>> translateText(
      String text, String sourceLang, String targetLang) async {
    try {
      // Get the token
      final token = await _authService.getValidToken();

      if (token == null) {
        Logger.error('No valid token available for translation');
        return {
          'success': false,
          'message': 'Authentication required',
          'originalText': text,
        };
      }

      // Set up headers
      final headers = {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };

      // Create request data with new structure
      final data = {
        'adapter_config': {
          'service': 'translation',
          'operation': 'translate',
          'version': 'v1',
        },
        'parameters': {
          'text': text,
          'source_lang': sourceLang,
          'target_lang': targetLang,
          'model': 'default',
        },
      };

      Logger.info('Translating text from $sourceLang to $targetLang');

      // Make the API call
      final response = await _dio.post(
        _baseUrl,
        data: data,
        options: Options(
          headers: headers,
        ),
      );

      // Check if the response is successful
      if (response.statusCode == 200) {
        Logger.info('Translation successful');
        Logger.info(
            'Translation response structure: ${response.data.keys.join(', ')}');

        // Log the data structure if it exists
        if (response.data['data'] != null) {
          Logger.info(
              'Data structure: ${response.data['data'].keys.join(', ')}');
        }

        // Parse the response using the TranslationResponseModel
        final translationModel =
            TranslationResponseModel.fromJson(response.data);

        // Get the translated text from the model based on the new structure
        final translatedText = translationModel.data?.translatedText ?? text;

        return {
          'success': true,
          'translatedText': translatedText,
          'originalText': text,
          'model': translationModel,
        };
      } else if (response.statusCode == 401) {
        // Handle 401 Unauthorized - try to refresh token and retry
        Logger.info(
            'Received 401 Unauthorized, attempting to refresh token and retry');

        final newToken = await _authService.getValidToken();
        if (newToken != null) {
          Logger.info(
              'Token refreshed successfully, retrying translation request');

          // Update headers with new token
          final retryHeaders = {
            'Authorization': 'Bearer $newToken',
            'Content-Type': 'application/json',
          };

          // Retry the request
          final retryResponse = await _dio.post(
            _baseUrl,
            data: data,
            options: Options(
              headers: retryHeaders,
            ),
          );

          if (retryResponse.statusCode == 200) {
            Logger.info('Translation successful after retry');

            final translationModel =
                TranslationResponseModel.fromJson(retryResponse.data);
            final translatedText =
                translationModel.data?.translatedText ?? text;

            return {
              'success': true,
              'translatedText': translatedText,
              'originalText': text,
              'model': translationModel,
            };
          } else {
            final errorMessage =
                'Translation failed after retry: ${retryResponse.statusCode}';
            Logger.error(errorMessage);

            // Display alert with response message for non-200 status codes
            String alertMessage = errorMessage;
            if (retryResponse.data != null &&
                retryResponse.data is Map &&
                retryResponse.data.containsKey('message')) {
              alertMessage = retryResponse.data['message'];
            }

            return {
              'success': false,
              'message': alertMessage,
              'originalText': text,
            };
          }
        } else {
          Logger.error('Failed to refresh token for translation');
          return {
            'success': false,
            'message': 'Authentication failed. Please login again.',
            'originalText': text,
          };
        }
      } else {
        final errorMessage = 'Translation failed: ${response.statusCode}';
        Logger.error(errorMessage);

        // Display alert with response message for non-200 status codes
        String alertMessage = errorMessage;
        if (response.data != null &&
            response.data is Map &&
            response.data.containsKey('message')) {
          alertMessage = response.data['message'];
        }

        return {
          'success': false,
          'message': alertMessage,
          'originalText': text,
        };
      }
    } catch (e) {
      Logger.error('Exception during translation: $e');

      // Handle specific error types for better user experience
      String errorMessage = 'An error occurred during translation';

      if (e.toString().contains('DioException')) {
        if (e.toString().contains('SocketException')) {
          errorMessage =
              'Network error. Please check your internet connection.';
        } else if (e.toString().contains('Connection refused')) {
          errorMessage =
              'Translation service is unreachable. Please try again later.';
        } else if (e.toString().contains('404')) {
          errorMessage =
              'Translation service not found. Please contact support.';
        } else if (e.toString().contains('timeout')) {
          errorMessage = 'Request timed out. Please try again.';
        }
      }

      return {
        'success': false,
        'message': errorMessage,
        'originalText': text,
      };
    }
  }
}
