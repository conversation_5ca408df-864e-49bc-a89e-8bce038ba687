import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:nsl/models/multimedia/file_upload_ocr_response.dart';
import 'package:nsl/services/auth_service.dart';
import 'package:nsl/services/dio_client.dart';
import 'package:nsl/utils/logger.dart';

class FileUploadService {
  final Dio _dio = DioClient().client;
  final AuthService _authService = AuthService();

  // API endpoint - using the new adapter endpoint
  final String _adapterUrl = 'http://**********:8001/adapters';

  // Method to pick a file
  Future<PlatformFile?> pickFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        return result.files.first;
      }

      return null;
    } catch (e) {
      Logger.error('Error picking file: $e');
      return null;
    }
  }

  // Method to upload a file
  Future<Map<String, dynamic>> uploadFile(PlatformFile file) async {
    try {
      // Get the token
      final token = await _authService.getValidToken();

      if (token == null) {
        Logger.error('No valid token available for file upload');
        return {
          'success': false,
          'message': 'Authentication required',
        };
      }

      // Create the request JSON structure
      final requestJson = {
        'adapter_config': {
          'service': 'ocr',
          'operation': 'updated_process_comprehensive',
          'version': 'v1',
        },
        'parameters': {
          'analysis_types': 'general',
          'apply_llm_correction': 'true',
        },
      };

      // Create form data with the new structure
      FormData formData;

      if (kIsWeb) {
        // For web, use the bytes
        formData = FormData.fromMap({
          'request': jsonEncode(requestJson),
          'file': MultipartFile.fromBytes(
            file.bytes!,
            filename: file.name,
          ),
        });
      } else {
        // For mobile, use the file path
        formData = FormData.fromMap({
          'request': jsonEncode(requestJson),
          'file': await MultipartFile.fromFile(
            file.path!,
            filename: file.name,
          ),
        });
      }

      // Set up headers
      final headers = {
        'Authorization': 'Bearer $token',
        'Content-Type': 'multipart/form-data',
      };

      Logger.info('Uploading file: ${file.name} using adapter endpoint');

      // Make the API call
      final response = await _dio.post(
        _adapterUrl,
        data: formData,
        options: Options(headers: headers),
      );

      // Check if the response is successful
      if (response.statusCode == 200) {
        Logger.info('File uploaded successfully: ${response.data}');

        // Parse the response using our model
        // The response now includes OCR data
        final fileUploadOcrResponse =
            FileUploadOcrResponse.fromJson(response.data);

        // Extract the OCR text
        final extractedText = fileUploadOcrResponse.data?.correctedText ??
            fileUploadOcrResponse.data?.originalText ??
            '';
        Logger.info('Extracted text from uploaded file: $extractedText');

        return {
          'success': true,
          'data': response.data,
          'file_name': file.name,
          'file_upload_ocr_response': fileUploadOcrResponse,
          'extracted_text': extractedText,
          'file_path': 'uploads/${file.name}',
          // fileUploadOcrResponse.output.filePath,
        };
      } else if (response.statusCode == 401) {
        // Handle 401 Unauthorized - try to refresh token and retry
        Logger.info(
            'Received 401 Unauthorized, attempting to refresh token and retry');

        final newToken = await _authService.getValidToken();
        if (newToken != null) {
          Logger.info(
              'Token refreshed successfully, retrying file upload request');

          // Update headers with new token
          final retryHeaders = {
            'Authorization': 'Bearer $newToken',
            'Content-Type': 'multipart/form-data',
          };

          // Retry the request
          final retryResponse = await _dio.post(
            _adapterUrl,
            data: formData,
            options: Options(headers: retryHeaders),
          );

          if (retryResponse.statusCode == 200) {
            Logger.info('File upload successful after retry');

            final fileUploadOcrResponse =
                FileUploadOcrResponse.fromJson(retryResponse.data);

            final extractedText = fileUploadOcrResponse.data?.correctedText ??
                fileUploadOcrResponse.data?.originalText ??
                '';

            return {
              'success': true,
              'data': retryResponse.data,
              'file_name': file.name,
              'file_upload_ocr_response': fileUploadOcrResponse,
              'extracted_text': extractedText,
              'file_path': 'uploads/${file.name}',
            };
          } else {
            final errorMessage =
                'File upload failed after retry: ${retryResponse.statusCode}';
            Logger.error(errorMessage);

            // Display alert with response message for non-200 status codes
            String alertMessage = errorMessage;
            if (retryResponse.data != null &&
                retryResponse.data is Map &&
                retryResponse.data.containsKey('message')) {
              alertMessage = retryResponse.data['message'];
            }

            return {
              'success': false,
              'message': alertMessage,
            };
          }
        } else {
          Logger.error('Failed to refresh token for file upload');
          return {
            'success': false,
            'message': 'Authentication failed. Please login again.',
          };
        }
      } else {
        final errorMessage = 'Error uploading file: ${response.statusCode}';
        Logger.error(errorMessage);

        // Display alert with response message for non-200 status codes
        String alertMessage = errorMessage;
        if (response.data != null &&
            response.data is Map &&
            response.data.containsKey('message')) {
          alertMessage = response.data['message'];
        }

        return {
          'success': false,
          'message': alertMessage,
        };
      }
    } catch (e) {
      Logger.error('Exception uploading file: $e');

      // Handle specific error types for better user experience
      String errorMessage = 'An error occurred during file upload';

      if (e.toString().contains('DioException')) {
        if (e.toString().contains('SocketException')) {
          errorMessage =
              'Network error. Please check your internet connection.';
        } else if (e.toString().contains('Connection refused')) {
          errorMessage =
              'File upload service is unreachable. Please try again later.';
        } else if (e.toString().contains('404')) {
          errorMessage =
              'File upload service not found. Please contact support.';
        } else if (e.toString().contains('timeout')) {
          errorMessage = 'Request timed out. Please try again.';
        } else if (e.toString().contains('413')) {
          errorMessage = 'File is too large. Please select a smaller file.';
        }
      }

      return {
        'success': false,
        'message': errorMessage,
      };
    }
  }
}
