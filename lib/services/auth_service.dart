import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:nsl/models/auth/auth_response_models.dart';
import 'package:nsl/models/auth/login_model.dart';
import 'package:nsl/models/auth/logout_model.dart';
import 'package:nsl/models/auth/user_profile_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../models/api_response.dart';
import '../models/registration.dart';
import '../utils/logger.dart';
import '../utils/constants.dart';
import '../utils/validators.dart';
import '../config/environment.dart';
import 'base_api_service.dart';

class AuthService extends BaseApiService {
  // Use constants for storing auth data
  static String get _tokenKey => AppConstants.tokenKey;
  static String get _userKey => AppConstants.userKey;
  static String get _rememberMeKey => AppConstants.rememberMeKey;
  static String get _emailKey => AppConstants.emailKey;
  static String get _passwordKey => AppConstants.passwordKey;
  static String get _mobileKey => AppConstants.mobileKey;

  // Login using the API
  Future<ApiResponse<AuthData>> login(String email, String password) async {
    try {
      Logger.info('Attempting login for user: $email');
      LoginModel? loginModel;

      // Validate email
      // final emailError = Validators.validateEmail(email);
      // if (emailError != null) {
      //   return {
      //     'success': false,
      //     'message': emailError,
      //   };
      // }

      // Validate password
      final passwordError = Validators.validatePassword(password);
      if (passwordError != null) {
        return ApiResponse.error(message: passwordError);
      }

      // Prepare the request payload as form data
      final formData = {
        'username': email, // Using email as username
        'password': password,
      };

      // Make the API call with form-urlencoded content type
      final response = await dio.post(
        Environment.loginUrl,
        data: formData,
        // options: Options(
        //   contentType: Headers.formUrlEncodedContentType,
        //   headers: {
        //     'Content-Type': 'application/x-www-form-urlencoded',
        //   },
        // ),
      );

      Logger.info('Login API response status: ${response.statusCode}');
      Logger.info('Login API response data: ${response.data}');

      // Check if the login was successful
      if (response.statusCode == 200) {
        // Extract tokens from the response
        loginModel = LoginModel.fromJson(response.data);
        final accessToken = loginModel.accessToken ?? '';
        final refreshToken = loginModel.refreshToken ?? '';

        // Get user details from the JWT token
        // final Map<String, dynamic> userDetails = _decodeJwtToken(accessToken);

        // Create a user object with the token and user details
        final user = User(
          id: loginModel.user?.userId ?? '',
          email: loginModel.user?.email ?? email,
          name: loginModel.user?.firstName ?? email,
          username: loginModel.user?.username ?? email,
          accessToken: accessToken,
          refreshToken: refreshToken,
          roles: loginModel.user?.roles,
          tenantId: loginModel.user?.tenantId,
        );

        final authData = AuthData(
          user: user,
          token: accessToken,
          refreshToken: refreshToken,
        );

        return ApiResponse.success(
          data: authData,
          statusCode: response.statusCode,
        );
      } else {
        // Login failed - check if it's an error response with detail
        if (response.data != null && response.data.containsKey('detail')) {
          // Create AuthData with error details
          final authData = AuthData.fromError(response.data);
          return ApiResponse.error(
            message: authData.error?.message ?? 'Login failed',
            statusCode: response.statusCode,
          );
        } else {
          // Fallback for other error formats
          final errorMessage = response.data?['message'] ?? 'Login failed';
          Logger.error('Login failed: $errorMessage');
          return ApiResponse.error(
            message: errorMessage,
            statusCode: response.statusCode,
          );
        }
      }
    } catch (e) {
      Logger.error('Login error: $e');
      String errorMessage = 'An error occurred during login';

      // Try to extract more specific error message if available
      if (e.toString().contains('DioException')) {
        if (e.toString().contains('SocketException')) {
          errorMessage =
              'Network error. Please check your internet connection.';
        } else if (e.toString().contains('Connection refused')) {
          errorMessage = 'Server is unreachable. Please try again later.';
        } else if (e.toString().contains('404')) {
          errorMessage = 'Login service not found. Please contact support.';
        } else if (e.toString().contains('timeout')) {
          errorMessage = 'Request timed out. Please try again.';
        }
      }

      return ApiResponse.error(message: errorMessage);
    }
  }

  // Registration using the API
  Future<ApiResponse<AuthData>> register({
    required String name,
    required String email,
    required String mobile,
    required String password,
    required String username,
    required String role,
    required String organization,
    String? profilePicture,
    String? initialStatus, // Optional: custom initial status
    bool? initialDisabled, // Optional: custom initial disabled state
    String? tenantId, // Optional: custom tenant ID
  }) async {
    try {
      Logger.info('Attempting registration for user: $email');

      // Validate name
      final nameError = Validators.validateRequired(name, 'Name');
      if (nameError != null) {
        return ApiResponse.error(message: nameError);
      }

      // Validate username
      final usernameError = Validators.validateUsername(username);
      if (usernameError != null) {
        return ApiResponse.error(message: usernameError);
      }

      // Validate email
      final emailError = Validators.validateEmail(email);
      if (emailError != null) {
        return ApiResponse.error(message: emailError);
      }

      // // Validate mobile
      // final mobileError = Validators.validateMobile(mobile);
      // if (mobileError != null) {
      //   return {
      //     'success': false,
      //     'message': mobileError,
      //   };
      // }

      // Validate role
      final roleError = Validators.validateRole(role);
      if (roleError != null) {
        return ApiResponse.error(message: roleError);
      }

      // Validate organization
      final organizationError = Validators.validateOrganization(organization);
      if (organizationError != null) {
        return ApiResponse.error(message: organizationError);
      }

      // Validate password
      final passwordError = Validators.validatePassword(password);
      if (passwordError != null) {
        return ApiResponse.error(message: passwordError);
      }

      // Split the name into first and last name
      List<String> nameParts = name.trim().split(' ');
      String firstName = nameParts.first;
      String lastName =
          nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

      // Create RegisterModel object with all applicable fields
      final registerData = RegisterModel(
        // User-provided fields
        username: username,
        email: email,
        firstName: firstName,
        lastName: lastName,
        roles: [role],

        // System fields for new registration (with configurable defaults)
        tenantId: tenantId ?? 't001', // Use provided or default tenant
        status: initialStatus ?? 'active', // Use provided or default status
        disabled:
            initialDisabled ?? false, // Use provided or default disabled state
        createdAt: DateTime.now(), // Set registration timestamp

        // userId will be generated by server, so we leave it null
        // userId: null,
      );

      // Prepare the request payload with only required fields for registration
      final payload = {
        'username': username,
        'email': email,
        'password': password,
        'first_name': firstName,
        'last_name': lastName,
        'roles': [role],
        'org_unit_id': organization,
        'tenant_id': tenantId ?? 't001',
      };

      // Only add optional fields if they have values
      if (initialStatus != null) {
        payload['status'] = initialStatus;
      }
      if (initialDisabled != null) {
        payload['disabled'] = initialDisabled;
      }

      // Note: No null values to remove since we're explicitly setting all values

      Logger.info('Registration payload before sending: $payload');
      Logger.info('Payload keys: ${payload.keys.toList()}');
      Logger.info('Payload values: ${payload.values.toList()}');

      // Make the API call
      final response = await dio.post(
        Environment.registerUrl,
        data: jsonEncode(payload),
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info('Registration API response status: ${response.statusCode}');
      Logger.info('Registration API response data: ${response.data}');

      // Check if the registration was successful (200 OK or 201 Created)
      if (response.statusCode == 200 || response.statusCode == 201) {
        // Parse the registration response using RegisterModel for consistency
        final registrationResponse = RegisterModel.fromJson(response.data);
        Logger.info(
            'Registration response parsed: ${registrationResponse.toJson()}');

        // Update the original registerData with server-generated fields
        final completeRegistrationData = registerData.copyWith(
          userId: registrationResponse.userId, // Server-generated user ID
          status: registrationResponse.status ??
              registerData.status, // Use server status if provided
          disabled: registrationResponse.disabled ??
              registerData.disabled, // Use server disabled state if provided
          createdAt: registrationResponse.createdAt ??
              registerData.createdAt, // Use server timestamp if provided
        );

        Logger.info(
            'Complete registration data: ${completeRegistrationData.toJson()}');

        // Create a user object from the registration response
        final user = User.fromAuth0Registration(response.data);

        // For now, we'll use a mock token since the API doesn't return one
        final token = 'mock-token-${DateTime.now().millisecondsSinceEpoch}';

        // Save user ID to SharedPreferences for future API calls
        if (response.data['user_id'] != null) {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('user_id', response.data['user_id']);
          Logger.info(
              'User ID saved to SharedPreferences: ${response.data['user_id']}');
        }

        // Create AuthData object
        final authData = AuthData(
          user: user,
          token: token,
        );

        return ApiResponse.success(
          data: authData,
          statusCode: response.statusCode,
          message: response.data['message'] ?? 'Registration successful',
        );
      } else {
        // Registration failed - handle different error types
        String errorMessage = 'Registration failed';

        if (response.statusCode == 422) {
          // Validation errors
          Logger.error('Validation error (422): ${response.data}');

          if (response.data is Map && response.data['detail'] != null) {
            if (response.data['detail'] is List) {
              // Handle array of validation errors
              final errors = response.data['detail'] as List;
              errorMessage =
                  errors.map((e) => e['msg'] ?? e.toString()).join(', ');
            } else if (response.data['detail'] is String) {
              errorMessage = response.data['detail'];
            } else {
              errorMessage = 'Validation failed: ${response.data['detail']}';
            }
          } else {
            errorMessage = 'Validation failed. Please check your input data.';
          }
        } else if (response.statusCode == 409) {
          // Conflict - user already exists
          errorMessage = response.data['detail'] ?? 'User already exists';
        } else {
          // Other errors
          errorMessage = response.data['detail'] ??
              response.data['message'] ??
              'Registration failed';
        }

        Logger.error(
            'Registration failed (${response.statusCode}): $errorMessage');
        Logger.error('Full response data: ${response.data}');

        return ApiResponse.error(
          message: errorMessage,
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      Logger.error('Registration error: $e');
      return ApiResponse.error(
        message: 'An error occurred during registration',
      );
    }
  }

  // Save auth data to shared preferences
  Future<void> saveAuthData(
      User user, String token, bool rememberMe, String email, String password,
      {String? mobile}) async {
    Logger.info('Saving auth data to SharedPreferences');
    Logger.info('Remember Me: $rememberMe');
    final prefs = await SharedPreferences.getInstance();

    // Always save the token and user data for the current session
    await prefs.setString(_tokenKey, token);
    await prefs.setString(_userKey, jsonEncode(user.toJson()));
    await prefs.setBool('isLoggedIn', true);

    // Save access token and refresh token if available
    if (user.accessToken != null) {
      await prefs.setString('access_token', user.accessToken!);
    }
    if (user.refreshToken != null) {
      await prefs.setString('refresh_token', user.refreshToken!);
    }

    // Only save credentials if remember me is checked
    if (rememberMe) {
      Logger.info('Remember Me is checked, saving credentials');
      await prefs.setString(_emailKey, email);
      await prefs.setString(_passwordKey, password);
      if (mobile != null) {
        await prefs.setString(_mobileKey, mobile);
        Logger.info('Mobile number saved: $mobile');
      }
      Logger.info('Credentials saved for: $email');
    } else {
      // Clear saved credentials if remember me is unchecked
      Logger.info('Remember Me is unchecked, clearing credentials');
      await prefs.remove(_emailKey);
      await prefs.remove(_passwordKey);
      await prefs.remove(_mobileKey);
    }

    // Always save the remember me preference
    await prefs.setBool(_rememberMeKey, rememberMe);
    Logger.info('Remember Me preference saved: $rememberMe');

    // Verify the data was saved correctly
    final savedRememberMe = prefs.getBool(_rememberMeKey);
    final savedEmail = prefs.getString(_emailKey);
    Logger.info(
        'Verification - Remember Me: $savedRememberMe, Email: $savedEmail');

    Logger.info('Auth data saved for user: ${user.email}');
  }

  // Get saved auth data
  Future<ApiResponse<SavedAuthData>> getSavedAuthData() async {
    Logger.info('Getting saved auth data from SharedPreferences');
    final prefs = await SharedPreferences.getInstance();

    // Log all keys in SharedPreferences for debugging
    final keys = prefs.getKeys();
    Logger.info('All keys in SharedPreferences: $keys');

    final token = prefs.getString(_tokenKey);
    final userJson = prefs.getString(_userKey);
    final rememberMe = prefs.getBool(_rememberMeKey) ?? false;
    final email = prefs.getString(_emailKey);
    final password = prefs.getString(_passwordKey);
    final mobile = prefs.getString(_mobileKey);
    final accessToken = prefs.getString('access_token');
    final refreshToken = prefs.getString('refresh_token');
    final isLoggedIn = prefs.getBool('isLoggedIn') ?? false;

    Logger.info('Retrieved from SharedPreferences:');
    Logger.info('- Remember Me: $rememberMe');
    Logger.info('- Email: $email');
    Logger.info('- Password: ${password != null ? '******' : 'null'}');
    Logger.info('- Mobile: $mobile');
    Logger.info('- Token exists: ${token != null}');
    Logger.info('- Access Token exists: ${accessToken != null}');
    Logger.info('- Refresh Token exists: ${refreshToken != null}');
    Logger.info('- Is Logged In: $isLoggedIn');
    Logger.info('- User JSON exists: ${userJson != null}');

    if ((token != null || accessToken != null) && userJson != null) {
      try {
        User user = User.fromJson(jsonDecode(userJson));

        // If we have access token but it's not in the user object, update it
        if (user.accessToken == null && accessToken != null) {
          user = user.copyWith(
              accessToken: accessToken, refreshToken: refreshToken);
        }

        final savedAuthData = SavedAuthData(
          isLoggedIn:
              isLoggedIn || true, // Use stored isLoggedIn or default to true
          user: user,
          token: token ?? accessToken, // Use token or fall back to accessToken
          rememberMe: rememberMe,
          email: email,
          password: password,
          mobile: mobile,
          accessToken: accessToken,
          refreshToken: refreshToken,
        );

        return ApiResponse.success(data: savedAuthData);
      } catch (e) {
        Logger.error('Error parsing saved user data: $e');
      }
    }

    final savedAuthData = SavedAuthData(
      isLoggedIn: isLoggedIn,
      rememberMe: rememberMe,
      email: email,
      password: password,
      mobile: mobile,
      accessToken: accessToken,
      refreshToken: refreshToken,
    );

    return ApiResponse.success(data: savedAuthData);
  }

  // Logout
  Future<ApiResponse<LogoutData>> logout() async {
    try {
      Logger.info('Attempting to logout user');

      // Get a valid token
      final token = await getValidToken();
      LogoutData? logoutData;

      if (token != null) {
        try {
          // Create auth options
          final options = createAuthOptions(token);

          // Make the API call to logout
          final response = await dio.post(
            Environment.logoutUrl,
            options: options,
          );

          Logger.info('Logout API response status: ${response.statusCode}');
          Logger.info('Logout API response data: ${response.data}');

          // Parse response using LogoutModel if successful
          if (response.statusCode == 200) {
            final logoutModel = LogoutModel.fromJson(response.data);
            logoutData = LogoutData.fromLogoutModel(logoutModel);
          }
        } catch (e) {
          // Even if the API call fails, we still want to clear local data
          Logger.error('Error calling logout API: $e');
        }
      }

      // Get shared preferences
      final prefs = await SharedPreferences.getInstance();
      final rememberMe = prefs.getBool(_rememberMeKey) ?? false;

      // Store email and password temporarily if remember me is checked
      final savedEmail = rememberMe ? prefs.getString(_emailKey) : null;
      final savedPassword = rememberMe ? prefs.getString(_passwordKey) : null;
      final savedMobile = rememberMe ? prefs.getString(_mobileKey) : null;

      // Clear all SharedPreferences values
      await prefs.clear();
      Logger.info('All SharedPreferences values cleared');

      // If remember me is checked, restore email and password
      if (rememberMe) {
        await prefs.setBool(_rememberMeKey, true);
        if (savedEmail != null) {
          await prefs.setString(_emailKey, savedEmail);
        }
        if (savedPassword != null) {
          await prefs.setString(_passwordKey, savedPassword);
        }
        if (savedMobile != null) {
          await prefs.setString(_mobileKey, savedMobile);
        }
        Logger.info('Remember Me is checked, restored credentials');
      }

      Logger.info('User logged out successfully');

      // Create default logout data if API call didn't provide one
      logoutData ??= LogoutData(
        success: true,
        message: 'Logged out successfully',
      );

      return ApiResponse.success(
        data: logoutData,
        message: logoutData.message ?? 'Logged out successfully',
      );
    } catch (e) {
      Logger.error('Error during logout: $e');
      return ApiResponse.error(
        message: 'An error occurred during logout',
      );
    }
  }

  /// Get current user profile information
  Future<ApiResponse<UserProfileData>> getUserProfile() async {
    try {
      Logger.info('Fetching user profile information');

      // Get a valid token
      final token = await getValidToken();

      if (token == null) {
        Logger.error('No valid token available to fetch user profile');
        return ApiResponse.error(message: 'Authentication required');
      }

      // Get user ID
      final userId = await getUserId();

      if (userId == null) {
        Logger.error('No user ID available to fetch user profile');
        return ApiResponse.error(message: 'User ID not found');
      }

      // Create auth options
      final options = createAuthOptions(token);

      // Make the API call with user ID in the URL
      final response = await dio.get(
        '${Environment.userProfileUrl}$userId',
        options: options,
      );

      Logger.info('User profile API response status: ${response.statusCode}');
      Logger.info('User profile API response data: ${response.data}');

      if (response.statusCode == 200) {
        // Parse response using UserProfileModel
        final userProfileModel = UserProfileModel.fromJson(response.data);

        // Create UserProfileData from UserProfileModel
        final userProfileData =
            UserProfileData.fromUserProfileModel(userProfileModel);

        // Update the stored user data (for backward compatibility)
        if (userProfileData.user != null) {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString(
              _userKey, jsonEncode(userProfileData.user!.toJson()));

          // Save user ID separately for easy access in API calls
          if (userProfileModel.userId != null) {
            await prefs.setString('user_id', userProfileModel.userId!);
            Logger.info(
                'User ID saved to SharedPreferences: ${userProfileModel.userId}');
          }
        }

        return ApiResponse.success(
          data: userProfileData,
          statusCode: response.statusCode,
        );
      } else {
        final errorMessage =
            response.data['message'] ?? 'Failed to fetch user profile';
        Logger.error('Failed to fetch user profile: $errorMessage');
        return ApiResponse.error(
          message: errorMessage,
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      Logger.error('Error fetching user profile: $e');
      return ApiResponse.error(
        message: 'An error occurred while fetching user profile',
      );
    }
  }

  // Validation methods moved to Validators utility class

  // Refresh token
  Future<ApiResponse<AuthData>> refreshToken(String refreshToken) async {
    try {
      Logger.info('Attempting to refresh token');

      // Prepare the request payload
      final payload = {
        'refresh_token': refreshToken,
      };

      // Make the API call
      final response = await dio.post(
        Environment.refreshTokenUrl,
        data: jsonEncode(payload),
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info('Refresh token API response status: ${response.statusCode}');
      Logger.info('Refresh token API response data: ${response.data}');

      // Check if the refresh was successful
      if (response.statusCode == 200) {
        // Parse response using LoginModel
        final loginModel = LoginModel.fromJson(response.data);

        // Create AuthData from LoginModel
        final authData = AuthData.fromLoginModel(loginModel);

        // Save the new tokens
        final prefs = await SharedPreferences.getInstance();
        if (authData.token != null) {
          await prefs.setString('access_token', authData.token!);
        }
        if (authData.refreshToken != null) {
          await prefs.setString('refresh_token', authData.refreshToken!);
        }
        if (authData.user != null) {
          await prefs.setString(_userKey, jsonEncode(authData.user!.toJson()));
        }

        Logger.info('Token refreshed successfully');

        return ApiResponse.success(
          data: authData,
          statusCode: response.statusCode,
        );
      } else {
        // Refresh failed
        final errorMessage = response.data['message'] ?? 'Token refresh failed';
        Logger.error('Token refresh failed: $errorMessage');
        return ApiResponse.error(
          message: errorMessage,
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      Logger.error('Token refresh error: $e');
      return ApiResponse.error(
        message: 'An error occurred during token refresh',
      );
    }
  }

  // Check if token is expired or about to expire
  bool isTokenExpired(String token) {
    try {
      final decodedToken = _decodeJwtToken(token);

      // Get expiration time from token
      final expTime = decodedToken['exp'];
      if (expTime == null) {
        return true; // If no expiration time, consider it expired
      }

      // Convert to DateTime
      final expirationDate =
          DateTime.fromMillisecondsSinceEpoch(expTime * 1000);

      // Check if token is expired or will expire in the next 5 minutes
      final now = DateTime.now();
      final fiveMinutesFromNow = now.add(const Duration(minutes: 5));

      return expirationDate.isBefore(fiveMinutesFromNow);
    } catch (e) {
      Logger.error('Error checking token expiration: $e');
      return true; // Consider expired if there's an error
    }
  }

  // Get a valid token, refreshing if necessary
  @override
  Future<String?> getValidToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final accessToken = prefs.getString('access_token');
      final refreshToken = prefs.getString('refresh_token');

      // If no tokens, return null
      if (accessToken == null || refreshToken == null) {
        return null;
      }

      // Check if token is expired or about to expire
      if (isTokenExpired(accessToken)) {
        Logger.info(
            'Access token is expired or about to expire, refreshing...');

        // Refresh the token
        final refreshResult = await this.refreshToken(refreshToken);

        if (refreshResult.success && refreshResult.data != null) {
          return refreshResult.data!.token;
        } else {
          // If refresh failed, return null
          return null;
        }
      }

      // Token is still valid
      return accessToken;
    } catch (e) {
      Logger.error('Error getting valid token: $e');
      return null;
    }
  }

  // Get user ID from SharedPreferences
  @override
  Future<String?> getUserId() async {
    try {
      // final prefs = await SharedPreferences.getInstance();
      // final userId = prefs.getString('user_id');
      final data = await getSavedAuthData();
      final userId = data.data?.user?.id;
      Logger.info('Retrieved user ID from SharedPreferences: $userId');
      return userId;
    } catch (e) {
      Logger.error('Error getting user ID from SharedPreferences: $e');
      return null;
    }
  }

  // Decode JWT token to extract user information
  Map<String, dynamic> _decodeJwtToken(String token) {
    try {
      Logger.info('Decoding JWT token');

      // Split the token into parts
      final parts = token.split('.');
      if (parts.length != 3) {
        Logger.error('Invalid JWT token format');
        return {};
      }

      // Decode the payload (second part)
      String payload = parts[1];

      // Add padding if needed
      while (payload.length % 4 != 0) {
        payload += '=';
      }

      // Replace characters that are different in base64Url vs base64
      payload = payload.replaceAll('-', '+').replaceAll('_', '/');

      // Decode the base64
      final decoded = utf8.decode(base64Url.decode(payload));
      final Map<String, dynamic> decodedJson = jsonDecode(decoded);

      Logger.info('JWT token decoded successfully');
      return decodedJson;
    } catch (e) {
      Logger.error('Error decoding JWT token: $e');
      return {};
    }
  }
}
