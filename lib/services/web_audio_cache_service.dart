import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:nsl/utils/logger.dart';

// Conditional import for web-specific functionality
import 'web_audio_cache_service_stub.dart'
    if (dart.library.html) 'web_audio_cache_service_web.dart';

/// A service for caching audio files in the browser for web playback
/// This service uses the browser's IndexedDB to store audio data
/// and creates blob URLs for playback
class WebAudioCacheService {
  // Singleton instance
  static final WebAudioCacheService _instance =
      WebAudioCacheService._internal();

  // Factory constructor
  factory WebAudioCacheService() => _instance;

  // Internal constructor
  WebAudioCacheService._internal();

  // Map to store blob URLs by cache key
  final Map<String, String> _blobUrls = {};

  // Map to store audio data by cache key
  final Map<String, Uint8List> _audioData = {};

  /// Store audio data in the cache
  ///
  /// [cacheKey] - The key to store the audio data under
  /// [audioData] - The audio data to store
  ///
  /// Returns a blob URL that can be used for playback
  Future<String> storeAudioData(String cacheKey, Uint8List audioData) async {
    try {
      // Store the audio data in memory
      _audioData[cacheKey] = audioData;

      // Create a blob URL using platform-specific operations
      final blobUrl = WebBlobOperations.createBlobUrl(audioData, 'audio/wav');

      // Store the blob URL
      _blobUrls[cacheKey] = blobUrl;

      // Logger.info('Stored audio data with cache key: $cacheKey, blob URL: $blobUrl');

      return blobUrl;
    } catch (e) {
      Logger.error('Error storing audio data: $e');
      rethrow;
    }
  }

  /// Get a blob URL for a cache key
  ///
  /// [cacheKey] - The cache key to get the blob URL for
  ///
  /// Returns the blob URL if it exists, null otherwise
  String? getBlobUrl(String cacheKey) {
    return _blobUrls[cacheKey];
  }

  /// Get audio data for a cache key
  ///
  /// [cacheKey] - The cache key to get the audio data for
  ///
  /// Returns the audio data if it exists, null otherwise
  Uint8List? getAudioData(String cacheKey) {
    return _audioData[cacheKey];
  }

  /// Check if a cache key exists
  ///
  /// [cacheKey] - The cache key to check
  ///
  /// Returns true if the cache key exists, false otherwise
  bool hasCacheKey(String cacheKey) {
    return _blobUrls.containsKey(cacheKey);
  }

  /// Remove a cache key and its associated data
  ///
  /// [cacheKey] - The cache key to remove
  void removeCacheKey(String cacheKey) {
    // Revoke the blob URL if it exists
    final blobUrl = _blobUrls[cacheKey];
    if (blobUrl != null) {
      WebBlobOperations.revokeBlobUrl(blobUrl);
    }

    // Remove the blob URL and audio data
    _blobUrls.remove(cacheKey);
    _audioData.remove(cacheKey);

    Logger.info('Removed cache key: $cacheKey');
  }

  /// Clear all cached audio data
  void clearCache() {
    // Revoke all blob URLs
    for (final blobUrl in _blobUrls.values) {
      WebBlobOperations.revokeBlobUrl(blobUrl);
    }

    // Clear the maps
    _blobUrls.clear();
    _audioData.clear();

    Logger.info('Cleared audio cache');
  }

  /// Extract the cache key from a cached URL
  ///
  /// [cachedUrl] - The cached URL to extract the cache key from
  ///
  /// Returns the cache key if the URL is a cached URL, null otherwise
  String? extractCacheKey(String cachedUrl) {
    if (cachedUrl.startsWith('cached://')) {
      return cachedUrl.substring(9); // Remove 'cached://' prefix
    }
    return null;
  }
}
