import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/providers/workflow_data_provider.dart';
import 'widgets/responsive_login_builder.dart';
import 'package:provider/provider.dart';
import 'providers/auth_provider.dart';
import 'providers/books_provider.dart';
import 'providers/build_provider.dart';
import 'providers/chat_provider.dart';
import 'providers/global_objectives_provider.dart';
import 'providers/language_provider.dart';
import 'providers/responsive_state_provider.dart';
import 'providers/screen_content_provider.dart';
import 'providers/theme_provider.dart';
import 'providers/transaction_provider.dart';
import 'providers/workflow_provider.dart';
import 'routes/app_routes.dart';

import 'theme/theme.dart';
import 'utils/constants.dart';
import 'utils/navigation_service.dart';
import 'l10n/app_localizations.dart';
import 'widgets/common/nsl_knowledge_loader.dart';

/// The main app class
class App extends StatelessWidget {
  const App({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => LanguageProvider()),
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => BooksProvider()),
        ChangeNotifierProvider(create: (_) => ChatProvider()),
        ChangeNotifierProvider(create: (_) => BuildProvider()),
        ChangeNotifierProvider(create: (_) => GlobalObjectivesProvider()),
        ChangeNotifierProvider(create: (_) => TransactionProvider()),
        ChangeNotifierProvider(create: (_) => WorkflowProvider()),
        ChangeNotifierProvider(create: (_) => ScreenContentProvider()),
        ChangeNotifierProvider(create: (_) => WebHomeProvider()),
        ChangeNotifierProvider(create: (_) => ResponsiveStateProvider()),
        ChangeNotifierProvider(create: (_) => WorkflowDataProvider()),
      ],
      child: Consumer3<ThemeProvider, LanguageProvider, AuthProvider>(
        builder: (context, themeProvider, languageProvider, authProvider, _) {
          return MaterialApp(
            locale: languageProvider.locale,
            localizationsDelegates: [
              FlutterQuillLocalizations.delegate,
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('en', 'US'), // English
              Locale('es', 'ES'), // Spanish
              Locale('fr', 'FR'), // French
              Locale('de', 'DE'), // German
              Locale('zh', 'CN'), // Chinese
              Locale('ja', 'JP'), // Japanese
              Locale('ko', 'KR'), // Korean
              Locale('ru', 'RU'), // Russian
              Locale('ar', 'SA'), // Arabic
              Locale('hi', 'IN'), // Hindi
              Locale('te', 'IN'), // Telugu
            ],
            title: AppConstants.appName,
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme(),
            darkTheme: AppTheme.darkTheme(),
            themeMode: themeProvider.themeMode,
            navigatorKey: NavigationService.navigatorKey,
            initialRoute: AppRoutes.login,
            routes: AppRoutes.getRoutes(),
            onGenerateRoute: AppRoutes.generateRoute,
            onUnknownRoute: (settings) => MaterialPageRoute(
              builder: (_) => Scaffold(
                appBar: AppBar(
                  title: Text(AppLocalizations.of(context)
                      .translate('common.pageNotFound')),
                ),
                body: Center(
                  child: Text(
                    AppLocalizations.of(context).translate(
                      'common.pageNotFoundMessage',
                      args: {'pageName': settings.name ?? ''},
                    ),
                  ),
                ),
              ),
            ),
            home: NSLKnowledgeLoaderWrapper(
              isLoading: authProvider.isLoadingAuthData,
              text: 'Loading NSL...',
              circularProgressColor: const Color.fromARGB(255, 232, 125, 31),
              child: const ResponsiveLoginBuilder(),
            ),
          );
        },
      ),
    );
  }
}
