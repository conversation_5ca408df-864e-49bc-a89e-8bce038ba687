import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

/// A reusable chat action icon widget that displays an SVG icon with hover effects.
///
/// This widget is used for various chat actions like copy, thumbs up, thumbs down,
/// and text-to-speech in the chat interface.
class ChatActionIcon extends StatefulWidget {
  /// The asset path to the SVG icon.
  final String iconPath;

  /// The callback function when the icon is tapped.
  final VoidCallback onTap;

  /// The width of the icon. Defaults to 18.
  final double width;

  /// The height of the icon. Defaults to 18.
  final double height;

  /// The color of the icon. Defaults to Colors.grey.shade600.
  final Color? color;

  /// The tooltip text to display on hover. Optional.
  final String? tooltip;

  const ChatActionIcon({
    super.key,
    required this.iconPath,
    required this.onTap,
    this.width = 12,
    this.height = 12,
    this.color,
    this.tooltip,
  });

  @override
  State<ChatActionIcon> createState() => _ChatActionIconState();
}

class _ChatActionIconState extends State<ChatActionIcon> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    // final color = widget.color ?? Colors.grey.shade600;
    // final hoverColor = Theme.of(context).colorScheme.primary;

    final iconWidget = InkWell(
      hoverColor: Color(0xffE4EDFF),
      onTap: widget.onTap,
      child: MouseRegion(
        onEnter: (_) => setState(() => isHovered = true),
        onExit: (_) => setState(() => isHovered = false),
        cursor: SystemMouseCursors.click,
        child: Padding(
          padding: const EdgeInsets.all(6.0),
          child: SvgPicture.asset(
            widget.iconPath,
            width: widget.width,
            height: widget.height,
            // colorFilter: ColorFilter.mode(
            //   isHovered ? hoverColor : color,
            //   BlendMode.srcIn,
            // ),
          ),
        ),
      ),
    );

    // Wrap with tooltip if provided
    if (widget.tooltip != null) {
      return Tooltip(
        message: widget.tooltip!,
        child: iconWidget,
      );
    }

    return iconWidget;
  }
}
