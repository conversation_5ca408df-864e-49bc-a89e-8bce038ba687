import 'package:flutter/material.dart';
import '../models/workflow.dart';
import '../utils/input_value_store.dart';
import '../utils/logger.dart';
import '../ui_components/inputs/app_text_field.dart';
import '../ui_components/pickers/app_date_picker.dart';
import '../ui_components/theme/app_theme.dart';

class InputFieldWidget extends StatefulWidget {
  final InputField field;
  final bool readOnly;
  final String sectionId;
  final Function(String, String, dynamic, String)? onValueChanged;
  final Function(String)? onChatValueSubmitted;
  final bool isChatView;

  const InputFieldWidget({
    super.key,
    required this.field,
    required this.sectionId,
    this.readOnly = false,
    this.onValueChanged,
    this.onChatValueSubmitted,
    this.isChatView = false,
  });

  @override
  State<InputFieldWidget> createState() => InputFieldWidgetState();
}

// Make the state class public so it can be referenced with GlobalKey
class InputFieldWidgetState extends State<InputFieldWidget> {
  dynamic _value;
  final TextEditingController _textController = TextEditingController();
  String? _errorText;
  final _formKey = GlobalKey<FormState>();
  bool _obscureText = true; // For password fields

  // Get the global input value store
  final _store = InputValueStore();

  List<DropdownOption> _dropdownOptions = [];

  @override
  void didUpdateWidget(InputFieldWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Log when readOnly state changes
    if (widget.readOnly != oldWidget.readOnly) {
      Logger.info(
          'InputFieldWidget readOnly changed for ${widget.field.displayName}: ${oldWidget.readOnly} -> ${widget.readOnly}');
      Logger.info('Current value: $_value');

      // Make sure the text controller is updated with the current value
      if (_value != null && _value is String) {
        _textController.text = _value.toString();
      }
    }

    // Check if this is a dependent field and if parent values have changed
    if (widget.field.needsParentValue == true &&
        widget.field.parentIds != null &&
        widget.field.parentIds!.isNotEmpty &&
        widget.field.dependencyType == 'calculation') {
      // Check if all parent values are available
      if (_store.hasAllParentValues(
          widget.sectionId, widget.field.parentIds!)) {
        // Get parent values
        final parentValues =
            _store.getParentValues(widget.sectionId, widget.field.parentIds!);

        // For calculation dependencies, we can perform a simple calculation
        // For this example, if we have start_date and end_date, calculate the number of days
        if (widget.field.displayName == 'Number of Days' &&
            parentValues.containsKey('in003') &&
            parentValues.containsKey('in004')) {
          final startDateStr = parentValues['in003'] as String?;
          final endDateStr = parentValues['in004'] as String?;

          if (startDateStr != null && endDateStr != null) {
            try {
              final startDate = parseDate(startDateStr);
              final endDate = parseDate(endDateStr);

              if (startDate != null && endDate != null) {
                // Calculate the difference in days
                final difference = endDate.difference(startDate).inDays +
                    1; // Include both start and end days

                // Only update if the value has changed
                if (_value != difference) {
                  setState(() {
                    _value = difference;
                    _textController.text = difference.toString();
                  });

                  // Store the calculated value
                  _storeValue(difference);

                  Logger.info(
                      'Updated calculated ${widget.field.displayName}: $difference days');
                }
              }
            } catch (e) {
              Logger.error('Error calculating days: $e');
            }
          }
        }
      }
    }
  }

  // Method to validate required fields - can be called when form is submitted
  void validateRequiredField() {
    if (widget.field.required &&
        (_value == null || (_value is String && (_value as String).isEmpty))) {
      setState(() {
        _errorText = '${widget.field.displayName} is required';
      });
    }
  }

  // Public method to get the current value
  dynamic getValue() {
    return _value;
  }

  // Helper method to store value in the InputValueStore
  void _storeValue(dynamic value) {
    if (value == null) return;

    // Store using the new format (sectionId, fieldId, value)
    _store.setValue(widget.sectionId, widget.field.attributeId, value);
    Logger.info(
        'Stored value for ${widget.sectionId}:${widget.field.attributeId}: $value (${value.runtimeType})');

    // If this field is a parent for any dependent fields, we should notify them
    // In a real implementation, this would be handled by a more sophisticated state management system
    // For now, we'll just log that we would do this
    Logger.info(
        'Would notify dependent fields that ${widget.field.attributeId} has changed');
  }

  // Helper method to format time in HH:MM:SS format
  String _formatTime(int seconds) {
    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final remainingSeconds = seconds % 60;

    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  // Helper method to build dropdown items from either allowedValues or dropdownOptions
  List<DropdownMenuItem<String>>? _buildDropdownItems() {
    // If we have dropdown options from the new API format, use those
    if (_dropdownOptions.isNotEmpty) {
      Logger.info('Using dropdownOptions for ${widget.field.displayName}');
      return _dropdownOptions.map((option) {
        return DropdownMenuItem<String>(
          value: option.label,
          child: Text(option.label),
        );
      }).toList();
    }

    // Otherwise, fall back to the old allowedValues format
    if (widget.field.allowedValues != null &&
        widget.field.allowedValues!.isNotEmpty) {
      Logger.info('Using allowedValues for ${widget.field.displayName}');
      return widget.field.allowedValues!.map((value) {
        return DropdownMenuItem<String>(
          value: value,
          child: Text(value),
        );
      }).toList();
    }

    // Return empty list if neither is available
    return [];
  }

  // Helper method to build multi-select dropdown items, filtering out already selected values
  List<DropdownMenuItem<String>>? _buildMultiSelectDropdownItems(
      List<String> selectedValues) {
    // If we have dropdown options from the new API format, use those
    if (widget.field.dropdownOptions != null &&
        widget.field.dropdownOptions!.isNotEmpty) {
      Logger.info(
          'Using dropdownOptions for multi-select ${widget.field.displayName}');
      return widget.field.dropdownOptions!
          .where((option) => !selectedValues.contains(option.value))
          .map((option) {
        return DropdownMenuItem<String>(
          value: option.value,
          child: Text(option.label),
        );
      }).toList();
    }

    // Otherwise, fall back to the old allowedValues format
    if (widget.field.allowedValues != null &&
        widget.field.allowedValues!.isNotEmpty) {
      Logger.info(
          'Using allowedValues for multi-select ${widget.field.displayName}');
      return widget.field.allowedValues!
          .where((value) => !selectedValues.contains(value))
          .map((value) {
        return DropdownMenuItem<String>(
          value: value,
          child: Text(value),
        );
      }).toList();
    }

    // Return empty list if neither is available
    return [];
  }

  // Public method to set the value
  void setValue(dynamic value) {
    if (value == null) return;

    // Check if the value has actually changed
    if (_value == value) return;

    // Update the value
    _value = value;

    // Handle different types of values
    if (_value is String) {
      _textController.text = _value.toString();
    } else if (_value is num) {
      _textController.text = _value.toString();
    } else if (_value is bool) {
      // For boolean values, no need to update text controller
    } else if (_value is List) {
      // For list values, no need to update text controller
    } else {
      // For other types, convert to string
      _textController.text = _value.toString();
    }

    Logger.info(
        'Set value for ${widget.field.displayName}: $_value (${_value.runtimeType})');

    // Only rebuild if necessary (e.g., for validation errors)
    final newErrorText =
        _value != null && _value is String && (_value as String).isNotEmpty
            ? _validateInput(_value.toString())
            : null;

    if (newErrorText != _errorText) {
      setState(() {
        _errorText = newErrorText;
      });
    }

    // Store the value in the InputValueStore
    _storeValue(value);
  }

  // Public method to get the field name
  String getFieldName() {
    return widget.field.displayName;
  }

  // Getter for the field
  InputField get field => widget.field;

  // Public method to validate the field
  bool validate() {
    // First check if it's required and empty
    if (widget.field.required &&
        (_value == null || (_value is String && (_value as String).isEmpty))) {
      setState(() {
        _errorText = '${widget.field.displayName} is required';
      });
      return false;
    }

    // Then validate the value if it's not empty
    if (_value != null && _value is String && (_value as String).isNotEmpty) {
      final error = _validateInput(_value.toString());
      setState(() {
        _errorText = error;
      });
      return error == null;
    }

    return true;
  }

  @override
  void initState() {
    super.initState();

    _dropdownOptions = widget.field.dropdownOptions ?? [];
    _value = widget.field.inputValue;

    // Check if we have a stored value for this field
    if (_store.hasValue(widget.sectionId, widget.field.attributeId)) {
      _value = _store.getValue(widget.sectionId, widget.field.attributeId);
      Logger.info(
          'Loaded stored value for ${widget.field.displayName}: $_value');
    }
    // For dependent fields, check if we need to handle dependencies
    // else if (widget.field.needsParentValue == true &&
    //     widget.field.parentIds != null &&
    //     widget.field.parentIds!.isNotEmpty) {
    //   // Check if all parent values are available
    //   if (_store.hasAllParentValues(
    //       widget.sectionId, widget.field.parentIds!)) {
    //     // Get parent values
    //     final parentValues =
    //         _store.getParentValues(widget.sectionId, widget.field.parentIds!);
    //     Logger.info(
    //         'Parent values for ${widget.field.displayName}: $parentValues');
    //
    //     // Handle different dependency types
    //     if (widget.field.dependencyType == 'dropdown') {
    //       // TODO: In a real implementation, we would make an API call here to fetch
    //       // the dropdown options based on the parent values
    //       // For now, we'll just log that we would do this
    //       Logger.info(
    //           'Would fetch dropdown options for ${widget.field.displayName} based on parent values: $parentValues');
    //     } else if (widget.field.dependencyType == 'calculation') {
    //       // For calculation dependencies, we can perform a simple calculation
    //       // In a real implementation, this would be more complex and possibly involve an API call
    //       // For this example, if we have start_date and end_date, calculate the number of days
    //       if (widget.field.displayName == 'Number of Days' &&
    //           parentValues.containsKey('in003') &&
    //           parentValues.containsKey('in004')) {
    //         final startDateStr = parentValues['in003'] as String?;
    //         final endDateStr = parentValues['in004'] as String?;
    //
    //         if (startDateStr != null && endDateStr != null) {
    //           try {
    //             final startDate = parseDate(startDateStr);
    //             final endDate = parseDate(endDateStr);
    //
    //             if (startDate != null && endDate != null) {
    //               // Calculate the difference in days
    //               final difference = endDate.difference(startDate).inDays +
    //                   1; // Include both start and end days
    //
    //               // Set the calculated value
    //               _value = difference;
    //               _textController.text = difference.toString();
    //
    //               // Store the calculated value
    //               _storeValue(difference);
    //
    //               Logger.info(
    //                   'Calculated ${widget.field.displayName}: $difference days');
    //             }
    //           } catch (e) {
    //             Logger.error('Error calculating days: $e');
    //           }
    //         }
    //       }
    //     }
    //   } else {
    //     Logger.info(
    //         'Not all parent values available for ${widget.field.displayName}');
    //   }
    //  }

    // Initialize controller with value if available
    if (_value != null) {
      if (_value is String) {
        _textController.text = _value.toString();
      } else if (_value is num) {
        _textController.text = _value.toString();
      } else {
        _textController.text = _value.toString();
      }
    }

    // Log initialization
    Logger.info(
        'Initializing InputFieldWidget for ${widget.field.displayName}, sectionId: ${widget.sectionId}, readOnly: ${widget.readOnly}');
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  // Validate input based on field validations
  String? _validateInput(String? value) {
    // Don't show required error for empty fields by default
    // We'll only validate non-empty values
    if (value == null || value.isEmpty) {
      // We won't show required errors here - they'll only show when the form is submitted
      // or when validateRequiredField() is called explicitly
      return null;
    }

    // Special validation for date picker
    if (widget.field.uiControl == 'oj-input-date-picker') {
      // Validate date format YYYY-MM-DD
      final dateRegex =
          RegExp(r'^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$');
      if (!dateRegex.hasMatch(value)) {
        return 'Please enter a valid date in YYYY-MM-DD format';
      }

      // Further validate the date is real (e.g., not 2023-02-31)
      try {
        final parts = value.split('-');
        final year = int.parse(parts[0]);
        final month = int.parse(parts[1]);
        final day = int.parse(parts[2]);

        final date = DateTime(year, month, day);

        // Check if the date is valid by comparing components
        // This catches invalid dates like 2023-02-31
        if (date.month != month || date.day != day || date.year != year) {
          return 'Please enter a valid date';
        }
      } catch (e) {
        return 'Please enter a valid date';
      }
    }

    // Check validations if they exist
    if (widget.field.validations != null &&
        widget.field.validations!.isNotEmpty) {
      for (final validation in widget.field.validations!) {
        if (validation == null) continue;

        // Check for min length validation
        if (validation.containsKey('minLength')) {
          final minLength = validation['minLength'];
          if (minLength is int && value.length < minLength) {
            return '${widget.field.displayName} must be at least $minLength characters';
          }
        }

        // Check for max length validation
        if (validation.containsKey('maxLength')) {
          final maxLength = validation['maxLength'];
          if (maxLength is int && value.length > maxLength) {
            return '${widget.field.displayName} must be at most $maxLength characters';
          }
        }

        // Check for pattern validation
        if (validation.containsKey('pattern')) {
          final pattern = validation['pattern'];
          if (pattern is String) {
            try {
              final regex = RegExp(pattern);
              if (!regex.hasMatch(value)) {
                return validation['message'] ??
                    '${widget.field.displayName} format is invalid';
              }
            } catch (e) {
              // Invalid regex pattern
              Logger.error('Invalid regex pattern: $pattern');
            }
          }
        }

        // Check for numeric range validation
        if (widget.field.dataType == 'Number' ||
            widget.field.dataType == 'Integer') {
          try {
            final numValue = num.parse(value);

            // Check minimum value
            if (validation.containsKey('minimum')) {
              final minimum = validation['minimum'];
              if (minimum is num && numValue < minimum) {
                return '${widget.field.displayName} must be at least $minimum';
              }
            }

            // Check maximum value
            if (validation.containsKey('maximum')) {
              final maximum = validation['maximum'];
              if (maximum is num && numValue > maximum) {
                return '${widget.field.displayName} must be at most $maximum';
              }
            }
          } catch (e) {
            return '${widget.field.displayName} must be a valid number';
          }
        }
      }
    }

    return null;
  }

  // Helper method to parse date string in YYYY-MM-DD format
  DateTime? parseDate(String dateStr) {
    try {
      final parts = dateStr.split('-');
      if (parts.length == 3) {
        final year = int.tryParse(parts[0]);
        final month = int.tryParse(parts[1]);
        final day = int.tryParse(parts[2]);
        if (year != null && month != null && day != null) {
          return DateTime(year, month, day);
        }
      }
    } catch (e) {
      Logger.warning('Failed to parse date: $dateStr');
    }
    return null;
  }

  // Helper method to build a read-only label for system inputs, info inputs, and calculation dependent fields
  Widget _buildReadOnlyLabel() {
    // Format the value based on data type
    String displayValue = '';

    // First check if we have a value in the local store
    if (_value != null) {
      displayValue = _value.toString();
    }
    // Then check if we have a value from the API
    else if (widget.field.inputValue != null) {
      displayValue = widget.field.inputValue.toString();
    }

    // Format date values if needed
    if (widget.field.dataType?.toLowerCase() == 'date' &&
        displayValue.isNotEmpty) {
      final date = parseDate(displayValue);
      if (date != null) {
        // Format as YYYY-MM-DD
        displayValue =
            '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      }
    }

    // Add special styling for calculation dependent fields
    final bool isCalculationField =
        widget.field.dependencyType == 'calculation';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Text(
          widget.field.displayName,
          style: AppTheme.bodyMedium.copyWith(
            fontWeight: FontWeight.w500,
            color: isCalculationField ? AppTheme.primaryColor : null,
          ),
        ),
        const SizedBox(height: AppTheme.spacingXs),

        // Value
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            color: isCalculationField
                ? AppTheme.primaryColor
                    .withAlpha(13) // 5% opacity (0.05 * 255 ≈ 13)
                : AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusM),
            border: Border.all(
              color: isCalculationField
                  ? AppTheme.primaryColor
                      .withAlpha(77) // 30% opacity (0.3 * 255 ≈ 77)
                  : Colors.grey[300]!,
            ),
          ),
          child: Text(
            displayValue,
            style: AppTheme.bodyMedium.copyWith(
              fontWeight: isCalculationField ? FontWeight.w500 : null,
              color: isCalculationField ? AppTheme.primaryColor : null,
            ),
          ),
        ),
      ],
    );
  }

  // Helper method to show time picker dialog
  Future<void> _showTimePickerDialog() async {
    final TimeOfDay? pickedTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppTheme.primaryColor,
              onPrimary: Colors.white,
              onSurface: AppTheme.textPrimaryColor,
            ),
          ),
          child: child!,
        );
      },
    );

    if (!mounted) return;

    if (pickedTime != null) {
      // Format the time as HH:MM
      final formattedTime =
          '${pickedTime.hour.toString().padLeft(2, '0')}:${pickedTime.minute.toString().padLeft(2, '0')}';
      setState(() {
        _value = formattedTime;
        _textController.text = formattedTime;
        _errorText = _validateInput(formattedTime);
      });

      // Store the value in the global store
      _storeValue(formattedTime);

      // Handle chat view submission
      if (widget.isChatView && widget.onChatValueSubmitted != null) {
        widget.onChatValueSubmitted!(formattedTime);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    Logger.info(
        'Building InputFieldWidget for ${widget.field.displayName}, type: ${widget.field.dataType}');
    return Padding(
      padding: const EdgeInsets.only(bottom: AppTheme.spacingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // We don't need to show the label here since the AppTextField components
          // already include labels
          _buildInputControl(),
        ],
      ),
    );
  }

  Widget _buildInputControl() {
    // Check if field should be displayed as read-only based on field.readOnly property or widget.readOnly parameter
    // Priority: field.readOnly > widget.readOnly > false
    bool isReadOnly = widget.field.readOnly || widget.readOnly;

    // For calculation dependent fields, always display as read-only labels
    if (widget.field.dependencyType == 'calculation' || isReadOnly) {
      return _buildReadOnlyLabel();
    }

    switch (widget.field.uiControl) {
      case 'oj-input-text':
      case 'oj-input-number':
        // If we have allowed values, show them as suggestions
        if (widget.field.allowedValues != null &&
            widget.field.allowedValues!.isNotEmpty) {
          return Form(
            key: _formKey,
            child: Autocomplete<String>(
              optionsBuilder: (TextEditingValue textEditingValue) {
                if (textEditingValue.text == '') {
                  return const Iterable<String>.empty();
                }
                return widget.field.allowedValues!.where((String option) {
                  return option
                      .toLowerCase()
                      .contains(textEditingValue.text.toLowerCase());
                });
              },
              onSelected: (String selection) {
                setState(() {
                  _value = selection;
                  _errorText = _validateInput(selection);
                });
              },
              fieldViewBuilder: (BuildContext context,
                  TextEditingController controller,
                  FocusNode focusNode,
                  VoidCallback onFieldSubmitted) {
                return AppTextField(
                  controller: controller,
                  focusNode: focusNode,
                  label: widget.field.displayName,
                  placeholder: 'Enter ${widget.field.displayName}',
                  suffix: const Icon(Icons.arrow_drop_down),
                  errorText: _errorText,
                  onChanged: (value) {
                    // Update the value without triggering a rebuild
                    _value = value;

                    // Only update error state if needed
                    final newErrorText =
                        value.isNotEmpty ? _validateInput(value) : null;
                    if (newErrorText != _errorText) {
                      setState(() {
                        _errorText = newErrorText;
                      });
                    }
                  },
                  onSubmitted: (value) {
                    onFieldSubmitted();
                  },
                );
              },
            ),
          );
        } else {
          // Regular text field if no allowed values
          return Form(
            key: _formKey,
            child: AppTextField(
              controller: _textController,
              label: widget.field.displayName,
              placeholder: 'Enter ${widget.field.displayName}',
              errorText: _errorText,
              enabled: !widget.readOnly,
              onChanged: (value) {
                // Update the value without triggering a rebuild for every keystroke
                _value = value;

                // Only update error state if needed
                final newErrorText =
                    value.isNotEmpty ? _validateInput(value) : null;
                if (newErrorText != _errorText) {
                  setState(() {
                    _errorText = newErrorText;
                  });
                }

                // Store the value in the global store
                _storeValue(value);

                // Notify parent about value change
                if (widget.onValueChanged != null) {
                  widget.onValueChanged!(widget.sectionId,
                      widget.field.attributeId, value, widget.field.inputId);
                }
              },
              onSubmitted: (value) {
                // Handle chat view submission
                if (widget.isChatView &&
                    widget.onChatValueSubmitted != null &&
                    value.isNotEmpty) {
                  widget.onChatValueSubmitted!(value);
                }
              },
            ),
          );
        }
      case 'oj-slider':
        // Implement slider using Slider widget with AppTheme styling
        // Extract min, max, and step from validations if available
        double minValue = 0.0;
        double maxValue = 100.0;
        double stepValue = 1.0;

        if (widget.field.validations != null) {
          for (var validation in widget.field.validations!) {
            if (validation != null) {
              if (validation.containsKey('min')) {
                minValue = (validation['min'] as num).toDouble();
              }
              if (validation.containsKey('max')) {
                maxValue = (validation['max'] as num).toDouble();
              }
              if (validation.containsKey('step')) {
                stepValue = (validation['step'] as num).toDouble();
              }
            }
          }
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label
            Text(
              widget.field.displayName,
              style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: AppTheme.spacingXs),
            // Value display
            Text(
              _value?.toString() ?? minValue.toString(),
              style: AppTheme.bodySmall,
            ),
            // Slider
            Slider(
              value: (_value as num?)?.toDouble() ?? minValue,
              min: minValue,
              max: maxValue,
              divisions: ((maxValue - minValue) / stepValue).round(),
              label: _value?.toString() ?? minValue.toString(),
              activeColor: AppTheme.primaryColor,
              inactiveColor: AppTheme.primaryColor.withAlpha(50),
              onChanged: widget.readOnly
                  ? null
                  : (value) {
                      setState(() {
                        _value = value;
                        _errorText =
                            null; // Clear error when a valid value is selected
                      });

                      // Store the value in the global store
                      _storeValue(value);
                      if (widget.onValueChanged != null) {
                        widget.onValueChanged!(
                            widget.sectionId,
                            widget.field.attributeId,
                            value,
                            widget.field.inputId);
                      }
                    },
            ),
            // Error text
            if (_errorText != null)
              Padding(
                padding: const EdgeInsets.only(top: AppTheme.spacingXs),
                child: Text(
                  _errorText!,
                  style:
                      AppTheme.bodySmall.copyWith(color: AppTheme.errorColor),
                ),
              ),
          ],
        );

      case 'oj-date-picker':
      case 'oj-c-input-date-picker':
      case 'oj-input-date-picker':
      case 'oj-input-date':
        // Use AppDatePicker for all date picker variants
        return AppDatePicker(
          selectedDate: _value != null && _value is String
              ? parseDate(_value as String)
              : null,
          firstDate: DateTime(2000),
          lastDate: DateTime(2100),
          label: widget.field.displayName,
          placeholder: 'YYYY-MM-DD',
          errorText: _errorText,
          dateFormat: (date) =>
              '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}',
          enabled: !widget.readOnly,
          border: Border.all(color: AppTheme.primaryColor),
          onDateSelected: (date) {
            final formattedDate =
                '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
            setState(() {
              _value = formattedDate;
              _textController.text = formattedDate;
              _errorText = _validateInput(formattedDate);
            });

            // Store the value in the global store
            _storeValue(formattedDate);

            // Handle chat view submission
            if (widget.isChatView && widget.onChatValueSubmitted != null) {
              widget.onChatValueSubmitted!(formattedDate);
            }

            if (widget.onValueChanged != null) {
              widget.onValueChanged!(widget.sectionId, widget.field.attributeId,
                  formattedDate, widget.field.inputId);
            }
          },
        );

      case 'oj-input-time':
        // Implement time picker using AppTextField with time picker dialog
        return Form(
          key: _formKey,
          child: AppTextField(
            controller: _textController,
            label: widget.field.displayName,
            placeholder: 'HH:MM',
            errorText: _errorText,
            enabled: !widget.readOnly,
            suffix: IconButton(
              icon: const Icon(Icons.access_time),
              onPressed: widget.readOnly
                  ? null
                  : () {
                      _showTimePickerDialog();
                    },
            ),
            onChanged: (value) {
              // Update the value without triggering a rebuild for every keystroke
              _value = value;

              // Only update error state if needed
              final newErrorText =
                  value.isNotEmpty ? _validateInput(value) : null;
              if (newErrorText != _errorText) {
                setState(() {
                  _errorText = newErrorText;
                });
              }

              // Store the value in the global store
              _storeValue(value);
              if (widget.onValueChanged != null) {
                widget.onValueChanged!(widget.sectionId,
                    widget.field.attributeId, value, widget.field.inputId);
              }
            },
            onSubmitted: (value) {
              // Handle chat view submission
              if (widget.isChatView &&
                  widget.onChatValueSubmitted != null &&
                  value.isNotEmpty) {
                widget.onChatValueSubmitted!(value);
              }
            },
          ),
        );

      case 'oj-input-date-time':
        return AppDateTimePicker(
          selectedDateTime: _value as DateTime?,
          firstDate: DateTime(2000),
          lastDate: DateTime(2100),
          label: widget.field.displayName,
          placeholder: 'Select ${widget.field.displayName}',
          errorText: _errorText,
          enabled: !widget.readOnly,
          onDateTimeSelected: (dateTime) {
            setState(() {
              _value = dateTime;
              _errorText = null; // Clear error when a valid date is selected
            });

            // Store the value in the global store
            _storeValue(dateTime);

            // Handle chat view submission
            if (widget.isChatView && widget.onChatValueSubmitted != null) {
              // Format date-time as string for chat view
              final formattedDateTime =
                  '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
              widget.onChatValueSubmitted!(formattedDateTime);
            }
            if (widget.onValueChanged != null) {
              widget.onValueChanged!(widget.sectionId, widget.field.attributeId,
                  dateTime, widget.field.inputId);
            }
          },
        );

      case 'oj-combobox-one':
        if (_dropdownOptions.isEmpty) {
          return Form(
            key: _formKey,
            child: AppTextField(
              controller: _textController,
              label: widget.field.displayName,
              placeholder: 'Enter ${widget.field.displayName}',
              errorText: _errorText,
              enabled: !widget.readOnly,
              onChanged: (value) {
                // Update the value without triggering a rebuild for every keystroke
                _value = value;

                // Only update error state if needed
                final newErrorText =
                    value.isNotEmpty ? _validateInput(value) : null;
                if (newErrorText != _errorText) {
                  setState(() {
                    _errorText = newErrorText;
                  });
                }

                // Store the value in the global store
                _storeValue(value);

                // Notify parent about value change
                if (widget.onValueChanged != null) {
                  widget.onValueChanged!(widget.sectionId,
                      widget.field.attributeId, value, widget.field.inputId);
                }
              },
              onSubmitted: (value) {
                // Handle chat view submission
                if (widget.isChatView &&
                    widget.onChatValueSubmitted != null &&
                    value.isNotEmpty) {
                  widget.onChatValueSubmitted!(value);
                }
              },
            ),
          );
        } else {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(widget.field.displayName,
                  style: AppTheme.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                    color: _errorText != null
                        ? AppTheme.errorColor
                        : AppTheme.textPrimaryColor,
                  )),
              const SizedBox(height: AppTheme.spacingXs),
              Form(
                key: _formKey,
                child: DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: widget.field.displayName,
                    border: OutlineInputBorder(),
                    errorText: _errorText,
                    filled: true,
                    fillColor: AppTheme.surfaceColor,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: AppTheme.spacingM,
                      vertical: AppTheme.spacingM,
                    ),
                  ),
                  hint: Text('Select ${widget.field.displayName}'),
                  value: _value as String?,
                  items: _buildDropdownItems(),
                  validator: _validateInput,
                  autovalidateMode: AutovalidateMode.disabled,
                  // Only validate when explicitly called
                  onChanged: widget.readOnly
                      ? null
                      : (value) {
                          setState(() {
                            _value = value;
                            // Only validate dropdown selections
                            _errorText = _validateInput(value);
                          });

                          // Store the value in the global store
                          _storeValue(value);

                          // Handle chat view submission
                          if (widget.isChatView &&
                              widget.onChatValueSubmitted != null &&
                              value != null) {
                            widget.onChatValueSubmitted!(value);
                          }

                          if (widget.onValueChanged != null) {
                            widget.onValueChanged!(
                                widget.sectionId,
                                widget.field.attributeId,
                                value,
                                widget.field.inputId);
                          }
                        },
                  style: AppTheme.bodyMedium,
                ),
              ),
            ],
          );
        }

      case 'oj-select-single':
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(widget.field.displayName,
                style: AppTheme.bodyMedium.copyWith(
                  fontWeight: FontWeight.w500,
                  color: _errorText != null
                      ? AppTheme.errorColor
                      : AppTheme.textPrimaryColor,
                )),
            const SizedBox(height: AppTheme.spacingXs),
            Form(
              key: _formKey,
              child: DropdownButtonFormField<String>(
                decoration: InputDecoration(
                  labelText: widget.field.displayName,
                  border: OutlineInputBorder(),
                  errorText: _errorText,
                  filled: true,
                  fillColor: AppTheme.surfaceColor,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: AppTheme.spacingM,
                    vertical: AppTheme.spacingM,
                  ),
                ),
                hint: Text('Select ${widget.field.displayName}'),
                value: _value as String?,
                items: _buildDropdownItems(),
                validator: _validateInput,
                autovalidateMode: AutovalidateMode.disabled,
                // Only validate when explicitly called
                onChanged: widget.readOnly
                    ? null
                    : (value) {
                        setState(() {
                          _value = value;
                          // Only validate dropdown selections
                          _errorText = _validateInput(value);
                        });

                        // Store the value in the global store
                        _storeValue(value);

                        // Handle chat view submission
                        if (widget.isChatView &&
                            widget.onChatValueSubmitted != null &&
                            value != null) {
                          widget.onChatValueSubmitted!(value);
                        }

                        if (widget.onValueChanged != null) {
                          widget.onValueChanged!(
                              widget.sectionId,
                              widget.field.attributeId,
                              value,
                              widget.field.inputId);
                        }
                      },
                style: AppTheme.bodyMedium,
              ),
            ),
          ],
        );

      case 'oj-c-select-multiple':
        // Implement multi-select using Chips
        List<String> selectedValues = [];
        if (_value != null) {
          if (_value is List) {
            selectedValues = List<String>.from(_value as List);
          } else if (_value is String) {
            // Handle case where a single value is stored as a string
            selectedValues = [_value as String];
          }
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label
            Text(
              widget.field.displayName,
              style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: AppTheme.spacingXs),

            // Selected values as chips
            if (selectedValues.isNotEmpty)
              Wrap(
                spacing: AppTheme.spacingXs,
                runSpacing: AppTheme.spacingXs,
                children: selectedValues.map((value) {
                  return Chip(
                    label: Text(value),
                    backgroundColor: AppTheme.primaryColor.withAlpha(50),
                    deleteIcon: const Icon(Icons.close, size: 18),
                    onDeleted: widget.readOnly
                        ? null
                        : () {
                            setState(() {
                              selectedValues.remove(value);
                              _value = selectedValues;
                              _errorText = _validateInput(_value);
                            });

                            // Store the value in the global store
                            _storeValue(selectedValues);
                          },
                  );
                }).toList(),
              ),

            const SizedBox(height: AppTheme.spacingXs),

            // Dropdown to add new values
            DropdownButtonFormField<String>(
              decoration: InputDecoration(
                hintText: 'Add ${widget.field.displayName}',
                border: OutlineInputBorder(),
                errorText: _errorText,
                filled: true,
                fillColor: AppTheme.surfaceColor,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacingM,
                  vertical: AppTheme.spacingM,
                ),
              ),
              value: null,
              // Always show hint
              items: _buildMultiSelectDropdownItems(selectedValues),

              onChanged: widget.readOnly
                  ? null
                  : (value) {
                      if (value != null) {
                        setState(() {
                          selectedValues.add(value);
                          _value = selectedValues;
                          _errorText = _validateInput(_value);
                        });

                        // Store the value in the global store
                        _storeValue(selectedValues);
                        if (widget.onValueChanged != null) {
                          widget.onValueChanged!(
                              widget.sectionId,
                              widget.field.attributeId,
                              value,
                              widget.field.inputId);
                        }
                      }
                    },
              style: AppTheme.bodyMedium,
            ),
          ],
        );

      case 'oj-file-picker':
        // Implement file picker
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label
            Text(
              widget.field.displayName,
              style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: AppTheme.spacingXs),

            // File picker button
            ElevatedButton.icon(
              onPressed: widget.readOnly
                  ? null
                  : () {
                      // In a real implementation, this would use a file picker plugin
                      // For now, we'll just simulate a file selection
                      setState(() {
                        _value = 'selected_file.pdf';
                        _errorText = null;
                      });

                      // Store the value in the global store
                      _storeValue('selected_file.pdf');
                      // if (widget.onValueChanged != null) {
                      //   widget.onValueChanged!(
                      //       widget.sectionId, widget.field.attributeId, value);
                      // }
                    },
              icon: const Icon(Icons.upload_file),
              label: Text('Choose File'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),

            // Display selected file
            if (_value != null && _value is String)
              Padding(
                padding: const EdgeInsets.only(top: AppTheme.spacingS),
                child: Row(
                  children: [
                    const Icon(Icons.insert_drive_file, size: 20),
                    const SizedBox(width: AppTheme.spacingXs),
                    Expanded(
                      child: Text(
                        _value as String,
                        style: AppTheme.bodySmall,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close, size: 18),
                      onPressed: widget.readOnly
                          ? null
                          : () {
                              setState(() {
                                _value = null;
                              });

                              // Store the value in the global store
                              _storeValue(null);
                            },
                    ),
                  ],
                ),
              ),

            // Error text
            if (_errorText != null)
              Padding(
                padding: const EdgeInsets.only(top: AppTheme.spacingXs),
                child: Text(
                  _errorText!,
                  style:
                      AppTheme.bodySmall.copyWith(color: AppTheme.errorColor),
                ),
              ),
          ],
        );

      case 'oj-c-rating-gauge':
        // Implement rating gauge using star icons
        int rating = (_value as int?) ?? 0;
        int maxRating = 5; // Default max rating

        // Check if max rating is specified in validations
        if (widget.field.validations != null) {
          for (var validation in widget.field.validations!) {
            if (validation != null && validation.containsKey('max')) {
              maxRating = (validation['max'] as num).toInt();
              break;
            }
          }
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label
            Text(
              widget.field.displayName,
              style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: AppTheme.spacingXs),

            // Rating stars
            Row(
              mainAxisSize: MainAxisSize.min,
              children: List.generate(maxRating, (index) {
                return IconButton(
                  icon: Icon(
                    index < rating ? Icons.star : Icons.star_border,
                    color: index < rating
                        ? Theme.of(context).colorScheme.secondary
                        : Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withAlpha(128),
                  ),
                  onPressed: widget.readOnly
                      ? null
                      : () {
                          setState(() {
                            _value = index + 1;
                            _errorText = null;
                          });

                          // Store the value in the global store
                          _storeValue(index + 1);
                          // if (widget.onValueChanged != null) {
                          //   widget.onValueChanged!(
                          //       widget.sectionId, widget.field.attributeId, dateTime);
                          // }
                        },
                );
              }),
            ),

            // Rating value
            Text(
              'Rating: $rating/$maxRating',
              style: AppTheme.bodySmall,
            ),

            // Error text
            if (_errorText != null)
              Padding(
                padding: const EdgeInsets.only(top: AppTheme.spacingXs),
                child: Text(
                  _errorText!,
                  style:
                      AppTheme.bodySmall.copyWith(color: AppTheme.errorColor),
                ),
              ),
          ],
        );

      case 'oj-avatar':
        // Implement avatar with initials or image
        String initials = '';
        if (_value != null &&
            _value is String &&
            (_value as String).isNotEmpty) {
          // Extract initials from value (assuming it's a name)
          final nameParts = (_value as String).split(' ');
          if (nameParts.length > 1) {
            initials = nameParts[0][0] + nameParts[1][0];
          } else if (nameParts.length == 1) {
            initials = nameParts[0][0];
          }
          initials = initials.toUpperCase();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label
            Text(
              widget.field.displayName,
              style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: AppTheme.spacingXs),

            // Avatar
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: AppTheme.primaryColor,
                  radius: 24,
                  child: Text(
                    initials,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
                const SizedBox(width: AppTheme.spacingM),
                Expanded(
                  child: AppTextField(
                    controller: _textController,
                    placeholder: 'Enter name',
                    errorText: _errorText,
                    onChanged: (value) {
                      setState(() {
                        _value = value;
                        if (value.isNotEmpty) {
                          _errorText = _validateInput(value);
                        } else {
                          _errorText = null;
                        }
                      });

                      // Store the value in the global store
                      _storeValue(value);
                      if (widget.onValueChanged != null) {
                        widget.onValueChanged!(
                            widget.sectionId,
                            widget.field.attributeId,
                            value,
                            widget.field.inputId);
                      }
                    },
                  ),
                ),
              ],
            ),
          ],
        );
      case 'oj-input-password':
        // Implement password field with toggle visibility
        return Form(
          key: _formKey,
          child: AppTextField(
            controller: _textController,
            label: widget.field.displayName,
            placeholder: 'Enter ${widget.field.displayName}',
            errorText: _errorText,
            type: AppTextFieldType.password,
            obscureText: _obscureText,
            // Use the state variable for obscureText
            onTogglePasswordVisibility: (newValue) {
              // Update the obscureText state when the toggle button is pressed
              setState(() {
                _obscureText = newValue;
              });
            },
            onChanged: (value) {
              // Update the value without triggering a rebuild for every keystroke
              _value = value;

              // Only update error state if needed
              final newErrorText =
                  value.isNotEmpty ? _validateInput(value) : null;
              if (newErrorText != _errorText) {
                setState(() {
                  _errorText = newErrorText;
                });
              }

              // Store the value in the global store
              _storeValue(value);
              if (widget.onValueChanged != null) {
                widget.onValueChanged!(widget.sectionId,
                    widget.field.attributeId, value, widget.field.inputId);
              }
            },
          ),
        );

      case 'oj-switch':
        // Implement switch toggle
        bool isEnabled = (_value as bool?) ?? false;

        return Row(
          children: [
            // Switch
            Switch(
              value: isEnabled,
              activeColor: AppTheme.primaryColor,
              onChanged: widget.readOnly
                  ? null
                  : (value) {
                      setState(() {
                        _value = value;
                        _errorText = null;
                      });

                      // Store the value in the global store
                      _storeValue(value);
                      if (widget.onValueChanged != null) {
                        widget.onValueChanged!(
                            widget.sectionId,
                            widget.field.attributeId,
                            value,
                            widget.field.inputId);
                      }
                    },
            ),
            const SizedBox(width: AppTheme.spacingS),

            // Label
            Expanded(
              child: Text(
                widget.field.displayName,
                style: AppTheme.bodyMedium,
              ),
            ),

            // Error text
            if (_errorText != null)
              Padding(
                padding: const EdgeInsets.only(left: AppTheme.spacingS),
                child: Text(
                  _errorText!,
                  style:
                      AppTheme.bodySmall.copyWith(color: AppTheme.errorColor),
                ),
              ),
          ],
        );

      case 'oj-checkboxset':
        // Implement checkbox group
        List<String> selectedValues = [];
        if (_value != null) {
          if (_value is List) {
            selectedValues = List<String>.from(_value as List);
          } else if (_value is String) {
            // Handle case where a single value is stored as a string
            selectedValues = [_value as String];
          }
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label
            Text(
              widget.field.displayName,
              style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: AppTheme.spacingXs),

            // Checkboxes
            if (widget.field.allowedValues != null)
              ...widget.field.allowedValues!.map((value) {
                return CheckboxListTile(
                  title: Text(value),
                  value: selectedValues.contains(value),
                  controlAffinity: ListTileControlAffinity.leading,
                  contentPadding: EdgeInsets.zero,
                  dense: true,
                  activeColor: AppTheme.primaryColor,
                  onChanged: widget.readOnly
                      ? null
                      : (checked) {
                          setState(() {
                            if (checked == true) {
                              if (!selectedValues.contains(value)) {
                                selectedValues.add(value);
                              }
                            } else {
                              selectedValues.remove(value);
                            }
                            _value = selectedValues;
                            _errorText = _validateInput(_value);
                          });

                          // Store the value in the global store
                          _storeValue(selectedValues);
                          if (widget.onValueChanged != null) {
                            widget.onValueChanged!(
                                widget.sectionId,
                                widget.field.attributeId,
                                value,
                                widget.field.inputId);
                          }
                        },
                );
              }),

            // Error text
            if (_errorText != null)
              Padding(
                padding: const EdgeInsets.only(top: AppTheme.spacingXs),
                child: Text(
                  _errorText!,
                  style:
                      AppTheme.bodySmall.copyWith(color: AppTheme.errorColor),
                ),
              ),
          ],
        );

      case 'oj-radioset':
        // Implement radio button group
        String? selectedValue = _value as String?;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label
            Text(
              widget.field.displayName,
              style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: AppTheme.spacingXs),

            // Radio buttons
            if (widget.field.allowedValues != null)
              ...widget.field.allowedValues!.map((value) {
                return RadioListTile<String>(
                  title: Text(value),
                  value: value,
                  groupValue: selectedValue,
                  controlAffinity: ListTileControlAffinity.leading,
                  contentPadding: EdgeInsets.zero,
                  dense: true,
                  activeColor: AppTheme.primaryColor,
                  onChanged: widget.readOnly
                      ? null
                      : (newValue) {
                          setState(() {
                            _value = newValue;
                            _errorText = _validateInput(_value);
                          });

                          // Store the value in the global store
                          _storeValue(newValue);
                          if (widget.onValueChanged != null) {
                            widget.onValueChanged!(
                                widget.sectionId,
                                widget.field.attributeId,
                                value,
                                widget.field.inputId);
                          }
                        },
                );
              }),

            // Error text
            if (_errorText != null)
              Padding(
                padding: const EdgeInsets.only(top: AppTheme.spacingXs),
                child: Text(
                  _errorText!,
                  style:
                      AppTheme.bodySmall.copyWith(color: AppTheme.errorColor),
                ),
              ),
          ],
        );

      case 'oj-list-item-layout':
        // Implement list item with leading icon, title, and description
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label
            Text(
              widget.field.displayName,
              style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: AppTheme.spacingXs),

            // List item
            Container(
              decoration: BoxDecoration(
                color: AppTheme.surfaceColor,
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusM),
                border: Border.all(color: AppTheme.dividerColor),
              ),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: AppTheme.primaryColor,
                  child: const Icon(Icons.list, color: Colors.white),
                ),
                title: AppTextField(
                  controller: _textController,
                  placeholder: 'Enter title',
                  onChanged: (value) {
                    setState(() {
                      // Store as a map with title and description
                      final Map<String, String> listItemData =
                          (_value as Map<String, String>?) ??
                              {'title': '', 'description': ''};
                      listItemData['title'] = value;
                      _value = listItemData;
                      _errorText = null;
                    });
                  },
                ),
                subtitle: Padding(
                  padding: const EdgeInsets.only(top: AppTheme.spacingXs),
                  child: AppTextField(
                    placeholder: 'Enter description',
                    onChanged: (value) {
                      setState(() {
                        // Store as a map with title and description
                        final Map<String, String> listItemData =
                            (_value as Map<String, String>?) ??
                                {'title': '', 'description': ''};
                        listItemData['description'] = value;
                        _value = listItemData;
                        _errorText = null;
                      });
                    },
                  ),
                ),
              ),
            ),

            // Error text
            if (_errorText != null)
              Padding(
                padding: const EdgeInsets.only(top: AppTheme.spacingXs),
                child: Text(
                  _errorText!,
                  style:
                      AppTheme.bodySmall.copyWith(color: AppTheme.errorColor),
                ),
              ),
          ],
        );

      case 'oj-text-area':
        return Form(
          key: _formKey,
          child: AppTextField(
            controller: _textController,
            label: widget.field.displayName,
            placeholder: 'Enter ${widget.field.displayName}',
            errorText: _errorText,
            type: AppTextFieldType.multiline,
            onChanged: (value) {
              setState(() {
                _value = value;
                // Only show validation errors for non-empty values
                if (value.isNotEmpty) {
                  _errorText = _validateInput(value);
                } else {
                  _errorText = null; // Clear error when field is empty
                }
              });

              // Store the value in the global store
              _storeValue(value);
            },
          ),
        );

      case 'oj-progress-bar':
        // Implement progress bar
        double progress = (_value as num?)?.toDouble() ?? 0.0;
        // Ensure progress is between 0 and 1
        progress = progress.clamp(0.0, 1.0);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label and percentage
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.field.displayName,
                  style:
                      AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w500),
                ),
                Text(
                  '${(progress * 100).toInt()}%',
                  style: AppTheme.bodyMedium,
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingXs),

            // Progress bar
            LinearProgressIndicator(
              value: progress,
              backgroundColor: AppTheme.primaryColor.withAlpha(50),
              valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
              minHeight: 8,
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusS),
            ),

            // Slider to adjust progress (for demo purposes)
            Slider(
              value: progress,
              min: 0.0,
              max: 1.0,
              divisions: 100,
              label: '${(progress * 100).toInt()}%',
              activeColor: AppTheme.primaryColor,
              inactiveColor: AppTheme.primaryColor.withAlpha(50),
              onChanged: (value) {
                setState(() {
                  _value = value;
                  _errorText = null;
                });

                // Store the value in the global store
                _storeValue(value);
              },
            ),

            // Error text
            if (_errorText != null)
              Padding(
                padding: const EdgeInsets.only(top: AppTheme.spacingXs),
                child: Text(
                  _errorText!,
                  style:
                      AppTheme.bodySmall.copyWith(color: AppTheme.errorColor),
                ),
              ),
          ],
        );

      case 'oj-c-popup':
        // Implement popup with trigger button
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label
            Text(
              widget.field.displayName,
              style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: AppTheme.spacingXs),

            // Popup trigger button
            ElevatedButton(
              onPressed: widget.readOnly
                  ? null
                  : () {
                      // Show dialog when button is pressed
                      showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          return AlertDialog(
                            title: Text(widget.field.displayName),
                            content: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                AppTextField(
                                  controller: _textController,
                                  placeholder: 'Enter value',
                                  onChanged: (value) {
                                    setState(() {
                                      _value = value;
                                    });
                                  },
                                ),
                              ],
                            ),
                            actions: [
                              TextButton(
                                onPressed: () {
                                  Navigator.of(context).pop();
                                },
                                child: const Text('Cancel'),
                              ),
                              ElevatedButton(
                                onPressed: () {
                                  Navigator.of(context).pop();
                                  setState(() {
                                    _errorText = _validateInput(_value);
                                  });

                                  // Store the value in the global store
                                  _storeValue(_value);
                                },
                                child: const Text('OK'),
                              ),
                            ],
                          );
                        },
                      );
                    },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.open_in_new, size: 18),
                  const SizedBox(width: AppTheme.spacingXs),
                  const Text('Open Popup'),
                ],
              ),
            ),

            // Display current value
            if (_value != null &&
                _value is String &&
                (_value as String).isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: AppTheme.spacingS),
                child: Text(
                  'Current value: $_value',
                  style: AppTheme.bodySmall,
                ),
              ),

            // Error text
            if (_errorText != null)
              Padding(
                padding: const EdgeInsets.only(top: AppTheme.spacingXs),
                child: Text(
                  _errorText!,
                  style:
                      AppTheme.bodySmall.copyWith(color: AppTheme.errorColor),
                ),
              ),
          ],
        );

      case 'oj-input-search':
        // Implement search field with suggestions
        return Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Search field
              AppTextField(
                controller: _textController,
                label: widget.field.displayName,
                placeholder: 'Search ${widget.field.displayName}',
                errorText: _errorText,
                type: AppTextFieldType.search,
                onChanged: (value) {
                  setState(() {
                    _value = value;
                    if (value.isNotEmpty) {
                      _errorText = _validateInput(value);
                    } else {
                      _errorText = null;
                    }
                  });
                },
              ),

              // Suggestions
              if (_value != null &&
                  _value is String &&
                  (_value as String).isNotEmpty &&
                  widget.field.allowedValues != null)
                Padding(
                  padding: const EdgeInsets.only(top: AppTheme.spacingS),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Suggestions:',
                        style: AppTheme.bodySmall
                            .copyWith(fontWeight: FontWeight.w500),
                      ),
                      const SizedBox(height: AppTheme.spacingXs),
                      ...widget.field.allowedValues!
                          .where((value) => value
                              .toLowerCase()
                              .contains((_value as String).toLowerCase()))
                          .take(5) // Limit to 5 suggestions
                          .map((value) => ListTile(
                                title: Text(value),
                                contentPadding: EdgeInsets.zero,
                                dense: true,
                                onTap: () {
                                  setState(() {
                                    _value = value;
                                    _textController.text = value;
                                    _textController.selection =
                                        TextSelection.fromPosition(
                                      TextPosition(offset: value.length),
                                    );
                                    _errorText = _validateInput(value);
                                  });
                                },
                              )),
                    ],
                  ),
                ),
            ],
          ),
        );

      case 'oj-accordion':
        // Implement accordion with expandable sections
        // Parse sections from value or use default sections
        List<Map<String, String>> sections = [];
        if (_value != null && _value is List) {
          sections = List<Map<String, String>>.from(
            (_value as List).map((item) => Map<String, String>.from(item)),
          );
        } else {
          // Default sections
          sections = [
            {'title': 'Section 1', 'content': ''},
            {'title': 'Section 2', 'content': ''},
          ];
          _value = sections;
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label
            Text(
              widget.field.displayName,
              style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: AppTheme.spacingXs),

            // Accordion sections
            ...List.generate(sections.length, (index) {
              return Card(
                margin: const EdgeInsets.only(bottom: AppTheme.spacingS),
                child: ExpansionTile(
                  title: Row(
                    children: [
                      Expanded(
                        child: AppTextField(
                          placeholder: 'Section title',
                          controller: TextEditingController(
                              text: sections[index]['title']),
                          enabled: !widget.readOnly,
                          onChanged: (value) {
                            setState(() {
                              sections[index]['title'] = value;
                              _value = sections;
                              _errorText = null;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(AppTheme.spacingM),
                      child: AppTextField(
                        placeholder: 'Section content',
                        controller: TextEditingController(
                            text: sections[index]['content']),
                        type: AppTextFieldType.multiline,
                        enabled: !widget.readOnly,
                        onChanged: (value) {
                          setState(() {
                            sections[index]['content'] = value;
                            _value = sections;
                            _errorText = null;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              );
            }),

            // Add section button
            TextButton.icon(
              onPressed: widget.readOnly
                  ? null
                  : () {
                      setState(() {
                        sections.add({'title': 'New Section', 'content': ''});
                        _value = sections;
                        _errorText = null;
                      });
                    },
              icon: const Icon(Icons.add),
              label: const Text('Add Section'),
              style: TextButton.styleFrom(
                foregroundColor: AppTheme.primaryColor,
              ),
            ),

            // Error text
            if (_errorText != null)
              Padding(
                padding: const EdgeInsets.only(top: AppTheme.spacingXs),
                child: Text(
                  _errorText!,
                  style:
                      AppTheme.bodySmall.copyWith(color: AppTheme.errorColor),
                ),
              ),
          ],
        );

      case 'oj-image-capture':
        // Implement image capture control
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label
            Text(
              widget.field.displayName,
              style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: AppTheme.spacingXs),

            // Image preview
            if (_value != null &&
                _value is String &&
                (_value as String).isNotEmpty)
              Container(
                width: double.infinity,
                height: 200,
                margin: const EdgeInsets.only(bottom: AppTheme.spacingS),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.asset(
                    'assets/images/placeholder_image.png',
                    // Use a placeholder image
                    fit: BoxFit.cover,
                  ),
                ),
              ),

            // Capture button
            ElevatedButton.icon(
              onPressed: widget.readOnly
                  ? null
                  : () {
                      // In a real implementation, this would use the camera
                      // For now, we'll just simulate an image capture
                      setState(() {
                        _value = 'captured_image.jpg';
                        _errorText = null;
                      });

                      // Store the value in the global store
                      _storeValue(_value);
                    },
              icon: const Icon(Icons.camera_alt),
              label: Text(_value == null ? 'Capture Image' : 'Recapture Image'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),

            // Error text
            if (_errorText != null)
              Padding(
                padding: const EdgeInsets.only(top: AppTheme.spacingXs),
                child: Text(
                  _errorText!,
                  style:
                      AppTheme.bodySmall.copyWith(color: AppTheme.errorColor),
                ),
              ),
          ],
        );

      case 'oj-timer':
        // Implement timer control
        final int timerValue = (_value as int?) ?? 0;
        final String formattedTime = _formatTime(timerValue);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label
            Text(
              widget.field.displayName,
              style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: AppTheme.spacingS),

            // Timer display
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 16),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Text(
                    formattedTime,
                    style: const TextStyle(
                      fontSize: 48,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'monospace',
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingS),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Start/Stop button
                      ElevatedButton.icon(
                        onPressed: widget.readOnly
                            ? null
                            : () {
                                // In a real implementation, this would start/stop a timer
                                // For now, we'll just simulate a timer value
                                setState(() {
                                  // Add 30 seconds to the current value
                                  final currentValue = (_value as int?) ?? 0;
                                  _value = currentValue + 30;
                                });

                                // Store the value in the global store
                                _storeValue(_value);
                              },
                        icon: const Icon(Icons.play_arrow),
                        label: const Text('Start'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: Colors.white,
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingS),
                      // Reset button
                      ElevatedButton.icon(
                        onPressed: widget.readOnly
                            ? null
                            : () {
                                setState(() {
                                  _value = 0;
                                });

                                // Store the value in the global store
                                _storeValue(_value);
                              },
                        icon: const Icon(Icons.refresh),
                        label: const Text('Reset'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey[400],
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Error text
            if (_errorText != null)
              Padding(
                padding: const EdgeInsets.only(top: AppTheme.spacingXs),
                child: Text(
                  _errorText!,
                  style:
                      AppTheme.bodySmall.copyWith(color: AppTheme.errorColor),
                ),
              ),
          ],
        );

      case 'oj-year-selector':
        // Implement year selection control
        final int currentYear = DateTime.now().year;
        final int selectedYear = (_value as int?) ?? currentYear;
        final List<int> years =
            List.generate(100, (index) => currentYear - 50 + index);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label
            Text(
              widget.field.displayName,
              style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: AppTheme.spacingXs),

            // Year dropdown
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<int>(
                  value: selectedYear,
                  isExpanded: true,
                  icon: const Icon(Icons.arrow_drop_down),
                  items: years.map((int year) {
                    return DropdownMenuItem<int>(
                      value: year,
                      child: Text(year.toString()),
                    );
                  }).toList(),
                  onChanged: widget.readOnly
                      ? null
                      : (int? newValue) {
                          if (newValue != null) {
                            setState(() {
                              _value = newValue;
                              _errorText = null;
                            });

                            // Store the value in the global store
                            _storeValue(newValue);
                          }
                        },
                ),
              ),
            ),

            // Error text
            if (_errorText != null)
              Padding(
                padding: const EdgeInsets.only(top: AppTheme.spacingXs),
                child: Text(
                  _errorText!,
                  style:
                      AppTheme.bodySmall.copyWith(color: AppTheme.errorColor),
                ),
              ),
          ],
        );

      case 'oj-audio-player':
        // Implement audio player control
        final String audioUrl = (_value as String?) ?? '';

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label
            Text(
              widget.field.displayName,
              style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: AppTheme.spacingXs),

            // Audio player controls
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  // Audio file name or URL
                  if (audioUrl.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(bottom: AppTheme.spacingS),
                      child: Text(
                        audioUrl,
                        style: AppTheme.bodySmall,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),

                  // Player controls
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Play button
                      IconButton(
                        icon: const Icon(Icons.play_arrow),
                        onPressed: widget.readOnly || audioUrl.isEmpty
                            ? null
                            : () {
                                // In a real implementation, this would play the audio
                                // For now, we'll just show a message
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                      content: Text('Playing audio...')),
                                );
                              },
                      ),
                      // Pause button
                      IconButton(
                        icon: const Icon(Icons.pause),
                        onPressed: widget.readOnly || audioUrl.isEmpty
                            ? null
                            : () {
                                // In a real implementation, this would pause the audio
                                // For now, we'll just show a message
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(content: Text('Audio paused')),
                                );
                              },
                      ),
                      // Stop button
                      IconButton(
                        icon: const Icon(Icons.stop),
                        onPressed: widget.readOnly || audioUrl.isEmpty
                            ? null
                            : () {
                                // In a real implementation, this would stop the audio
                                // For now, we'll just show a message
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                      content: Text('Audio stopped')),
                                );
                              },
                      ),
                    ],
                  ),

                  // Upload button
                  if (!widget.readOnly)
                    ElevatedButton.icon(
                      onPressed: () {
                        // In a real implementation, this would open a file picker
                        // For now, we'll just simulate selecting an audio file
                        setState(() {
                          _value = 'sample_audio.mp3';
                          _errorText = null;
                        });

                        // Store the value in the global store
                        _storeValue(_value);
                      },
                      icon: const Icon(Icons.upload_file),
                      label: Text(
                          audioUrl.isEmpty ? 'Upload Audio' : 'Change Audio'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                      ),
                    ),
                ],
              ),
            ),

            // Error text
            if (_errorText != null)
              Padding(
                padding: const EdgeInsets.only(top: AppTheme.spacingXs),
                child: Text(
                  _errorText!,
                  style:
                      AppTheme.bodySmall.copyWith(color: AppTheme.errorColor),
                ),
              ),
          ],
        );

      case 'oj-video-player':
        // Implement video player control
        final String videoUrl = (_value as String?) ?? '';

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label
            Text(
              widget.field.displayName,
              style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: AppTheme.spacingXs),

            // Video preview
            if (videoUrl.isNotEmpty)
              Container(
                width: double.infinity,
                height: 200,
                margin: const EdgeInsets.only(bottom: AppTheme.spacingS),
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Icon(
                    Icons.play_circle_outline,
                    color: Colors.white,
                    size: 64,
                  ),
                ),
              ),

            // Video player controls
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  // Video file name or URL
                  if (videoUrl.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(bottom: AppTheme.spacingS),
                      child: Text(
                        videoUrl,
                        style: AppTheme.bodySmall,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),

                  // Player controls
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Play button
                      IconButton(
                        icon: const Icon(Icons.play_arrow),
                        onPressed: widget.readOnly || videoUrl.isEmpty
                            ? null
                            : () {
                                // In a real implementation, this would play the video
                                // For now, we'll just show a message
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                      content: Text('Playing video...')),
                                );
                              },
                      ),
                      // Pause button
                      IconButton(
                        icon: const Icon(Icons.pause),
                        onPressed: widget.readOnly || videoUrl.isEmpty
                            ? null
                            : () {
                                // In a real implementation, this would pause the video
                                // For now, we'll just show a message
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(content: Text('Video paused')),
                                );
                              },
                      ),
                      // Stop button
                      IconButton(
                        icon: const Icon(Icons.stop),
                        onPressed: widget.readOnly || videoUrl.isEmpty
                            ? null
                            : () {
                                // In a real implementation, this would stop the video
                                // For now, we'll just show a message
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                      content: Text('Video stopped')),
                                );
                              },
                      ),
                    ],
                  ),

                  // Upload button
                  if (!widget.readOnly)
                    ElevatedButton.icon(
                      onPressed: () {
                        // In a real implementation, this would open a file picker
                        // For now, we'll just simulate selecting a video file
                        setState(() {
                          _value = 'sample_video.mp4';
                          _errorText = null;
                        });

                        // Store the value in the global store
                        _storeValue(_value);
                      },
                      icon: const Icon(Icons.upload_file),
                      label: Text(
                          videoUrl.isEmpty ? 'Upload Video' : 'Change Video'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                      ),
                    ),
                ],
              ),
            ),

            // Error text
            if (_errorText != null)
              Padding(
                padding: const EdgeInsets.only(top: AppTheme.spacingXs),
                child: Text(
                  _errorText!,
                  style:
                      AppTheme.bodySmall.copyWith(color: AppTheme.errorColor),
                ),
              ),
          ],
        );

      default:
        // For any unsupported UI control, fall back to a standard text field
        return Form(
          key: _formKey,
          child: AppTextField(
            controller: _textController,
            label: widget.field.displayName,
            placeholder: 'Enter ${widget.field.displayName}',
            errorText: _errorText,
            enabled: !widget.readOnly,
            onChanged: (value) {
              setState(() {
                _value = value;
                // Only show validation errors for non-empty values
                if (value.isNotEmpty) {
                  _errorText = _validateInput(value);
                } else {
                  _errorText = null; // Clear error when field is empty
                }
              });

              // Store the value in the global store
              _storeValue(value);
            },
          ),
        );
    }
  }

  void updateDropdownOptions(List<DropdownOption> newOptions) {
    setState(() {
      _dropdownOptions = newOptions;
      // Reset the selected value if the new options don't contain it
      if (!_dropdownOptions.any((option) => option.value == _value)) {
        _value = null;
      }
    });
  }
}
