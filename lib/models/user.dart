import 'base_model.dart';
import 'auth/login_model.dart';

class User implements BaseModel {
  final String id;
  final String email;
  final String name;
  final String? username;
  final String? role;
  final String? profilePicture;
  final String? organization;
  final String? mobileNumber;
  final String? accessToken;
  final String? refreshToken;
  final List<String>? roles;
  final List<String>? orgUnits;
  final String? tenantId;
  final bool? disabled;
  final String? status;

  User({
    required this.id,
    required this.email,
    required this.name,
    this.username,
    this.role,
    this.profilePicture,
    this.organization,
    this.mobileNumber,
    this.accessToken,
    this.refreshToken,
    this.roles,
    this.orgUnits,
    this.tenantId,
    this.disabled,
    this.status,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    // Handle roles list
    List<String>? roles;
    if (json['roles'] != null) {
      roles = List<String>.from(json['roles']);
    }

    // Handle org units list
    List<String>? orgUnits;
    if (json['org_units'] != null) {
      orgUnits = List<String>.from(json['org_units']);
    }

    return User(
      id: json['id'] ?? json['user_id'] ?? json['sub'] ?? '',
      email: json['email'] ?? '',
      name: json['name'] ?? json['first_name'] ?? json['nickname'] ?? '',
      username: json['username'],
      role: json['roleValue'] ?? json['role'],
      profilePicture:
          json['profile_picture'] ?? json['profilePic'] ?? json['picture'],
      organization: json['OrgName'] ?? json['organization'],
      mobileNumber: json['mobileNo'] ?? json['mobile'],
      accessToken: json['access_token'],
      refreshToken: json['refresh_token'],
      roles: roles,
      orgUnits: orgUnits,
      tenantId: json['tenant_id'],
      disabled: json['disabled'],
      status: json['status'],
    );
  }

  factory User.fromLoginResponse(Map<String, dynamic> response) {
    // Extract token data
    final tokenData = response['token_data'] as Map<String, dynamic>;
    final accessToken = tokenData['access_token'] as String;
    final refreshToken = tokenData['refresh_token'] as String;

    // Extract user info
    final userInfo = response['user_info'] as Map<String, dynamic>;

    return User(
      id: userInfo['user_id'].toString(), // Convert to String if needed
      email: userInfo['email'] as String,
      name: userInfo['full_name'] as String,
      username: userInfo['username'] as String,
      profilePicture: userInfo['profilePic'] as String,
      mobileNumber: userInfo['phone_number'] as String,
      organization: userInfo['company'] as String,
      accessToken: accessToken,
      refreshToken: refreshToken,
    );
  }

  factory User.fromAuth0Registration(Map<String, dynamic> response) {
    // Handle roles list
    List<String>? roles;
    if (response['roles'] != null) {
      roles = List<String>.from(response['roles']);
    }

    // Handle org units list
    List<String>? orgUnits;
    if (response['org_units'] != null) {
      orgUnits = List<String>.from(response['org_units']);
    }

    return User(
      id: response['user_id'] ?? '',
      email: response['email'] ?? '',
      name: '${response['first_name'] ?? ''}'
          .trim(),
      username: response['username'] ?? '',
      role: roles?.isNotEmpty == true ? roles!.first : '',
      profilePicture: response['picture'],
      organization: orgUnits?.isNotEmpty == true ? orgUnits!.first : '',
      mobileNumber: '',
      roles: roles,
      orgUnits: orgUnits,
      tenantId: response['tenant_id'],
      disabled: response['disabled'],
      status: response['status'],
    );
  }

  factory User.fromUserDetails(UserDetails userDetails) {
    return User(
      id: userDetails.userId ?? '',
      email: userDetails.email ?? '',
      name:
          '${userDetails.firstName ?? ''} '.trim(),
      username: userDetails.username,
      roles: userDetails.roles,
      tenantId: userDetails.tenantId,
      disabled: userDetails.disabled,
      status: userDetails.status,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': id,
      'email': email,
      'name': name,
      'username': username,
      'roleValue': role,
      'profilePic': profilePicture,
      'OrgName': organization,
      'mobileNo': mobileNumber,
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'roles': roles,
      'org_units': orgUnits,
      'tenant_id': tenantId,
      'disabled': disabled,
      'status': status,
    };
  }

  @override
  User copyWith({
    String? id,
    String? email,
    String? name,
    String? username,
    String? role,
    String? profilePicture,
    String? organization,
    String? mobileNumber,
    String? accessToken,
    String? refreshToken,
    List<String>? roles,
    List<String>? orgUnits,
    String? tenantId,
    bool? disabled,
    String? status,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      username: username ?? this.username,
      role: role ?? this.role,
      profilePicture: profilePicture ?? this.profilePicture,
      organization: organization ?? this.organization,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      roles: roles ?? this.roles,
      orgUnits: orgUnits ?? this.orgUnits,
      tenantId: tenantId ?? this.tenantId,
      disabled: disabled ?? this.disabled,
      status: status ?? this.status,
    );
  }

  // Utility method to check if the user is logged in
  bool get isLoggedIn => accessToken != null && accessToken!.isNotEmpty;

  // Utility method to get the user's display name
  String get displayName => name.isNotEmpty ? name : username ?? email;

  // Utility method to get the user's initials
  String get initials {
    if (name.isNotEmpty) {
      final parts = name.split(' ');
      if (parts.length > 1) {
        return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
      }
      return name[0].toUpperCase();
    }
    return email[0].toUpperCase();
  }
}
