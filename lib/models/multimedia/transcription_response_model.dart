// To parse this JSON data, do
//
//     final transcriptionResponseModel = transcriptionResponseModelFromJson(jsonString);

import 'dart:convert';

TranscriptionResponseModel transcriptionResponseModelFromJson(String str) =>
    TranscriptionResponseModel.fromJson(json.decode(str));

String transcriptionResponseModelToJson(TranscriptionResponseModel data) =>
    json.encode(data.toJson());

class TranscriptionResponseModel {
  bool? success;
  AdapterConfig? adapterConfig;
  Data? data;
  String? message;
  dynamic error;
  DateTime? timestamp;
  int? processingTimeMs;
  OptimizationInfo? optimizationInfo;

  TranscriptionResponseModel({
    this.success,
    this.adapterConfig,
    this.data,
    this.message,
    this.error,
    this.timestamp,
    this.processingTimeMs,
    this.optimizationInfo,
  });

  TranscriptionResponseModel copyWith({
    bool? success,
    AdapterConfig? adapterConfig,
    Data? data,
    String? message,
    dynamic error,
    DateTime? timestamp,
    int? processingTimeMs,
    OptimizationInfo? optimizationInfo,
  }) =>
      TranscriptionResponseModel(
        success: success ?? this.success,
        adapterConfig: adapterConfig ?? this.adapterConfig,
        data: data ?? this.data,
        message: message ?? this.message,
        error: error ?? this.error,
        timestamp: timestamp ?? this.timestamp,
        processingTimeMs: processingTimeMs ?? this.processingTimeMs,
        optimizationInfo: optimizationInfo ?? this.optimizationInfo,
      );

  factory TranscriptionResponseModel.fromJson(Map<String, dynamic> json) =>
      TranscriptionResponseModel(
        success: json["success"],
        adapterConfig: json["adapter_config"] == null
            ? null
            : AdapterConfig.fromJson(json["adapter_config"]),
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        message: json["message"],
        error: json["error"],
        timestamp: json["timestamp"] == null
            ? null
            : DateTime.parse(json["timestamp"]),
        processingTimeMs: json["processing_time_ms"],
        optimizationInfo: json["optimization_info"] == null
            ? null
            : OptimizationInfo.fromJson(json["optimization_info"]),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "adapter_config": adapterConfig?.toJson(),
        "data": data?.toJson(),
        "message": message,
        "error": error,
        "timestamp": timestamp?.toIso8601String(),
        "processing_time_ms": processingTimeMs,
        "optimization_info": optimizationInfo?.toJson(),
      };
}

class AdapterConfig {
  String? service;
  String? operation;
  String? version;

  AdapterConfig({
    this.service,
    this.operation,
    this.version,
  });

  AdapterConfig copyWith({
    String? service,
    String? operation,
    String? version,
  }) =>
      AdapterConfig(
        service: service ?? this.service,
        operation: operation ?? this.operation,
        version: version ?? this.version,
      );

  factory AdapterConfig.fromJson(Map<String, dynamic> json) => AdapterConfig(
        service: json["service"],
        operation: json["operation"],
        version: json["version"],
      );

  Map<String, dynamic> toJson() => {
        "service": service,
        "operation": operation,
        "version": version,
      };
}

class Data {
  String? status;
  String? language;
  String? languageName;
  bool? isIndian;
  bool? highPriorityIndian;
  bool? usedWhisperTranslation;
  String? transcription;
  dynamic englishTranslation;
  String? processedText;
  String? modelUsed;
  bool? accuracyOptimized;
  double? processingTime;

  Data({
    this.status,
    this.language,
    this.languageName,
    this.isIndian,
    this.highPriorityIndian,
    this.usedWhisperTranslation,
    this.transcription,
    this.englishTranslation,
    this.processedText,
    this.modelUsed,
    this.accuracyOptimized,
    this.processingTime,
  });

  Data copyWith({
    String? status,
    String? language,
    String? languageName,
    bool? isIndian,
    bool? highPriorityIndian,
    bool? usedWhisperTranslation,
    String? transcription,
    dynamic englishTranslation,
    String? processedText,
    String? modelUsed,
    bool? accuracyOptimized,
    double? processingTime,
  }) =>
      Data(
        status: status ?? this.status,
        language: language ?? this.language,
        languageName: languageName ?? this.languageName,
        isIndian: isIndian ?? this.isIndian,
        highPriorityIndian: highPriorityIndian ?? this.highPriorityIndian,
        usedWhisperTranslation:
            usedWhisperTranslation ?? this.usedWhisperTranslation,
        transcription: transcription ?? this.transcription,
        englishTranslation: englishTranslation ?? this.englishTranslation,
        processedText: processedText ?? this.processedText,
        modelUsed: modelUsed ?? this.modelUsed,
        accuracyOptimized: accuracyOptimized ?? this.accuracyOptimized,
        processingTime: processingTime ?? this.processingTime,
      );

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        status: json["status"],
        language: json["language"],
        languageName: json["language_name"],
        isIndian: json["is_indian"],
        highPriorityIndian: json["high_priority_indian"],
        usedWhisperTranslation: json["used_whisper_translation"],
        transcription: json["transcription"],
        englishTranslation: json["english_translation"],
        processedText: json["processed_text"],
        modelUsed: json["model_used"],
        accuracyOptimized: json["accuracy_optimized"],
        processingTime: json["processing_time"]?.toDouble(),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "language": language,
        "language_name": languageName,
        "is_indian": isIndian,
        "high_priority_indian": highPriorityIndian,
        "used_whisper_translation": usedWhisperTranslation,
        "transcription": transcription,
        "english_translation": englishTranslation,
        "processed_text": processedText,
        "model_used": modelUsed,
        "accuracy_optimized": accuracyOptimized,
        "processing_time": processingTime,
      };
}

class OptimizationInfo {
  bool? cached;
  bool? gpuUsed;
  int? processingTimeMs;
  int? modelCacheHits;
  int? resultCacheHits;

  OptimizationInfo({
    this.cached,
    this.gpuUsed,
    this.processingTimeMs,
    this.modelCacheHits,
    this.resultCacheHits,
  });

  OptimizationInfo copyWith({
    bool? cached,
    bool? gpuUsed,
    int? processingTimeMs,
    int? modelCacheHits,
    int? resultCacheHits,
  }) =>
      OptimizationInfo(
        cached: cached ?? this.cached,
        gpuUsed: gpuUsed ?? this.gpuUsed,
        processingTimeMs: processingTimeMs ?? this.processingTimeMs,
        modelCacheHits: modelCacheHits ?? this.modelCacheHits,
        resultCacheHits: resultCacheHits ?? this.resultCacheHits,
      );

  factory OptimizationInfo.fromJson(Map<String, dynamic> json) =>
      OptimizationInfo(
        cached: json["cached"],
        gpuUsed: json["gpu_used"],
        processingTimeMs: json["processing_time_ms"],
        modelCacheHits: json["model_cache_hits"],
        resultCacheHits: json["result_cache_hits"],
      );

  Map<String, dynamic> toJson() => {
        "cached": cached,
        "gpu_used": gpuUsed,
        "processing_time_ms": processingTimeMs,
        "model_cache_hits": modelCacheHits,
        "result_cache_hits": resultCacheHits,
      };
}
