// To parse this JSON data, do
//
//     final translationResponseModel = translationResponseModelFromJson(jsonString);

import 'dart:convert';

TranslationResponseModel translationResponseModelFromJson(String str) =>
    TranslationResponseModel.fromJson(json.decode(str));

String translationResponseModelToJson(TranslationResponseModel data) =>
    json.encode(data.toJson());

class TranslationResponseModel {
  bool? success;
  AdapterConfig? adapterConfig;
  Data? data;
  String? message;
  dynamic error;
  DateTime? timestamp;
  int? processingTimeMs;
  OptimizationInfo? optimizationInfo;

  TranslationResponseModel({
    this.success,
    this.adapterConfig,
    this.data,
    this.message,
    this.error,
    this.timestamp,
    this.processingTimeMs,
    this.optimizationInfo,
  });

  TranslationResponseModel copyWith({
    bool? success,
    AdapterConfig? adapterConfig,
    Data? data,
    String? message,
    dynamic error,
    DateTime? timestamp,
    int? processingTimeMs,
    OptimizationInfo? optimizationInfo,
  }) =>
      TranslationResponseModel(
        success: success ?? this.success,
        adapterConfig: adapterConfig ?? this.adapterConfig,
        data: data ?? this.data,
        message: message ?? this.message,
        error: error ?? this.error,
        timestamp: timestamp ?? this.timestamp,
        processingTimeMs: processingTimeMs ?? this.processingTimeMs,
        optimizationInfo: optimizationInfo ?? this.optimizationInfo,
      );

  factory TranslationResponseModel.fromJson(Map<String, dynamic> json) =>
      TranslationResponseModel(
        success: json["success"],
        adapterConfig: json["adapter_config"] == null
            ? null
            : AdapterConfig.fromJson(json["adapter_config"]),
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        message: json["message"],
        error: json["error"],
        timestamp: json["timestamp"] == null
            ? null
            : DateTime.parse(json["timestamp"]),
        processingTimeMs: json["processing_time_ms"],
        optimizationInfo: json["optimization_info"] == null
            ? null
            : OptimizationInfo.fromJson(json["optimization_info"]),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "adapter_config": adapterConfig?.toJson(),
        "data": data?.toJson(),
        "message": message,
        "error": error,
        "timestamp": timestamp?.toIso8601String(),
        "processing_time_ms": processingTimeMs,
        "optimization_info": optimizationInfo?.toJson(),
      };
}

class AdapterConfig {
  String? service;
  String? operation;
  String? version;

  AdapterConfig({
    this.service,
    this.operation,
    this.version,
  });

  AdapterConfig copyWith({
    String? service,
    String? operation,
    String? version,
  }) =>
      AdapterConfig(
        service: service ?? this.service,
        operation: operation ?? this.operation,
        version: version ?? this.version,
      );

  factory AdapterConfig.fromJson(Map<String, dynamic> json) => AdapterConfig(
        service: json["service"],
        operation: json["operation"],
        version: json["version"],
      );

  Map<String, dynamic> toJson() => {
        "service": service,
        "operation": operation,
        "version": version,
      };
}

class Data {
  String? translatedText;
  String? sourceLang;
  String? targetLang;
  String? model;

  Data({
    this.translatedText,
    this.sourceLang,
    this.targetLang,
    this.model,
  });

  Data copyWith({
    String? translatedText,
    String? sourceLang,
    String? targetLang,
    String? model,
  }) =>
      Data(
        translatedText: translatedText ?? this.translatedText,
        sourceLang: sourceLang ?? this.sourceLang,
        targetLang: targetLang ?? this.targetLang,
        model: model ?? this.model,
      );

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        translatedText: json["translated_text"],
        sourceLang: json["source_lang"],
        targetLang: json["target_lang"],
        model: json["model"],
      );

  Map<String, dynamic> toJson() => {
        "translated_text": translatedText,
        "source_lang": sourceLang,
        "target_lang": targetLang,
        "model": model,
      };
}

class OptimizationInfo {
  bool? cached;
  bool? gpuUsed;
  int? processingTimeMs;
  int? modelCacheHits;
  int? resultCacheHits;

  OptimizationInfo({
    this.cached,
    this.gpuUsed,
    this.processingTimeMs,
    this.modelCacheHits,
    this.resultCacheHits,
  });

  OptimizationInfo copyWith({
    bool? cached,
    bool? gpuUsed,
    int? processingTimeMs,
    int? modelCacheHits,
    int? resultCacheHits,
  }) =>
      OptimizationInfo(
        cached: cached ?? this.cached,
        gpuUsed: gpuUsed ?? this.gpuUsed,
        processingTimeMs: processingTimeMs ?? this.processingTimeMs,
        modelCacheHits: modelCacheHits ?? this.modelCacheHits,
        resultCacheHits: resultCacheHits ?? this.resultCacheHits,
      );

  factory OptimizationInfo.fromJson(Map<String, dynamic> json) =>
      OptimizationInfo(
        cached: json["cached"],
        gpuUsed: json["gpu_used"],
        processingTimeMs: json["processing_time_ms"],
        modelCacheHits: json["model_cache_hits"],
        resultCacheHits: json["result_cache_hits"],
      );

  Map<String, dynamic> toJson() => {
        "cached": cached,
        "gpu_used": gpuUsed,
        "processing_time_ms": processingTimeMs,
        "model_cache_hits": modelCacheHits,
        "result_cache_hits": resultCacheHits,
      };
}
