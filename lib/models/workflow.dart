import '../utils/logger.dart';

class WorkflowInstance {
  String instanceId;
  String goId;
  String tenantId;
  String status;
  String startedBy;
  String startedAt;
  String? completedAt;
  String? currentLoId;
  Map<String, dynamic> instanceData;
  bool isTest;
  String version;

  WorkflowInstance({
    required this.instanceId,
    required this.goId,
    required this.tenantId,
    required this.status,
    required this.startedBy,
    required this.startedAt,
    this.completedAt,
    this.currentLoId,
    required this.instanceData,
    required this.isTest,
    required this.version,
  });

  factory WorkflowInstance.fromJson(Map<String, dynamic> json) {
    return WorkflowInstance(
      instanceId: json['instance_id'] ?? '',
      goId: json['go_id'] ?? '',
      tenantId: json['tenant_id'] ?? '',
      status: json['status'] ?? '',
      startedBy: json['started_by'] ?? '',
      startedAt: json['started_at'] ?? '',
      completedAt: json['completed_at'],
      currentLoId: json['current_lo_id'],
      instanceData: json['instance_data'] ?? {},
      isTest: json['is_test'] ?? false,
      version: json['version'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'instance_id': instanceId,
      'go_id': goId,
      'tenant_id': tenantId,
      'status': status,
      'started_by': startedBy,
      'started_at': startedAt,
      'completed_at': completedAt,
      'current_lo_id': currentLoId,
      'instance_data': instanceData,
      'is_test': isTest,
      'version': version,
    };
  }
}

class DropdownOption {
  String value;
  String label;

  DropdownOption({
    required this.value,
    required this.label,
  });

  factory DropdownOption.fromJson(Map<String, dynamic> json) {
    return DropdownOption(
      value: json['value'] ?? '',
      label: json['label'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'value': value,
      'label': label,
    };
  }
}

class InputFieldMetadata {
  String usage;
  bool isInformational;
  bool hasDropdownSource;

  InputFieldMetadata({
    this.usage = '',
    this.isInformational = false,
    this.hasDropdownSource = false,
  });

  factory InputFieldMetadata.fromJson(Map<String, dynamic>? json) {
    if (json == null) return InputFieldMetadata();
    return InputFieldMetadata(
      usage: json['usage'] ?? '',
      isInformational: json['is_informational'] ?? false,
      hasDropdownSource: json['has_dropdown_source'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'usage': usage,
      'is_informational': isInformational,
      'has_dropdown_source': hasDropdownSource,
    };
  }
}

class InputField {
  String inputId;
  int? inputStackId;
  String attributeId;
  String entityId;
  String displayName;
  String? dataType;
  String sourceType;
  bool required;
  String uiControl;
  bool isVisible;
  bool readOnly;
  List<String>? allowedValues;
  List<Map<String, dynamic>?>? validations;
  String contextualId;
  dynamic inputValue;
  bool hasDropdownSource;
  List<String>? dependencies;
  String? dependencyType;
  InputFieldMetadata metadata;
  List<DropdownOption>? dropdownOptions;
  bool? needsParentValue;
  List<String>? parentIds;

  InputField({
    required this.inputId,
    this.inputStackId,
    required this.attributeId,
    required this.entityId,
    required this.displayName,
    this.dataType,
    required this.sourceType,
    required this.required,
    required this.uiControl,
    required this.isVisible,
    this.readOnly = false,
    this.allowedValues,
    this.validations,
    required this.contextualId,
    this.inputValue,
    this.hasDropdownSource = false,
    this.dependencies,
    this.dependencyType,
    required this.metadata,
    this.dropdownOptions,
    this.needsParentValue,
    this.parentIds,
  });

  // Helper method to parse validations safely
  static List<Map<String, dynamic>?>? _parseValidations(dynamic validations) {
    if (validations == null) {
      return null;
    }

    try {
      if (validations is List) {
        return List<Map<String, dynamic>?>.from(validations.map((v) {
          if (v is Map<String, dynamic>) {
            return v;
          } else if (v is Map) {
            // Convert to Map<String, dynamic>
            return Map<String, dynamic>.from(v);
          } else {
            Logger.warning('Unexpected validation format: $v');
            return null;
          }
        }));
      } else if (validations is Map) {
        // If it's a single map, wrap it in a list
        return [Map<String, dynamic>.from(validations)];
      }
    } catch (e) {
      Logger.error('Error parsing validations: $e');
    }

    return null;
  }

  factory InputField.fromJson(Map<String, dynamic> json) {
    try {
      Logger.info('Parsing InputField: $json');

      // Parse dropdown options if available
      List<DropdownOption>? dropdownOptions;
      if (json['dropdown_options'] != null) {
        dropdownOptions = (json['dropdown_options'] as List)
            .map((x) => DropdownOption.fromJson(x as Map<String, dynamic>))
            .toList();
      }

      // Parse dependencies if available
      List<String>? dependencies;
      if (json['dependencies'] != null) {
        dependencies = List<String>.from(json['dependencies']);
      }

      // Parse parent IDs if available
      List<String>? parentIds;
      if (json['parent_ids'] != null) {
        parentIds = List<String>.from(json['parent_ids']);
      }

      return InputField(
        inputId: json['input_id'] ?? '',
        inputStackId: json['input_stack_id'],
        attributeId: json['attribute_id'] ?? '',
        entityId: json['entity_id'] ?? '',
        displayName: json['display_name'] ?? '',
        dataType: json['data_type'],
        sourceType: json['source_type'] ?? '',
        required: json['required'] ?? false,
        uiControl: json['ui_control'] ?? '',
        isVisible: json['is_visible'] ?? true,
        readOnly: json['read_only'] ?? false,
        allowedValues: json['allowed_values'] != null
            ? List<String>.from(json['allowed_values'])
            : null,
        validations: _parseValidations(json['validations']),
        contextualId: json['contextual_id'] ?? '',
        inputValue: json['input_value'],
        hasDropdownSource: json['has_dropdown_source'] ?? false,
        dependencies: dependencies,
        dependencyType: json['dependency_type'],
        metadata: InputFieldMetadata.fromJson(
            json['metadata'] as Map<String, dynamic>?),
        dropdownOptions: dropdownOptions,
        needsParentValue: json['needs_parent_value'],
        parentIds: parentIds,
      );
    } catch (e) {
      Logger.error('Error parsing InputField: $e');
      return InputField(
        inputId: '',
        attributeId: '',
        entityId: '',
        displayName: 'Error parsing field',
        dataType: 'String',
        sourceType: 'user',
        required: false,
        uiControl: 'oj-input-text',
        isVisible: true,
        readOnly: false,
        contextualId: '',
        metadata: InputFieldMetadata(),
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'input_id': inputId,
      'input_stack_id': inputStackId,
      'attribute_id': attributeId,
      'entity_id': entityId,
      'display_name': displayName,
      'data_type': dataType,
      'source_type': sourceType,
      'required': required,
      'ui_control': uiControl,
      'is_visible': isVisible,
      'read_only': readOnly,
      'allowed_values': allowedValues,
      'validations': validations?.map((v) => v).toList(),
      'contextual_id': contextualId,
      'input_value': inputValue,
      'has_dropdown_source': hasDropdownSource,
      'dependencies': dependencies,
      'dependency_type': dependencyType,
      'metadata': metadata.toJson(),
      'dropdown_options': dropdownOptions?.map((x) => x.toJson()).toList(),
      'needs_parent_value': needsParentValue,
      'parent_ids': parentIds,
    };
  }
}

class WorkflowInputs {
  String localObjective;
  List<InputField> userInputs;
  List<InputField> systemInputs;
  List<InputField> infoInputs;
  List<InputField> dependentInputs;
  Map<String, List<String>> dependencies;

  WorkflowInputs({
    required this.localObjective,
    required this.userInputs,
    this.systemInputs = const [],
    this.infoInputs = const [],
    this.dependentInputs = const [],
    this.dependencies = const {},
  });

  // Legacy support - combine all input types into a single list
  List<InputField> get inputFields {
    return [...userInputs, ...infoInputs, ...dependentInputs];
  }

  // Legacy support - provide system inputs through the old property name
  List<InputField> get inputFieldsSystem {
    return systemInputs;
  }

  factory WorkflowInputs.fromJson(dynamic json) {
    try {
      // Log the input type and content for debugging
      Logger.info('WorkflowInputs.fromJson received type: ${json.runtimeType}');
      Logger.info('WorkflowInputs.fromJson received data: $json');

      // Handle the new API response format
      if (json is Map && json.containsKey('local_objective')) {
        Logger.info('Parsing new API response format');

        // Parse user_inputs
        List<InputField> userInputs = [];
        if (json.containsKey('user_inputs') && json['user_inputs'] is List) {
          userInputs = (json['user_inputs'] as List)
              .map((x) => InputField.fromJson(x as Map<String, dynamic>))
              .toList();
          userInputs.sort((a, b) => a.attributeId.compareTo(b.attributeId));
          Logger.info('Parsed ${userInputs.length} user inputs');
        }

        // Parse system_inputs
        List<InputField> systemInputs = [];
        if (json.containsKey('system_inputs') &&
            json['system_inputs'] is List) {
          systemInputs = (json['system_inputs'] as List)
              .map((x) => InputField.fromJson(x as Map<String, dynamic>))
              .toList();
          systemInputs.sort((a, b) => a.attributeId.compareTo(b.attributeId));
          Logger.info('Parsed ${systemInputs.length} system inputs');
        }

        // Parse info_inputs
        List<InputField> infoInputs = [];
        if (json.containsKey('info_inputs') && json['info_inputs'] is List) {
          infoInputs = (json['info_inputs'] as List)
              .map((x) => InputField.fromJson(x as Map<String, dynamic>))
              .toList();
          infoInputs.sort((a, b) => a.attributeId.compareTo(b.attributeId));
          Logger.info('Parsed ${infoInputs.length} info inputs');
        }

        // Parse dependent_inputs
        List<InputField> dependentInputs = [];
        if (json.containsKey('dependent_inputs') &&
            json['dependent_inputs'] is List) {
          dependentInputs = (json['dependent_inputs'] as List)
              .map((x) => InputField.fromJson(x as Map<String, dynamic>))
              .toList();
          dependentInputs
              .sort((a, b) => a.attributeId.compareTo(b.attributeId));
          Logger.info('Parsed ${dependentInputs.length} dependent inputs');
        }

        // Parse dependencies
        Map<String, List<String>> dependencies = {};
        if (json.containsKey('dependencies') && json['dependencies'] is Map) {
          final dependenciesMap = json['dependencies'] as Map;
          dependenciesMap.forEach((key, value) {
            if (value is List) {
              dependencies[key.toString()] = List<String>.from(value);
            }
          });
          Logger.info('Parsed dependencies for ${dependencies.length} fields');
        }

        return WorkflowInputs(
          localObjective: json['local_objective'] ?? '',
          userInputs: userInputs,
          systemInputs: systemInputs,
          infoInputs: infoInputs,
          dependentInputs: dependentInputs,
          dependencies: dependencies,
        );
      }

      // Legacy format support - for backward compatibility
      if (json is Map && json.containsKey('input_fields')) {
        Logger.info('Parsing legacy format with input_fields');

        // Parse input_fields
        List<InputField> inputFields = [];
        if (json['input_fields'] is List) {
          inputFields = (json['input_fields'] as List)
              .map((x) => InputField.fromJson(x as Map<String, dynamic>))
              .toList();
          inputFields.sort((a, b) => a.attributeId.compareTo(b.attributeId));
        }

        // Parse input_fields_system
        List<InputField> systemInputs = [];
        if (json.containsKey('input_fields_system') &&
            json['input_fields_system'] is List) {
          systemInputs = (json['input_fields_system'] as List)
              .map((x) => InputField.fromJson(x as Map<String, dynamic>))
              .toList();
          systemInputs.sort((a, b) => a.attributeId.compareTo(b.attributeId));
        }

        return WorkflowInputs(
          localObjective: json['local_objective'] ?? '',
          userInputs: inputFields,
          systemInputs: systemInputs,
        );
      }

      // Handle the case where json is a List (legacy format)
      if (json is List) {
        Logger.info('Parsing legacy list format');
        final inputFields = json
            .map((x) => InputField.fromJson(x as Map<String, dynamic>))
            .toList();
        inputFields.sort((a, b) => a.attributeId.compareTo(b.attributeId));

        return WorkflowInputs(
          localObjective: '',
          userInputs: inputFields,
        );
      }

      // Default empty case
      Logger.warning('Could not parse input, returning empty WorkflowInputs');
      return WorkflowInputs(
        localObjective: '',
        userInputs: [],
      );
    } catch (e) {
      // Log the error and return an empty object
      Logger.error('Error parsing WorkflowInputs: $e');
      return WorkflowInputs(
        localObjective: '',
        userInputs: [],
      );
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> result = {
      'local_objective': localObjective,
      'user_inputs': userInputs.map((x) => x.toJson()).toList(),
      'system_inputs': systemInputs.map((x) => x.toJson()).toList(),
    };

    if (infoInputs.isNotEmpty) {
      result['info_inputs'] = infoInputs.map((x) => x.toJson()).toList();
    }

    if (dependentInputs.isNotEmpty) {
      result['dependent_inputs'] =
          dependentInputs.map((x) => x.toJson()).toList();
    }

    if (dependencies.isNotEmpty) {
      result['dependencies'] = dependencies;
    }

    return result;
  }
}
