import 'dart:math' show max;
import 'package:nsl/models/user.dart';
import 'package:nsl/providers/auth_provider.dart';
import 'package:nsl/screens/web/web_my_transactions_screen.dart';
import 'package:provider/provider.dart';
import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';
import '../../models/input_value_store.dart';
import '../../models/transaction_status.dart';
import '../../providers/transaction_provider.dart';
import '../../models/transaction.dart';
import '../../models/global_objective.dart';
import '../../widgets/chat_text_field.dart';
import '../../widgets/common/nsl_knowledge_loader.dart';
import '../../widgets/resizable_divider.dart';
import '../../utils/logger.dart';
import '../workflow_detail_screen_fixed.dart';
import 'web_existing_transactions_screen.dart';

class WebTransactScreen extends StatefulWidget {
  const WebTransactScreen({super.key});

  @override
  State<WebTransactScreen> createState() => _WebTransactScreenState();
}

class _WebTransactScreenState extends State<WebTransactScreen>
    with SingleTickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _textController = TextEditingController();

  bool _isLoading = false;

  late TabController _tabController;

  final _inputStore = InputValueStore();

  // Panel width state
  double _contentWidth = 0.0;
  double _detailsPanelWidth = 450.0;
  final double _minDetailsPanelWidth = 250.0;
  final double _maxDetailsPanelWidth = 600.0;
  bool _showDetailsPanel = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Add listener to clear details panel when switching tabs
    _tabController.addListener(_handleTabChange);

    _loadData();
    _searchController.addListener(_filterTransactions);
  }

  // Handle tab changes and clear the details panel
  void _handleTabChange() {
    // Only execute when the tab index actually changes (not during animation)
    if (!_tabController.indexIsChanging) {
      // Clear all selections in the provider
      final transactionProvider =
          Provider.of<TransactionProvider>(context, listen: false);
      transactionProvider.clearAllSelections();
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    _textController.dispose();

    // Remove the tab controller listener before disposing
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();

    super.dispose();
  }

  void _filterTransactions() {
    // This will be implemented to filter transactions based on search text
    setState(() {});
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load global objectives for transaction creation
      final provider = Provider.of<TransactionProvider>(context, listen: false);
      await provider.fetchGlobalObjectives();

      // Load transactions
      // await provider.fetchTransactions();
    } catch (e) {
      // Handle error
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(context.tr('transaction.errorLoadingData',
                  args: {'error': e.toString()}))),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Check screen width to determine if details panel should be shown
    final screenWidth = MediaQuery.of(context).size.width;
    _showDetailsPanel = screenWidth > 860;

    // Calculate content width on first build
    if (_contentWidth == 0.0) {
      // Get screen width and subtract sidebar width (70) and details panel width if shown
      setState(() {
        if (_showDetailsPanel) {
          _contentWidth =
              screenWidth - 70 - _detailsPanelWidth - 8; // 8 is divider width
        } else {
          _contentWidth = screenWidth - 70;
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface.withAlpha(245),
      body: Row(
        children: [
          // Navigation Sidebar
          // const WebNavigationSidebar(currentScreen: 'transact'),

          // Main Content Area
          SizedBox(
            width: _contentWidth,
            child: Column(
              children: [
                Expanded(
                  child: Consumer<TransactionProvider>(
                    builder: (context, transactionProvider, _) {
                      // Define the refresh function
                      Future<void> refreshData() async {
                        // Refresh both global objectives and transactions
                        await transactionProvider.fetchGlobalObjectives();
                        await transactionProvider.fetchTransactions();
                      }

                      // Show loading indicator when fetching initial data
                      if (transactionProvider.isLoading &&
                          transactionProvider.globalObjectives.isEmpty) {
                        return NSLKnowledgeLoaderWrapper(
                          isLoading: true,
                          child: Container(),
                        );
                      }

                      return RefreshIndicator(
                        onRefresh: refreshData,
                        child: _buildMainContent(transactionProvider),
                      );
                    },
                  ),
                ),

                // Chat text field at the bottom
                Consumer<TransactionProvider>(
                  builder: (context, transactionProvider, _) {
                    return ChatTextField(
                      controller: _textController,
                      hintText:
                          context.tr('transaction.enterTransactionDetails'),
                      isLoading: transactionProvider.isLoading,
                      onSubmitted: (value) {
                        // Execute the transaction
                        // Store the scaffold messenger before the async gap
                      },
                      onCancel: () {},
                    );
                  },
                ),
              ],
            ),
          ),

          // Only show resizable divider and details panel if screen is wide enough
          if (_showDetailsPanel) ...[
            // Resizable Divider
            ResizableDivider(
              onResize: (delta) {
                setState(() {
                  // Adjust content width (increase when dragging right, decrease when dragging left)
                  // Positive delta means dragging right, negative means dragging left
                  _contentWidth =
                      (_contentWidth + delta).clamp(300.0, double.infinity);

                  // Calculate new details panel width based on screen width
                  final screenWidth = MediaQuery.of(context).size.width;
                  _detailsPanelWidth = screenWidth -
                      70 -
                      _contentWidth -
                      8; // 8 is divider width

                  // Ensure details panel width stays within bounds
                  if (_detailsPanelWidth < _minDetailsPanelWidth) {
                    _detailsPanelWidth = _minDetailsPanelWidth;
                    _contentWidth = screenWidth - 70 - _detailsPanelWidth - 8;
                  } else if (_detailsPanelWidth > _maxDetailsPanelWidth) {
                    _detailsPanelWidth = _maxDetailsPanelWidth;
                    _contentWidth = screenWidth - 70 - _detailsPanelWidth - 8;
                  }
                });
              },
            ),

            // Right Sidebar (Details Panel)
            Container(
              width: _detailsPanelWidth,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface.withAlpha(240),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).shadowColor.withAlpha(10),
                    blurRadius: 4,
                    offset: const Offset(-2, 0),
                  ),
                ],
              ),
              child: _buildDetailsPanel(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMainContent(TransactionProvider transactionProvider) {
    final User? user = Provider.of<AuthProvider>(context, listen: false).user;
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with greeting and title
          Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 24.0),
            child: Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.tr('transaction.greeting', args: {
                        'name': user?.name ?? context.tr('common.user')
                      }),
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      context.tr('transaction.welcomeMessage'),
                      style: TextStyle(
                        fontSize: 16,
                        color: Theme.of(context).textTheme.bodySmall?.color,
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                // Search Field for web
                Container(
                  width: 300,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Theme.of(context).dividerColor,
                      width: 1,
                    ),
                  ),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: context.tr('transaction.searchTransactions'),
                      prefixIcon: const Icon(Icons.search, size: 20),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(vertical: 10),
                    ),
                    style: TextStyle(fontSize: 14),
                  ),
                ),
              ],
            ),
          ),

          // Tab Bar for Transactions and Global Objectives
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
            ),
            child: TabBar(
              controller: _tabController,
              tabs: [
                Tab(text: context.tr('transaction.globalObjectives')),
                Tab(text: context.tr('transaction.myTransactions')),
              ],
              labelColor: Theme.of(context).colorScheme.primary,
              unselectedLabelColor: Theme.of(context).colorScheme.onSurface,
              indicatorColor: Theme.of(context).colorScheme.primary,
            ),
          ),

          const SizedBox(height: 16),

          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // Global Objectives Tab
                _buildGlobalObjectivesTab(transactionProvider),

                // My Transactions Tab
                WebMyTransactionsScreen(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGlobalObjectivesTab(TransactionProvider transactionProvider) {
    if (_isLoading) {
      return NSLKnowledgeLoaderWrapper(
        isLoading: true,
        child: Container(),
      );
    }

    if (transactionProvider.globalObjectives.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.category_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.primary.withAlpha(100),
            ),
            SizedBox(height: 16),
            Text(
              context.tr('transaction.noObjectives'),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).textTheme.bodySmall?.color,
              ),
            ),
          ],
        ),
      );
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate the number of columns based on available width
        // Minimum card width of 320px
        final double cardWidth = 320.0;
        final int crossAxisCount =
            max(1, (constraints.maxWidth / cardWidth).floor());

        // Calculate aspect ratio based on available height and width
        // This makes cards shorter on smaller screens
        final double screenHeight = MediaQuery.of(context).size.height;
        final double screenWidth = MediaQuery.of(context).size.width;

        // Adjust aspect ratio based on screen dimensions
        // Wider aspect ratio (more width than height) on smaller screens
        // Default is 1.5 (width is 1.5x the height)
        double aspectRatio = 1.5;

        // Make cards shorter on smaller screens
        if (screenHeight < 800) {
          aspectRatio = 2.0; // Wider cards (less height) on smaller screens
        } else if (screenWidth < 1400) {
          aspectRatio = 1.8; // Slightly wider cards on medium screens
        }

        return GridView.builder(
          padding: EdgeInsets.all(16),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: aspectRatio,
          ),
          itemCount: transactionProvider.globalObjectives.length,
          itemBuilder: (context, index) {
            final objective = transactionProvider.globalObjectives[index];
            return Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              child: InkWell(
                onTap: () {
                  // Select the objective and show details in the details panel
                  final transactionProvider =
                      Provider.of<TransactionProvider>(context, listen: false);
                  transactionProvider.selectGlobalObjective(objective);
                },
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.category_outlined,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              objective.name,
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                      Text(
                        context.tr('transaction.objectiveId',
                            args: {'id': objective.objectiveId}),
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).textTheme.bodySmall?.color,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        context.tr('transaction.objectiveStatus',
                            args: {'status': objective.status}),
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).textTheme.bodySmall?.color,
                        ),
                      ),
                      Spacer(),
                      Align(
                        alignment: Alignment.centerRight,
                        child: ElevatedButton(
                          onPressed: () {
                            // Check if GO is available in user's transactions
                            checkGoAvailability(objective);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                Theme.of(context).colorScheme.primary,
                            foregroundColor: Colors.white,
                          ),
                          child:
                              Text(context.tr('transaction.startTransaction')),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  void showObjectiveDetailsDialog(GlobalObjective objective) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr('transaction.objectiveDetails')),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(context.tr('transaction.objectiveName',
                args: {'name': objective.name})),
            SizedBox(height: 8),
            Text(context.tr('transaction.objectiveId',
                args: {'id': objective.objectiveId})),
            SizedBox(height: 8),
            Text(context.tr('transaction.objectiveStatus',
                args: {'status': objective.status})),
            SizedBox(height: 8),
            Text(context.tr('transaction.objectiveVersion',
                args: {'version': objective.version.toString()})),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(context.tr('common.close')),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              checkGoAvailability(objective);
            },
            child: Text(context.tr('transaction.startTransaction')),
          ),
        ],
      ),
    );
  }

  // Check if a GO is available in the user's transactions
  Future<void> checkGoAvailability(GlobalObjective objective) async {
    // Show loading indicator
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    scaffoldMessenger.showSnackBar(
      SnackBar(
        content: Text(context.tr('transaction.checkingExistingTransactions')),
        duration: Duration(seconds: 1),
      ),
    );

    // Check if the GO is available in the user's transactions
    final transactionProvider =
        Provider.of<TransactionProvider>(context, listen: false);
    final transactions = await transactionProvider
        .validateGoAvailableInMyTransaction(objective.objectiveId);

    // Hide loading indicator
    scaffoldMessenger.hideCurrentSnackBar();

    // Check if the widget is still mounted before using context
    if (!mounted) return;

    // Log the transactions data for debugging
    Logger.info('Transactions data: $transactions');

    // If there are existing transactions, show a popup dialog
    if (transactions.isNotEmpty) {
      // Show dialog asking if user wants to see existing transactions
      showDialog(
        context: context,
        builder: (dialogContext) => AlertDialog(
          title: Text(context.tr('transaction.existingTransactions')),
          content: Text(context.tr('transaction.existingTransactionsQuestion')),
          actions: [
            TextButton(
              onPressed: () {
                // Close the dialog
                Navigator.of(dialogContext).pop();

                // Show the start new transaction dialog
                startNewTransaction(objective);
              },
              child: Text(context.tr('transaction.no')),
            ),
            ElevatedButton(
              onPressed: () {
                // Close the dialog
                Navigator.of(dialogContext).pop();

                // Navigate to existing transactions screen
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => WebExistingTransactionsScreen(
                      objective: objective,
                      transactions: transactions,
                    ),
                  ),
                );
              },
              child: Text(context.tr('transaction.yes')),
            ),
          ],
        ),
      );
    } else {
      // If no existing transactions, show the start new transaction dialog
      startNewTransaction(objective);
    }
  }

  void startNewTransaction(GlobalObjective objective) {
    // Show dialog to start a new transaction with the selected objective
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr('transaction.startNewTransaction')),
        content: Text(context.tr('transaction.startNewTransactionWith',
            args: {'name': objective.name})),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(context.tr('common.cancel')),
          ),
          ElevatedButton(
            onPressed: () {
              // Create a new transaction
              _inputStore.clear();
              Navigator.pop(context);
              // Navigate to workflow detail screen
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) =>
                      WorkflowDetailScreen(objective: objective),
                ),
              );
            },
            child: Text(context.tr('common.start')),
          ),
        ],
      ),
    );
  }

  Widget buildStatusChip(TransactionStatus status) {
    Color chipColor;
    String statusText;

    switch (status) {
      case TransactionStatus.pending:
        chipColor = Colors.orange;
        statusText = context.tr('transaction.pending');
        break;
      case TransactionStatus.completed:
        chipColor = Colors.green;
        statusText = context.tr('transaction.completed');
        break;
      case TransactionStatus.failed:
        chipColor = Colors.red;
        statusText = context.tr('transaction.failed');
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor.withAlpha(30),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: chipColor,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildDetailsPanel() {
    return Consumer<TransactionProvider>(
      builder: (context, transactionProvider, _) {
        final selectedTransaction = transactionProvider.selectedTransaction;
        final selectedGlobalObjective =
            transactionProvider.selectedGlobalObjective;

        // Determine what type of content to show
        final bool isGroupedTransaction = selectedTransaction?.data != null &&
            selectedTransaction!.data.containsKey('isGrouped') &&
            selectedTransaction.data['isGrouped'] == true;

        // Determine the panel title based on what's selected
        String panelTitle = context.tr('common.details');
        if (selectedTransaction != null) {
          panelTitle = isGroupedTransaction
              ? context.tr('transaction.groupedTransactionDetails')
              : context.tr('transaction.transactionDetails');
        } else if (selectedGlobalObjective != null) {
          panelTitle = context.tr('transaction.globalObjectiveDetails');
        }

        return Column(
          children: [
            // Header
            Container(
              height: 64,
              alignment: Alignment.centerLeft,
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(10),
                    blurRadius: 2,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Text(
                    panelTitle,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: selectedTransaction != null
                  // Show transaction details
                  ? isGroupedTransaction
                      ? _buildGroupedTransactionDetails(selectedTransaction)
                      : _buildSingleTransactionDetails(selectedTransaction)
                  // Show global objective details or placeholder
                  : selectedGlobalObjective != null
                      ? _buildGlobalObjectiveDetails(selectedGlobalObjective)
                      : _buildEmptyDetailsPlaceholder(),
            ),
          ],
        );
      },
    );
  }

  Widget _buildEmptyDetailsPlaceholder() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.info_outline,
            size: 48,
            color: Theme.of(context).colorScheme.primary.withAlpha(100),
          ),
          const SizedBox(height: 16),
          Text(
            context.tr('transaction.selectItemForDetails'),
            style: TextStyle(
              color: Theme.of(context).textTheme.bodySmall?.color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildGlobalObjectiveDetails(GlobalObjective objective) {
    return Container(
      margin: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(5),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Objective Icon and Name
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withAlpha(30),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.category_outlined,
                    color: Theme.of(context).colorScheme.primary,
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    objective.name,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Objective ID
            Text(
              context.tr('transaction.objectiveId', args: {'id': ''}),
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: Theme.of(context).colorScheme.secondary,
              ),
            ),
            const SizedBox(height: 4),
            Text(objective.objectiveId),
            const SizedBox(height: 16),

            // Tenant ID
            Text(
              context.tr('transaction.tenantId'),
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: Theme.of(context).colorScheme.secondary,
              ),
            ),
            const SizedBox(height: 4),
            Text(objective.tenantId),
            const SizedBox(height: 16),

            // Version
            Text(
              context.tr('transaction.version'),
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: Theme.of(context).colorScheme.secondary,
              ),
            ),
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.tertiary.withAlpha(30),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'v${objective.version}',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.tertiary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Status
            Text(
              context.tr('transaction.status'),
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: Theme.of(context).colorScheme.secondary,
              ),
            ),
            const SizedBox(height: 4),
            _buildObjectiveStatusChip(objective.status),
            const SizedBox(height: 32),

            // Action Buttons
            Center(
              child: ElevatedButton.icon(
                onPressed: () {
                  // Start a new transaction with this objective
                  checkGoAvailability(objective);
                },
                icon: const Icon(Icons.play_arrow, size: 16),
                label: Text(context.tr('transaction.startTransaction')),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildObjectiveStatusChip(String status) {
    Color chipColor;

    switch (status.toLowerCase()) {
      case 'active':
        chipColor = Colors.green;
        break;
      case 'draft':
        chipColor = Colors.orange;
        break;
      case 'inactive':
        chipColor = Colors.red;
        break;
      default:
        chipColor = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor.withAlpha(30),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        status,
        style: TextStyle(
          color: chipColor,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildGroupedTransactionDetails(Transaction transaction) {
    final data = transaction.data;
    final int loCount = data['loCount'] ?? 0;
    final List<dynamic> transactionIds = data['transactionIds'] ?? [];

    return Container(
      margin: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(5),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Transaction Name
            Text(
              context.tr('transaction.globalObjective'),
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: Theme.of(context).colorScheme.secondary,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              transaction.name,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Workflow ID
            Text(
              context.tr('transaction.workflowId'),
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: Theme.of(context).colorScheme.secondary,
              ),
            ),
            const SizedBox(height: 4),
            Text(transaction.id),
            const SizedBox(height: 16),

            // Date & Time
            Text(
              context.tr('transaction.dateTime'),
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: Theme.of(context).colorScheme.secondary,
              ),
            ),
            const SizedBox(height: 4),
            Text(transaction.timestamp.toString()),
            const SizedBox(height: 16),

            // Status
            Text(
              context.tr('transaction.status'),
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: Theme.of(context).colorScheme.secondary,
              ),
            ),
            const SizedBox(height: 4),
            buildStatusChip(transaction.status),
            const SizedBox(height: 24),

            // Created & Updated
            if (data.containsKey('createdAt') && data.containsKey('updatedAt'))
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.tr('transaction.created'),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: Theme.of(context).colorScheme.secondary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(_formatDate(data['createdAt'])),
                  const SizedBox(height: 16),
                  Text(
                    context.tr('transaction.lastUpdated'),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: Theme.of(context).colorScheme.secondary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(_formatDate(data['updatedAt'])),
                  const SizedBox(height: 24),
                ],
              ),

            // Local Objectives Section
            Text(
              context.tr('transaction.localObjectives'),
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Theme.of(context).dividerColor),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Local Objectives Count
                  Row(
                    children: [
                      Icon(
                        Icons.list_alt,
                        size: 16,
                        color: Theme.of(context).colorScheme.secondary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        context.tr('transaction.totalLocalObjectives',
                            args: {'count': loCount.toString()}),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.secondary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Local Objectives List
                  if (transactionIds.isNotEmpty)
                    _buildLocalObjectivesList(transactionIds),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Action Buttons
            if (transaction.status == TransactionStatus.pending)
              Center(
                child: ElevatedButton.icon(
                  onPressed: () {
                    // Resume the transaction workflow
                    _resumeTransaction(transaction);
                  },
                  icon: const Icon(Icons.play_arrow, size: 16),
                  label: Text(context.tr('transaction.resumeTransaction')),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 12),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocalObjectivesList(List<dynamic> transactionIds) {
    // Get transaction details from the provider
    final transactionProvider =
        Provider.of<TransactionProvider>(context, listen: false);
    final allTransactionDetails = transactionProvider.transactionDetails;

    // Filter transaction details based on the IDs in the list
    final relevantTransactions = allTransactionDetails
        .where((detail) => transactionIds.contains(detail.workflowInstanceId))
        .toList();

    if (relevantTransactions.isEmpty) {
      return Text(context.tr('transaction.noLocalObjectiveDetails'));
    }

    // Sort by updated date (newest first)
    relevantTransactions.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

    return Column(
      children: [
        // Header row
        Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withAlpha(25),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Row(
            children: [
              Expanded(
                flex: 3,
                child: Text(
                  context.tr('transaction.localObjective'),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  context.tr('transaction.status'),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  context.tr('transaction.updated'),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Data rows
        ...relevantTransactions.map((transaction) => Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: Theme.of(context).dividerColor,
                    width: 0.5,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: Text(
                      transaction.loName ?? 'N/A',
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color:
                            _getStatusColor(transaction.status).withAlpha(25),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        transaction.status,
                        style: TextStyle(
                          color: _getStatusColor(transaction.status),
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      _formatDate(transaction.updatedAt),
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).textTheme.bodySmall?.color,
                      ),
                    ),
                  ),
                ],
              ),
            )),
      ],
    );
  }

  Widget _buildSingleTransactionDetails(Transaction transaction) {
    return Container(
      margin: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(5),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Transaction ID
            Text(
              'Transaction ID',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: Theme.of(context).colorScheme.secondary,
              ),
            ),
            const SizedBox(height: 4),
            Text(transaction.id),
            const SizedBox(height: 16),

            // Date & Time
            Text(
              'Date & Time',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: Theme.of(context).colorScheme.secondary,
              ),
            ),
            const SizedBox(height: 4),
            Text(transaction.timestamp.toString()),
            const SizedBox(height: 16),

            // Description
            Text(
              'Description',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: Theme.of(context).colorScheme.secondary,
              ),
            ),
            const SizedBox(height: 4),
            Text(transaction.description),
            const SizedBox(height: 16),

            // Amount
            Text(
              'Amount',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: Theme.of(context).colorScheme.secondary,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              '\$${transaction.amount.toStringAsFixed(2)}',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 18,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),

            // Status
            Text(
              'Status',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: Theme.of(context).colorScheme.secondary,
              ),
            ),
            const SizedBox(height: 4),
            buildStatusChip(transaction.status),
            const SizedBox(height: 24),

            // Action Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: () {
                    editTransaction(transaction);
                  },
                  icon: const Icon(Icons.edit_outlined, size: 16),
                  label: const Text('Edit'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.surface,
                    foregroundColor: Theme.of(context).colorScheme.primary,
                    side: BorderSide(
                        color: Theme.of(context).colorScheme.primary),
                  ),
                ),
                if (transaction.status == TransactionStatus.pending)
                  ElevatedButton.icon(
                    onPressed: () {
                      completeTransaction(transaction);
                    },
                    icon: const Icon(Icons.check_circle_outline, size: 16),
                    label: const Text('Complete'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return dateString;
    }
  }

  void _resumeTransaction(Transaction transaction) {
    // Implementation for resuming a transaction
    // This would navigate to the workflow detail screen
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr('transaction.resumeTransaction')),
        content: Text(context.tr('transaction.resumeTransactionMessage')),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(context.tr('common.close')),
          ),
        ],
      ),
    );
  }

  void selectTransaction(Transaction transaction) {
    Provider.of<TransactionProvider>(context, listen: false)
        .selectTransaction(transaction);
  }

  void editTransaction(Transaction transaction) {
    // Show edit transaction dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr('transaction.editTransaction')),
        content: Text(context.tr('transaction.editTransactionMessage')),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(context.tr('common.close')),
          ),
        ],
      ),
    );
  }

  void completeTransaction(Transaction transaction) {
    // Complete the transaction
    Provider.of<TransactionProvider>(context, listen: false)
        .updateTransactionStatus(
      transaction.id,
      TransactionStatus.completed,
    );
  }

  void showNewTransactionDialog() {
    // Show new transaction dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr('transaction.newTransaction')),
        content: Text(context.tr('transaction.newTransactionMessage')),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(context.tr('common.cancel')),
          ),
          TextButton(
            onPressed: () {
              // Create a new transaction
              Provider.of<TransactionProvider>(context, listen: false)
                  .addTransaction(
                Transaction(
                  id: 'TX${DateTime.now().millisecondsSinceEpoch}',
                  name: 'New Transaction',
                  description: 'New Transaction',
                  timestamp: DateTime.now(),
                  amount: 100.0,
                  status: TransactionStatus.pending,
                  data: {},
                ),
              );
              Navigator.pop(context);
            },
            child: Text(context.tr('common.create')),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'failed':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
