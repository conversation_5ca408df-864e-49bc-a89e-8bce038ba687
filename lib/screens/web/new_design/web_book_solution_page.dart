import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/chat_field.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';
import 'add_modules_screen.dart';

class WebBookSolutionPage extends StatefulWidget {
  final Map<String, dynamic>? initialData;

  const WebBookSolutionPage({super.key, this.initialData});

  @override
  State<WebBookSolutionPage> createState() => _WebBookSolutionPageState();
}

class _WebBookSolutionPageState extends State<WebBookSolutionPage> {
  List<SolutionItem> solutionItems = [];
  List<String> solutionOptions = [];

  // Hover state variables
  bool _isAddModulesHeaderButtonHovering = false;
  final TextEditingController chatController = TextEditingController();

  // State for showing input field
  bool showAddModules = false;

  @override
  void initState() {
    super.initState();
    // Initialize with sample data
    solutionItems = [
      SolutionItem(
        title: 'Create a solution of a Product management',
        lastMessageTime: '18 hours ago',
        date: '22/04/2023',
        versionId: 'V00512',
      ),
      // Add more items as needed
    ];

    // Initialize solution options for the modal
    solutionOptions = List.generate(12, (index) => 'Solutions');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      body: Row(
        children: [
          Expanded(child: SizedBox()),
          Expanded(
            flex: 9,
            child: Column(
              children: [
                // Header
                _buildHeader(),

                // Content
                Expanded(
                  child: Padding(
                    padding:
                        const EdgeInsets.symmetric(vertical: AppSpacing.lg),
                    child: showAddModules
                        ? _buildAddModulesContent()
                        : _buildSolutionsList(),
                  ),
                ),
                ChatField(
                  isLoading: false,
                  onSendMessage: () {},
                  onFileSelected: (fileName, filePath) {},
                  onToggleRecording: () {},
                  controller: chatController,
                )
              ],
            ),
          ),
          Expanded(child: SizedBox()),
        ],
      ),
    );
  }

  void _toggleAddModules() {
    setState(() {
      showAddModules = !showAddModules;
    });
  }

  Widget _buildHeader() {
    return SizedBox(
      height: 48,
      // padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          // Back button
          InkWell(
            child: const Icon(Icons.arrow_back, color: Colors.grey, size: 16),
            onTap: () {
              Provider.of<WebHomeProvider>(context, listen: false)
                  .currentScreenIndex = ScreenConstants.webMyLibrary;
            },
          ),
          const SizedBox(width: AppSpacing.xs),
          // Book name
          const Text(
            'Book Name',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),

          const SizedBox(width: AppSpacing.lg),

          // Add Modules dropdown
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            margin: EdgeInsets.only(right: 38),
            child: MouseRegion(
              cursor: SystemMouseCursors.click,
              onEnter: (_) {
                setState(() {
                  _isAddModulesHeaderButtonHovering = true;
                });
              },
              onExit: (_) {
                setState(() {
                  _isAddModulesHeaderButtonHovering = false;
                });
              },
              child: GestureDetector(
                onTap: _toggleAddModules,
                child: Container(
                  decoration: BoxDecoration(
                    color: _isAddModulesHeaderButtonHovering || showAddModules
                        ? Colors.white
                        : Colors.transparent,
                    border: Border.all(
                      color: showAddModules
                          ? Color(0xff0058FF)
                          : _isAddModulesHeaderButtonHovering
                              ? Color(0xff0058FF)
                              : Colors.transparent,
                      width: 1.5,
                    ),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  padding: EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                  child: Row(
                    children: [
                      SvgPicture.asset(
                        'assets/images/add-module.svg',
                        width: 20,
                        height: 20,
                        colorFilter: ColorFilter.mode(
                          _isAddModulesHeaderButtonHovering || showAddModules
                              ? Color(0xff0058FF)
                              : Colors.grey.shade600,
                          BlendMode.srcIn,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Add Modules',
                        style: TextStyle(
                          fontSize: 12,
                          color: _isAddModulesHeaderButtonHovering ||
                                  showAddModules
                              ? Color(0xff0058FF)
                              : Colors.black,
                          fontFamily: 'TiemposText',
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 2),
                      Icon(Icons.arrow_drop_down,
                          color: _isAddModulesHeaderButtonHovering ||
                                  showAddModules
                              ? Color(0xff0058FF)
                              : Colors.grey.shade600),
                    ],
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(width: 20),

          // Agent count
          _buildCountItem('assets/images/agent.svg', '3 Agent (V001)'),

          const SizedBox(width: 16),

          // Objects count
          _buildCountItem('assets/images/cube-box.svg', '12 Objects (V001)'),

          const SizedBox(width: 16),

          // Solutions count
          _buildCountItem(
              'assets/images/square-box-uncheck.svg', '15 Solutions'),

          const Spacer(),

          // Expand button
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: SvgPicture.asset(
              'assets/images/expand-arrow-left-new.svg',
              width: 20,
              height: 20,
              fit: BoxFit.fill,
              colorFilter: ColorFilter.mode(
                Colors.grey.shade600,
                BlendMode.srcIn,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCountItem(String iconPath, String text) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: Row(
        children: [
          SvgPicture.asset(
            iconPath,
            width: 12,
            height: 12,
            colorFilter: ColorFilter.mode(
              Colors.black,
              BlendMode.srcIn,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            text,
            style: const TextStyle(
              fontSize: 12,
              fontFamily: 'TiemposText',
              color: Colors.black,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSolutionsList() {
    return ListView.builder(
      itemCount: solutionItems.length,
      itemBuilder: (context, index) {
        final item = solutionItems[index];
        return _buildSolutionItem(item);
      },
    );
  }

  Widget _buildSolutionItem(SolutionItem item) {
    return Container(
      margin: const EdgeInsets.only(
        bottom: 16,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Color(0xffD0D0D0)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Left content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.title,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xff000000),
                      fontWeight: FontWeight.w600,
                      fontFamily: 'TiemposText',
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Last Message: ${item.lastMessageTime}',
                    style: TextStyle(
                      fontSize: 12,
                      fontFamily: 'TiemposText',
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),

            // Right content
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Row(
                  children: [
                    SvgPicture.asset(
                      'assets/images/folder.svg',
                      width: 16,
                      height: 16,
                      colorFilter: ColorFilter.mode(
                        Colors.grey,
                        BlendMode.srcIn,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      item.versionId,
                      style: TextStyle(
                        fontSize: 12,
                        fontFamily: 'TiemposText',
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  item.date,
                  style: TextStyle(
                    fontSize: 12,
                    fontFamily: 'TiemposText',
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddModulesContent() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Stack(
        children: [
          Column(
            children: [
              // Header row
              Container(
                height: 48,
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: Colors.grey.shade300),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 250, // left column
                      alignment: Alignment.centerLeft,
                      padding: EdgeInsets.only(left: 10),
                      decoration: BoxDecoration(
                        border: Border(
                          right: BorderSide(color: Colors.grey.shade300),
                        ),
                      ),
                      child: Text(
                        'Drag Your Solutions',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    Container(
                      width: 250, // middle column
                      alignment: Alignment.center,
                      padding: EdgeInsets.only(left: 0),
                      decoration: BoxDecoration(
                        border: Border(
                          right: BorderSide(color: Colors.grey.shade300),
                        ),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                'Modules',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black,
                                ),
                              ),
                              SizedBox(width: 12),
                              Container(
                                margin: EdgeInsets.symmetric(horizontal: 0),
                                padding: EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  border:
                                      Border.all(color: Colors.grey.shade900),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Row(
                                  children: [
                                    Icon(Icons.add,
                                        size: 16, color: Colors.grey.shade700),
                                    SizedBox(width: 4),
                                    Text('Modules',
                                        style: TextStyle(fontSize: 12)),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          // ...existing code for modules list if needed...
                        ],
                      ),
                    ),
                    Container(
                      width: 250, // right column
                      decoration: BoxDecoration(
                        border: Border(
                          right: BorderSide(color: Colors.grey.shade300),
                        ),
                      ),
                    ),
                    Expanded(child: Container()),
                  ],
                ),
              ),
              // Content row
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Left panel - Solutions list
                    Container(
                      width: 250,
                      decoration: BoxDecoration(
                        border: Border(
                          right: BorderSide(color: Colors.grey.shade300),
                        ),
                      ),
                      child: Stack(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(bottom: 40),
                            child: ListView.builder(
                              itemCount: solutionOptions.length,
                              itemBuilder: (context, index) {
                                return Container(
                                  padding: EdgeInsets.symmetric(
                                      vertical: 6, horizontal: 10),
                                  child: Text(
                                    'Solutions-${(index + 1).toString().padLeft(2, '0')}',
                                    style: TextStyle(
                                        fontSize: 14, color: Colors.black),
                                  ),
                                );
                              },
                            ),
                          ),
                          Positioned(
                            bottom: 0,
                            left: 0,
                            right: 0,
                            child: Container(
                              height: 40,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                border: Border(
                                  top: BorderSide(color: Colors.grey.shade300),
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.chevron_left,
                                      size: 18, color: Colors.grey.shade600),
                                  SizedBox(width: 8),
                                  Icon(Icons.chevron_right,
                                      size: 18, color: Colors.grey.shade600),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Middle panel - Modules
                    Container(
                      width: 250,
                      decoration: BoxDecoration(
                        border: Border(
                          right: BorderSide(color: Colors.grey.shade300),
                        ),
                      ),
                    ),
                    // Right panel - empty
                    Container(
                      width: 250,
                      decoration: BoxDecoration(
                        border: Border(
                          right: BorderSide(color: Colors.grey.shade300),
                        ),
                      ),
                    ),
                    // Empty panel on the right
                    Expanded(
                      child: Container(color: Colors.white),
                    ),
                  ],
                ),
              ),
            ],
          ),
          // Cross button in top right
          Positioned(
            top: 8,
            right: 8,
            child: GestureDetector(
              onTap: _toggleAddModules,
              child: Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.grey.shade200,
                ),
                padding: EdgeInsets.all(4),
                child: Icon(Icons.close, size: 20, color: Colors.grey.shade700),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class SolutionItem {
  final String title;
  final String lastMessageTime;
  final String date;
  final String versionId;

  SolutionItem({
    required this.title,
    required this.lastMessageTime,
    required this.date,
    required this.versionId,
  });
}
