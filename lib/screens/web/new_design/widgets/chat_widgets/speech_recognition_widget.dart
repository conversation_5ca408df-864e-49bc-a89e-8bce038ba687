import 'dart:async';
import 'package:flutter/material.dart';
import 'package:nsl/utils/logger.dart';

/// A widget for speech recognition that shows a timer,
/// cancel button, and confirm button.
class SpeechRecognitionWidget extends StatefulWidget {
  /// Callback when the user cancels speech recognition
  final VoidCallback onCancel;
  
  /// Callback when the user confirms speech recognition
  final VoidCallback onConfirm;
  
  /// The recognized text
  final String recognizedText;
  
  /// Constructor
  const SpeechRecognitionWidget({
    super.key,
    required this.onCancel,
    required this.onConfirm,
    required this.recognizedText,
  });

  @override
  State<SpeechRecognitionWidget> createState() => _SpeechRecognitionWidgetState();
}

class _SpeechRecognitionWidgetState extends State<SpeechRecognitionWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 300,
      height: 56,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          // Cancel button
          IconButton(
            icon: Icon(Icons.close, color: Colors.black),
            onPressed: widget.onCancel,
          ),
          
          // Timer display with a stateful approach to avoid delay
          Expanded(
            child: Center(
              child: _RecordingTimer(),
            ),
          ),
          
          // Confirm button
          Container(
            margin: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: Icon(Icons.check, color: Colors.white),
              onPressed: widget.onConfirm,
            ),
          ),
        ],
      ),
    );
  }
}

/// A stateful timer widget that starts immediately
class _RecordingTimer extends StatefulWidget {
  @override
  State<_RecordingTimer> createState() => _RecordingTimerState();
}

class _RecordingTimerState extends State<_RecordingTimer> {
  int _seconds = 0;
  late Timer _timer;
  
  @override
  void initState() {
    super.initState();
    // Start the timer immediately
    _startTimer();
  }
  
  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }
  
  void _startTimer() {
    // Start immediately with 0 seconds
    setState(() {
      _seconds = 0;
    });
    
    // Update every second
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      setState(() {
        _seconds++;
      });
    });
  }
  
  String _formatDuration() {
    final minutes = (_seconds ~/ 60).toString().padLeft(2, '0');
    final seconds = (_seconds % 60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }
  
  @override
  Widget build(BuildContext context) {
    return Text(
      _formatDuration(),
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
      ),
    );
  }
}
