import 'package:flutter/material.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/multimedia_widgets.dart';
import 'package:nsl/services/file_upload_ocr_service.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/logger.dart';

class AddButtonWithMenu extends StatefulWidget {
  final dynamic parentState;

  const AddButtonWithMenu(this.parentState, {super.key});

  @override
  State<AddButtonWithMenu> createState() => AddButtonWithMenuState();
}

class AddButtonWithMenuState extends State<AddButtonWithMenu> {
  bool isHovered = false;
  bool isMenuOpen = false;
  bool isLoading = false;
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;

  // File upload state
  bool isFileUploaded = false;
  String uploadedFileName = '';
  String extractedText = '';

  @override
  void dispose() {
    _removeMenu();
    super.dispose();
  }

  void _toggleMenu() {
    if (isMenuOpen) {
      _removeMenu();
    } else {
      _showMenu();
    }
  }

  void _removeMenu() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    setState(() {
      isMenuOpen = false;
    });
  }

  void _showMenu() {
    _removeMenu();

    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);

    // Set a fixed menu height
    final menuHeight = 100; // Approximate height of the menu

    // Get screen height to determine if menu should appear above or below
    final screenHeight = MediaQuery.of(context).size.height;

    // Check if there's enough space below the button
    final spaceBelow = screenHeight - (offset.dy + size.height);
    final showAbove = spaceBelow < menuHeight + 20; // Add some buffer space

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: offset.dx,
        // Position above or below based on available space, with minimal gap
        top: showAbove
            ? offset.dy - menuHeight + 15 // Position above with small gap
            : offset.dy + size.height - 5, // Position below with small gap
        child: Material(
          // elevation: 4.0,
          borderRadius: BorderRadius.circular(AppSpacing.xxs),
          child: Container(
            width: 190,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(AppSpacing.xxs),
              border: Border.all(color: Color(0xffC1C1C1), width: 1),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                AddButtonMenuItem(
                  imagePath: 'assets/images/upload_file.svg',
                  text: 'Upload A File',
                  onTap: () async {
                    _removeMenu();
                    await _uploadFile();
                  },
                ),
                AddButtonMenuItem(
                  imagePath: 'assets/images/screenshot.svg',
                  text: 'Take A Screenshot',
                  onTap: () {
                    _removeMenu();
                    // Handle screenshot action
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
    setState(() {
      isMenuOpen = true;
    });
  }

  // Upload file and process OCR
  Future<void> _uploadFile() async {
    // Create the service
    final fileUploadOcrService = FileUploadOcrService();

    // Store the context before async operation

    // Show loading overlay
    // _showLoadingOverlay(currentContext, 'Uploading file...');
    isLoading = true;

    try {
      // Process the file - now the file upload API returns OCR data directly
      final result = await fileUploadOcrService.processFileForChat();

      // Hide loading overlay
      // _hideLoadingOverlay();
      isLoading = false;

      // Check if widget is still mounted before updating UI
      if (!mounted) return;

      if (result['isCanceled'] ?? false) {
        isLoading = false;
        return;
      }
      if (result['success']) {
        // Update state with file info
        setState(() {
          isFileUploaded = true;
          uploadedFileName = result['fileName'];
          extractedText = result['extractedText'];
        });

        // Update parent state
        widget.parentState.setState(() {
          widget.parentState.uploadedFileName = result['fileName'];
          widget.parentState.uploadedFileText = result['extractedText'];
          widget.parentState.isFileUploaded = true;
          widget.parentState.fileUploadOcrResponse =
              result["fileUploadOcrResponse"];

          // Also update OCR panel if available
          if (widget.parentState.ocrFileName != null) {
            widget.parentState.ocrFileName = result['fileName'];
            widget.parentState.ocrText = result['extractedText'];
            // widget.parentState.showOcrPanel = true;
          }
        });

        // Show success message if still mounted

        fileUploadOcrService.showOverlay(
          context,
          'File uploaded: ${result['fileName']}',
        );

        // Log the extracted text
        Logger.info(
            'Extracted text to be sent with next message: ${result['extractedText']}');
      } else if (result['isCanceled'] != true) {
        // Show error message if not canceled and still mounted

        fileUploadOcrService.showOverlay(
          context,
          result['errorMessage'] ?? 'File upload failed',
          isError: true,
        );
      }
    } catch (e) {
      // Hide loading overlay
      // _hideLoadingOverlay();
      isLoading = false;

      fileUploadOcrService.showOverlay(
        context,
        'Error: $e',
        isError: true,
      );
    } finally {
      isLoading = false;
    }
  }

  // Show loading overlay

  // Hide loading overlay

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: MouseRegion(
        onEnter: (_) => setState(() => isHovered = true),
        onExit: (_) => setState(() => isHovered = false),
        cursor: SystemMouseCursors.click,
        child: Container(
          height: isLoading ? 16 : 28,
          width: isLoading ? 16 : 28,
          margin: EdgeInsets.symmetric(
              horizontal: AppSpacing.xs, vertical: AppSpacing.sm),
          padding: EdgeInsets.zero,
          decoration: BoxDecoration(
              color: isLoading
                  ? Colors.transparent
                  : isMenuOpen
                      ? Theme.of(context).colorScheme.primary
                      : isHovered
                          ? Theme.of(context).colorScheme.primary
                          : Color(0xffE4EDFF),
              borderRadius: BorderRadius.circular(AppSpacing.lg)),
          child: isLoading
              ? CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Color(0xff0058FF),
                  ),
                )
              : IconButton(
                  padding: EdgeInsets.zero,
                  onPressed: _toggleMenu,
                  icon: Icon(isMenuOpen ? Icons.close : Icons.add),
                  iconSize: 18,
                  color: isMenuOpen || isHovered ? Colors.white : null,
                ),
        ),
      ),
    );
  }
}
