// Custom expansion tile that handles ellipsis correctly
import 'package:flutter/material.dart';
import 'package:nsl/models/entities_data.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/custom_expansion_tile_new.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/web_home_screen_chat.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/logger.dart';
import 'package:nsl/widgets/custom_checkbox.dart';

class CustomExpansionTileWithEllipsis extends StatefulWidget {
  final Entity entity;
  final GlobalKey entityCardKey;
  final Function(bool) onExpansionChanged;
  final VoidCallback onTitleTap;
  final Color backgroundColor;
  final List<Widget> children;

  const CustomExpansionTileWithEllipsis({super.key, 
    required this.entity,
    required this.entityCardKey,
    required this.onExpansionChanged,
    required this.onTitleTap,
    this.backgroundColor = Colors.transparent,
    this.children = const [],
  });

  @override
  State<CustomExpansionTileWithEllipsis> createState() =>
      CustomExpansionTileWithEllipsisState();
}

class CustomExpansionTileWithEllipsisState
    extends State<CustomExpansionTileWithEllipsis> {
  late bool isExpanded;

  @override
  void initState() {
    super.initState();
    isExpanded = widget.entity.expanded ?? false;
  }

  @override
  void didUpdateWidget(CustomExpansionTileWithEllipsis oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.entity.expanded != widget.entity.expanded) {
      isExpanded = widget.entity.expanded ?? false;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Log the expansion state for debugging
    Logger.info(
        "Building CustomExpansionTile with expanded: $isExpanded for entity: ${widget.entity.title}");

    return CustomExpansionTileNew(
      title: Builder(
        builder: (context) {
          // Show with ellipsis when collapsed, without ellipsis when expanded
          return _buildEntityTitle(
            widget.entityCardKey,
            widget.entity,
            isExpanded,
          );
        },
      ),
      initiallyExpanded: isExpanded,
      onExpansionChanged: (expanded) {
        setState(() {
          isExpanded = expanded;
        });
        widget.onExpansionChanged(expanded);
      },
      onTitleTap: widget.onTitleTap,
      backgroundColor: widget.backgroundColor,
      children: widget.children,
    );
  }

  // Build entity title with or without ellipsis
  Widget _buildEntityTitle(
      GlobalKey entityCardKey, Entity entity, bool expanded) {
    // Get access to the global entities data from the parent widget
    final WebHomeScreenChatState? parentState =
        context.findAncestorStateOfType<WebHomeScreenChatState>();
    final entitiesData = parentState?.globalEntitiesData ?? EntitiesData();

    return Row(
      key: entityCardKey,
      children: [
        // Container(
        //   padding: EdgeInsets.only(
        //       left: 0,
        //       right: AppSpacing.xxs,
        //       top: AppSpacing.xs,
        //       bottom: AppSpacing.xs),
        //   decoration: BoxDecoration(
        //     border: Border(
        //         right: BorderSide(color: Colors.grey.shade300, width: 1)),
        //   ),
        //   child: CustomCheckbox(
        //     initialValue: entitiesData.getEntityCheckedState(entity.id ?? ''),
        //     onChanged: (bool value) {
        //       // Use a callback to update the parent state
        //       if (parentState != null) {
        //         parentState.setState(() {
        //           entitiesData.updateEntityCheckedState(entity.id ?? '', value);
        //         });
        //       }
        //     },
        //     materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        //   ),
        // ),
        // SizedBox(width: AppSpacing.xs),
        Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: AppSpacing.xs),
            child: Row(
              children: [
                // Create a combined RichText for the entire title with attributes
                Expanded(
                  child: Builder(
                    builder: (context) {
                      final GlobalKey titleKey =
                          GlobalKey(debugLabel: 'entityTitle_${entity.id}');

                      // Create a list of text spans for the combined title
                      List<InlineSpan> titleSpans = [];

                      // Add the entity title with bold style
                      TextSpan titleSpan = TextSpan(
                        text: entity.title ?? 'Untitled',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                          fontFamily: "TiemposText",
                        ),
                      );

                      // Wrap only the title in MouseRegion for tooltip
                      titleSpans.add(WidgetSpan(
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          onEnter: (_) {
                            // Show profile tooltip only when hovering on title
                            if (parentState != null) {
                              parentState.showProfileTooltip(titleKey, entity);
                            }
                          },
                          onExit: (_) {
                            // Hide profile tooltip when not hovering
                            if (parentState != null) {
                              parentState.hideProfileTooltip();
                            }
                          },
                          child: Text.rich(
                            TextSpan(
                                text: titleSpan.text, style: titleSpan.style),
                          ),
                        ),
                      ));

                      // Add attributes if they exist
                      if (entity.attributeString != null &&
                          entity.attributeString!.isNotEmpty) {
                        // Add " has " text
                        titleSpans.add(
                          TextSpan(
                            text: ' has ',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              color: Colors.grey.shade700,
                              fontFamily: "TiemposText",
                            ),
                          ),
                        );

                        // Add attribute spans
                        titleSpans.addAll(_buildAttributeSpans(
                          entity.attributeString ?? '',
                          entity.attributes ?? [],
                        ));
                      }

                      // Log the RichText properties for debugging
                      Logger.info(
                          "RichText with expanded: $expanded, overflow: ${!expanded ? 'ellipsis' : 'visible'}, maxLines: ${!expanded ? 1 : 'null'}");

                      return RichText(
                        key: titleKey,
                        text: TextSpan(
                            children: titleSpans,
                            style: TextStyle(
                              fontSize: 14,
                              fontFamily: "TiemposText",
                              textBaseline: TextBaseline.alphabetic,
                            )),
                        overflow: !expanded
                            ? TextOverflow.ellipsis
                            : TextOverflow.visible,
                        softWrap: expanded,
                        maxLines: !expanded ? 1 : null,
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Build attribute spans for the combined RichText
  List<InlineSpan> _buildAttributeSpans(
      String attributeString, List<Attribute> attributes) {
    // Log for debugging
    Logger.info("Building attribute spans for: $attributeString");

    if (attributeString.isEmpty) {
      return [];
    }

    // If there are no attributes, just return the original string as a span
    if (attributes.isEmpty) {
      return [
        TextSpan(
          text: attributeString,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
            color: Colors.black,
            fontFamily: 'TiemposText',
          ),
        ),
      ];
    }

    // Create a map of attribute names to their isPk status
    final Map<String, bool> primaryKeyMap = {};
    for (var attribute in attributes) {
      if (attribute.name != null) {
        primaryKeyMap[attribute.name!] = attribute.isPk ?? false;
      }
    }

    // Split the attribute string by commas and "and"
    List<String> parts = [];
    final andParts = attributeString.split(' and ');
    for (int i = 0; i < andParts.length; i++) {
      if (i < andParts.length - 1) {
        final commaParts = andParts[i].split(', ');
        parts.addAll(commaParts);
      } else {
        parts.add(andParts[i]);
      }
    }

    // Create a list of text spans for each attribute
    List<InlineSpan> textSpans = [];
    for (int i = 0; i < parts.length; i++) {
      String part = parts[i].trim();
      bool isPrimaryKey = false;

      for (var attrName in primaryKeyMap.keys) {
        if (part.contains(attrName) && primaryKeyMap[attrName] == true) {
          isPrimaryKey = true;
          break;
        }
      }

      // Add the attribute with or without PK indicator
      if (isPrimaryKey) {
        textSpans.add(
          TextSpan(
            text: part,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 14,
              color: Colors.black,
              fontFamily: 'TiemposText',
            ),
          ),
        );
        textSpans.add(TextSpan(text: ' '));
        textSpans.add(
          WidgetSpan(
            alignment: PlaceholderAlignment.top,
            child: Transform.translate(
              offset: Offset(0, -5),
              child: Text(
                '^PK',
                style: TextStyle(
                  fontSize: 9,
                  fontWeight: FontWeight.bold,
                  color: Color(0xff0058FF),
                  fontFamily: 'TiemposText',
                ),
              ),
            ),
          ),
        );
      } else {
        textSpans.add(
          TextSpan(
            text: part,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 14,
              color: Colors.black,
              fontFamily: 'TiemposText',
            ),
          ),
        );
      }

      // Add separator if not the last item
      if (i < parts.length - 1) {
        if (i == parts.length - 2) {
          textSpans.add(TextSpan(text: ' and '));
        } else {
          textSpans.add(TextSpan(text: ', '));
        }
      }
    }

    return textSpans;
  }
}
