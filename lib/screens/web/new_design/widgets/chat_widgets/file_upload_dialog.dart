import 'package:flutter/material.dart';
import 'package:nsl/services/multimedia_service.dart';
import 'package:nsl/utils/logger.dart';

/// A dialog for uploading files and processing them with OCR
class FileUploadDialog extends StatelessWidget {
  /// The parent state that will receive the OCR results
  final State parentState;
  
  /// The multimedia service to use for file processing
  final MultimediaService multimediaService;
  
  /// Constructor
  const FileUploadDialog({
    super.key,
    required this.parentState,
    required this.multimediaService,
  });
  
  @override
  Widget build(BuildContext context) {
    // Reference to the loading dialog
    BuildContext? loadingDialogContext;
    
    // Show a loading dialog
    void showLoadingDialog(String message) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext dialogContext) {
          loadingDialogContext = dialogContext;
          return AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                Sized<PERSON><PERSON>(height: 16),
                Text(message),
              ],
            ),
          );
        },
      );
    }
    
    // Hide the loading dialog
    void hideLoadingDialog() {
      if (loadingDialogContext != null) {
        Navigator.of(loadingDialogContext!).pop();
        loadingDialogContext = null;
      }
    }
    
    return AlertDialog(
      title: Text('Upload File'),
      content: Text('Select a file to upload and process with OCR'),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () async {
            // Close the dialog
            Navigator.of(context).pop();
            
            // Show loading dialog
            showLoadingDialog('Uploading and processing file...');
            
            try {
              // Process the file using the multimedia service
              await multimediaService.processFile(parentState.context);
            } catch (e) {
              Logger.error('Error processing file: $e');
            } finally {
              // Hide loading dialog
              hideLoadingDialog();
            }
          },
          child: Text('Select File'),
        ),
      ],
    );
  }
  
  /// Show the file upload dialog
  static void show(BuildContext context, State parentState, MultimediaService multimediaService) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return FileUploadDialog(
          parentState: parentState,
          multimediaService: multimediaService,
        );
      },
    );
  }
}
