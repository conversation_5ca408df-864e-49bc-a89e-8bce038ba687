import 'package:flutter/material.dart';
import 'package:nsl/widgets/common/nsl_knowledge_loader.dart';
import 'package:provider/provider.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/providers/auth_provider.dart';
import 'package:nsl/providers/screen_content_provider.dart';
import 'package:nsl/utils/logger.dart';
import 'package:nsl/utils/navigation_service.dart';
import 'package:nsl/widgets/common/loading_overlay.dart';
import 'package:nsl/widgets/web/web_navigation_sidebar.dart';

class WebHomeScreen extends StatefulWidget {
  const WebHomeScreen({super.key});

  @override
  State<WebHomeScreen> createState() => _WebHomeScreenState();
}

class _WebHomeScreenState extends State<WebHomeScreen> {
  bool _isLoggingOut = false; // Manual loading state for logout

  @override
  Widget build(BuildContext context) {
    return Consumer<ScreenContentProvider>(
      builder: (context, screenContentProvider, _) {
        return NSLKnowledgeLoaderWrapper(
          isLoading: _isLoggingOut,
          child: Scaffold(
            backgroundColor: Theme.of(context).colorScheme.surface.withAlpha(245),
            body: Stack(
              children: [
                Row(
                  children: [
                    // Navigation Sidebar
                    WebNavigationSidebar(
                      currentScreen: screenContentProvider.currentScreen,
                      onLogoutPressed: _showLogoutConfirmation,
                    ),
          
                    // Main content area
                    Expanded(
                      child: screenContentProvider.currentContent,
                    ),
                  ],
                ),
                // Full-screen loading overlay for logout
                // if (_isLoggingOut)
                //   LoadingOverlay(
                //     text: context.tr('auth.loggingOut'),
                //     color: Theme.of(context).colorScheme.error,
                //   ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Show logout confirmation dialog
  void _showLogoutConfirmation(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.logout, color: Theme.of(context).colorScheme.error),
              const SizedBox(width: 8),
              Text(context.tr('navigation.logout')),
            ],
          ),
          content: Text(context.tr('navigation.logoutConfirmation')),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(context.tr('common.cancel')),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();

                // Set loading state to true
                setState(() {
                  _isLoggingOut = true;
                });

                Logger.info('Logging out user');

                try {
                  // Perform logout
                  final success = await authProvider.logout();

                  if (success) {
                    // Navigate to login screen
                    NavigationService.navigateToLogin();
                  } else {
                    Logger.error('Logout failed');
                    // Error handling will be done in the finally block
                  }
                } catch (e) {
                  Logger.error('Error during logout: $e');
                  // Error handling will be done in the finally block
                } finally {
                  // Reset loading state if still mounted
                  if (mounted) {
                    setState(() {
                      _isLoggingOut = false;
                    });
                  }
                }
              },
              style: TextButton.styleFrom(
                foregroundColor: Theme.of(context).colorScheme.error,
              ),
              child: Text(context.tr('navigation.logout')),
            ),
          ],
        );
      },
    );
  }
}
