import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../l10n/app_localizations.dart';
import '../../models/input_value_store.dart';
import '../../providers/transaction_provider.dart';
import '../../models/transaction_detail.dart';
import '../../models/transaction.dart';
import '../../models/transaction_status.dart';
import '../../models/global_objective.dart';
import '../../screens/workflow_detail_screen_fixed.dart';
import '../../widgets/common/nsl_knowledge_loader.dart';

// Model class for grouped transactions
class GroupedTransaction {
  final String workflowInstanceId;
  final List<TransactionDetail> transactions;
  final String goName;
  final String goId;
  final String tenantId;
  final String userId;
  final String createdAt;
  final String updatedAt;

  GroupedTransaction({
    required this.workflowInstanceId,
    required this.transactions,
    required this.goName,
    required this.goId,
    required this.tenantId,
    required this.userId,
    required this.createdAt,
    required this.updatedAt,
  });

  // Get the status based on the last transaction in the group
  String get status {
    if (transactions.isEmpty) return 'Unknown';

    // Sort transactions by updatedAt to get the most recent one
    final sortedTransactions = List<TransactionDetail>.from(transactions);
    sortedTransactions.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

    return sortedTransactions.first.status;
  }

  // Get a display name for the group
  String get displayName => goName;

  // Get a description for the group
  String get description => 'Workflow ID: $workflowInstanceId';

  // Get the number of LOs in this group
  int get loCount => transactions.length;

  // Get the most recent transaction in the group
  TransactionDetail get latestTransaction {
    if (transactions.isEmpty) {
      throw Exception('No transactions in group');
    }

    final sortedTransactions = List<TransactionDetail>.from(transactions);
    sortedTransactions.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

    return sortedTransactions.first;
  }
}

// Sort options
enum SortField { date, name, id, status }

enum SortDirection { ascending, descending }

// Sort configuration class
class SortConfig {
  final SortField field;
  final SortDirection direction;

  const SortConfig(this.field, this.direction);
}

class WebMyTransactionsScreen extends StatefulWidget {
  const WebMyTransactionsScreen({super.key});

  @override
  State<WebMyTransactionsScreen> createState() =>
      _WebMyTransactionsScreenState();
}

class _WebMyTransactionsScreenState extends State<WebMyTransactionsScreen>
    with SingleTickerProviderStateMixin {
  bool _isLoading = true;
  late TabController _tabController;
  late List<String> _statusFilters;
  String _currentFilter = 'All';
  // Default sort configuration: date descending (newest first)
  SortConfig _currentSort =
      const SortConfig(SortField.date, SortDirection.descending);

  // Selected transaction for details panel
  TransactionDetail? _selectedTransaction;

  // Selected grouped transaction for details panel
  GroupedTransaction? _selectedGroupedTransaction;

  // Get the appropriate icon for the current sort direction
  IconData _getSortDirectionIcon() {
    return _currentSort.direction == SortDirection.ascending
        ? Icons.arrow_upward
        : Icons.arrow_downward;
  }

  @override
  void initState() {
    super.initState();

    // Initialize status filters
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        _statusFilters = [
          context.tr('transaction.all'),
          context.tr('transaction.pending'),
          context.tr('transaction.completed')
        ];
        // Initialize tab controller
        _tabController =
            TabController(length: _statusFilters.length, vsync: this);
        _tabController.addListener(() {
          if (!_tabController.indexIsChanging) {
            setState(() {
              _currentFilter = _statusFilters[_tabController.index];
            });
          }
        });
      });
    });

    _fetchTransactionDetails();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _fetchTransactionDetails() async {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    await provider.fetchTransactionDetails();
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Group transactions by workflow ID
  List<GroupedTransaction> _groupTransactionsByWorkflowId(
      List<TransactionDetail> transactions) {
    // Create a map to group transactions by workflow ID
    final Map<String, List<TransactionDetail>> groupedMap = {};

    // Group transactions by workflow ID
    for (final transaction in transactions) {
      if (!groupedMap.containsKey(transaction.workflowInstanceId)) {
        groupedMap[transaction.workflowInstanceId] = [];
      }
      groupedMap[transaction.workflowInstanceId]!.add(transaction);
    }

    // Convert the map to a list of GroupedTransaction objects
    final List<GroupedTransaction> result = [];

    groupedMap.forEach((workflowId, transactionsList) {
      // Use the first transaction for common properties
      final firstTransaction = transactionsList.first;

      // Find the latest transaction for createdAt and updatedAt
      transactionsList.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
      final latestTransaction = transactionsList.first;

      result.add(GroupedTransaction(
        workflowInstanceId: workflowId,
        transactions: transactionsList,
        goName: firstTransaction.goName,
        goId: firstTransaction.goId,
        tenantId: firstTransaction.tenantId,
        userId: firstTransaction.userId,
        createdAt: firstTransaction.createdAt,
        updatedAt: latestTransaction.updatedAt,
      ));
    });

    return result;
  }

  // Filter grouped transactions based on status
  List<GroupedTransaction> _filterGroupedTransactions(
      List<GroupedTransaction> groups, String filter) {
    List<GroupedTransaction> filteredGroups;

    if (filter == 'All') {
      filteredGroups = List.from(groups);
    } else {
      filteredGroups = groups.where((group) {
        return group.status.toLowerCase() == filter.toLowerCase();
      }).toList();
    }

    // Sort the filtered groups based on current sort configuration
    return _sortGroupedTransactions(filteredGroups);
  }

  // Sort grouped transactions based on the current sort configuration
  List<GroupedTransaction> _sortGroupedTransactions(
      List<GroupedTransaction> groups) {
    final sortedGroups = List<GroupedTransaction>.from(groups);

    sortedGroups.sort((a, b) {
      int comparison;
      switch (_currentSort.field) {
        case SortField.date:
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        case SortField.name:
          comparison = a.displayName.compareTo(b.displayName);
          break;
        case SortField.id:
          comparison = a.workflowInstanceId.compareTo(b.workflowInstanceId);
          break;
        case SortField.status:
          comparison = a.status.compareTo(b.status);
          break;
      }

      // Reverse the comparison if descending
      return _currentSort.direction == SortDirection.ascending
          ? comparison
          : -comparison;
    });

    return sortedGroups;
  }

  // Filter transactions based on the selected status (kept for backward compatibility)
  List<TransactionDetail> _filterTransactions(
      List<TransactionDetail> transactions, String filter) {
    if (filter == 'All') {
      return transactions;
    }
    return transactions
        .where((t) => t.status.toLowerCase() == filter.toLowerCase())
        .toList();
  }

  // Sort transactions based on the current sort configuration (kept for backward compatibility)
  List<TransactionDetail> _sortTransactions(
      List<TransactionDetail> transactions, SortConfig sortConfig) {
    final sortedList = List<TransactionDetail>.from(transactions);

    sortedList.sort((a, b) {
      int comparison;
      switch (sortConfig.field) {
        case SortField.date:
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        case SortField.name:
          comparison = a.displayName.compareTo(b.displayName);
          break;
        case SortField.id:
          comparison = a.workflowInstanceId.compareTo(b.workflowInstanceId);
          break;
        case SortField.status:
          comparison = a.status.compareTo(b.status);
          break;
      }

      // Reverse the comparison if descending
      return sortConfig.direction == SortDirection.ascending
          ? comparison
          : -comparison;
    });

    return sortedList;
  }

  void _toggleSortDirection() {
    setState(() {
      _currentSort = SortConfig(
        _currentSort.field,
        _currentSort.direction == SortDirection.ascending
            ? SortDirection.descending
            : SortDirection.ascending,
      );
    });
  }

  void _changeSortField(SortField field) {
    setState(() {
      _currentSort = SortConfig(field, _currentSort.direction);
    });
  }

  void _selectTransaction(TransactionDetail transaction) {
    setState(() {
      _selectedTransaction = transaction;
      _selectedGroupedTransaction = null;
    });
  }

  void _selectGroupedTransaction(GroupedTransaction group) {
    setState(() {
      _selectedGroupedTransaction = group;
      _selectedTransaction = group.latestTransaction;

      // Also update the selected transaction in the TransactionProvider
      // This will make it visible in the details panel of the parent screen
      final transactionProvider =
          Provider.of<TransactionProvider>(context, listen: false);

      // Create a Transaction object from the TransactionDetail
      final transaction = Transaction(
        id: group.workflowInstanceId,
        name: group.goName,
        description: 'Workflow ID: ${group.workflowInstanceId}',
        timestamp: DateTime.tryParse(group.updatedAt) ?? DateTime.now(),
        amount: 0.0, // Default value
        status: group.status.toLowerCase() == 'completed'
            ? TransactionStatus.completed
            : (group.status.toLowerCase() == 'failed'
                ? TransactionStatus.failed
                : TransactionStatus.pending),
        data: {
          'workflowInstanceId': group.workflowInstanceId,
          'goName': group.goName,
          'goId': group.goId,
          'status': group.status,
          'loCount': group.loCount,
          'createdAt': group.createdAt,
          'updatedAt': group.updatedAt,
          'isGrouped': true,
          'transactionIds':
              group.transactions.map((t) => t.workflowInstanceId).toList(),
        },
      );

      transactionProvider.selectTransaction(transaction);
    });
  }

  void _resumeTransaction(TransactionDetail transaction) {
    final inputStore = InputValueStore();
    inputStore.clear();

    // Create a GlobalObjective from the transaction data
    final objective = GlobalObjective(
      objectiveId: transaction.goId,
      name: transaction.loName ?? transaction.goName,
      tenantId: transaction.tenantId,
      version: '1.0', // Default version
      status: transaction.status,
    );

    // Navigate to the workflow detail screen with resume flag
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WorkflowDetailScreen(
          objective: objective,
          transaction: transaction,
          isResuming: true,
        ),
      ),
    );
  }

  void _resumeGroupedTransaction(GroupedTransaction group) {
    // Clear the input store before resuming
    final inputStore = InputValueStore();
    inputStore.clear();

    // Get the latest transaction in the group
    final latestTransaction = group.latestTransaction;

    // Create a GlobalObjective from the group data
    final objective = GlobalObjective(
      objectiveId: group.goId,
      name: group.goName,
      tenantId: group.tenantId,
      version: '1.0', // Default version
      status: group.status,
    );

    // Navigate to the workflow detail screen with resume flag
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WorkflowDetailScreen(
          objective: objective,
          transaction: latestTransaction,
          isResuming: true,
        ),
      ),
    );
  }

  Widget _buildNavigationSidebar() {
    return Container(
      width: 80,
      color: Theme.of(context).colorScheme.surface,
      child: Column(
        children: [
          // Logo
          Container(
            height: 80,
            width: 80,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 1,
                ),
              ),
            ),
            child: Text(
              'NSL',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),

          // Navigation Items
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildNavItem(Icons.chat_bubble_outline,
                    context.tr('navigation.chat'), false, () {
                  Navigator.pushReplacementNamed(context, '/chat');
                }),
                _buildNavItem(
                    Icons.build_outlined, context.tr('navigation.build'), false,
                    () {
                  Navigator.pushReplacementNamed(context, '/build');
                }),
                _buildNavItem(Icons.swap_horiz_outlined,
                    context.tr('navigation.transact'), false, () {
                  Navigator.pushReplacementNamed(context, '/transact');
                }),
                _buildNavItem(Icons.history,
                    context.tr('transaction.myTransactions'), true, () {
                  // Already on my transactions screen
                }),
                _buildNavItem(Icons.settings_outlined,
                    context.tr('navigation.settings'), false, () {
                  Navigator.pushReplacementNamed(context, '/settings');
                }),
                _buildNavItem(
                    Icons.help_outline, context.tr('navigation.help'), false,
                    () {
                  // Navigate to Help screen
                }),
              ],
            ),
          ),

          // User Profile
          Container(
            height: 80,
            width: 80,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 1,
                ),
              ),
            ),
            child: IconButton(
              icon: CircleAvatar(
                backgroundColor:
                    Theme.of(context).colorScheme.primary.withAlpha(30),
                child: Icon(
                  Icons.person_outline,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              onPressed: () {
                // Show profile options
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem(
      IconData icon, String label, bool isActive, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 80,
        width: 80,
        decoration: BoxDecoration(
          border: Border(
            left: BorderSide(
              color: isActive
                  ? Theme.of(context).colorScheme.primary
                  : Colors.transparent,
              width: 3,
            ),
          ),
          color: isActive
              ? Theme.of(context).colorScheme.primary.withAlpha(20)
              : Colors.transparent,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isActive
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.onSurface.withAlpha(180),
              size: 24,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: isActive
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.onSurface.withAlpha(180),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface.withAlpha(245),
      body: Row(
        children: [
          // Navigation Sidebar

          // Main Content
          Expanded(
            child: Column(
              children: [
                // App Bar
                // Container(
                //   height: 64,
                //   padding: const EdgeInsets.symmetric(horizontal: 24),
                //   decoration: BoxDecoration(
                //     color: Theme.of(context).colorScheme.surface,
                //     boxShadow: [
                //       BoxShadow(
                //         color: Theme.of(context).shadowColor.withAlpha(10),
                //         blurRadius: 2,
                //         offset: const Offset(0, 2),
                //       ),
                //     ],
                //   ),
                //   child: Row(
                //     children: [
                //       Text(
                //         'My Transactions',
                //         style: TextStyle(
                //           fontSize: 20,
                //           fontWeight: FontWeight.bold,
                //         ),
                //       ),
                //       const Spacer(),
                //       // Sort button
                //       PopupMenuButton<SortField>(
                //         tooltip: 'Sort by',
                //         icon: Row(
                //           mainAxisSize: MainAxisSize.min,
                //           children: [
                //             const Icon(Icons.sort),
                //             const SizedBox(width: 4),
                //             Icon(_getSortDirectionIcon(), size: 16),
                //           ],
                //         ),
                //         onSelected: _changeSortField,
                //         itemBuilder: (context) => [
                //           const PopupMenuItem(
                //             value: SortField.date,
                //             child: Text('Date'),
                //           ),
                //           const PopupMenuItem(
                //             value: SortField.name,
                //             child: Text('Name'),
                //           ),
                //           const PopupMenuItem(
                //             value: SortField.id,
                //             child: Text('ID'),
                //           ),
                //           const PopupMenuItem(
                //             value: SortField.status,
                //             child: Text('Status'),
                //           ),
                //         ],
                //       ),
                //       // Toggle sort direction
                //       IconButton(
                //         icon: Icon(_getSortDirectionIcon()),
                //         onPressed: _toggleSortDirection,
                //         tooltip:
                //             _currentSort.direction == SortDirection.ascending
                //                 ? 'Ascending'
                //                 : 'Descending',
                //       ),
                //     ],
                //   ),
                // ),

                // Tab Bar
                Container(
                  color: Theme.of(context).colorScheme.surface,
                  child: TabBar(
                    controller: _tabController,
                    tabs: _statusFilters
                        .map((status) => Tab(text: status))
                        .toList(),
                    isScrollable: true,
                  ),
                ),

                // Main Content
                Expanded(
                  child: _isLoading
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CircularProgressIndicator(),
                              const SizedBox(height: 24),
                              Text(
                                context.tr('transaction.loadingTransactions'),
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Theme.of(context)
                                      .textTheme
                                      .bodySmall
                                      ?.color,
                                ),
                              ),
                            ],
                          ),
                        )
                      : Row(
                          children: [
                            // Main content area - Transactions list
                            Expanded(
                              flex: 3,
                              child: _buildTransactionsList(),
                            ),

                            // // Right sidebar - Transaction details
                            // if (_selectedTransaction != null)
                            //   Expanded(
                            //     flex: 2,
                            //     child: Container(
                            //       decoration: BoxDecoration(
                            //         color:
                            //             Theme.of(context).colorScheme.surface,
                            //         boxShadow: [
                            //           BoxShadow(
                            //             color: Theme.of(context)
                            //                 .shadowColor
                            //                 .withAlpha(10),
                            //             blurRadius: 4,
                            //             offset: const Offset(-2, 0),
                            //           ),
                            //         ],
                            //       ),
                            //       child: _selectedGroupedTransaction != null
                            //           ? _buildGroupedTransactionDetailsPanel(
                            //               _selectedGroupedTransaction!)
                            //           : _buildTransactionDetailsPanel(
                            //               _selectedTransaction!),
                            //     ),
                            //   ),
                          ],
                        ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsList() {
    return Consumer<TransactionProvider>(
      builder: (context, provider, child) {
        if (provider.isLoadingTransactionDetails) {
          return NSLKnowledgeLoaderWrapper(
            isLoading: true,
            child: Center(
              child: Text(
                context.tr('transaction.loadingTransactions'),
                style: TextStyle(
                  fontSize: 16,
                  color: Theme.of(context).textTheme.bodySmall?.color,
                ),
              ),
            ),
          );
        }

        if (provider.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Theme.of(context).colorScheme.error.withAlpha(150),
                ),
                const SizedBox(height: 16),
                Text(
                  context.tr('transaction.errorLoadingTransactions'),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).textTheme.bodySmall?.color,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  '${provider.error}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: _fetchTransactionDetails,
                  icon: const Icon(Icons.refresh),
                  label: Text(context.tr('common.retry')),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Theme.of(context).colorScheme.onPrimary,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 12),
                  ),
                ),
              ],
            ),
          );
        }

        final allTransactions = provider.transactionDetails;

        // Group transactions by workflow ID
        final groupedTransactions =
            _groupTransactionsByWorkflowId(allTransactions);

        // Filter grouped transactions based on the selected tab
        final filteredGroups =
            _filterGroupedTransactions(groupedTransactions, _currentFilter);

        if (filteredGroups.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.inbox_outlined,
                  size: 64,
                  color: Theme.of(context).colorScheme.primary.withAlpha(100),
                ),
                const SizedBox(height: 16),
                Text(
                  _currentFilter == context.tr('transaction.all')
                      ? context.tr('transaction.noTransactionsFound')
                      : context.tr('transaction.noFilteredTransactionsFound',
                          args: {'status': _currentFilter.toLowerCase()}),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).textTheme.bodySmall?.color,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  context.tr('transaction.startNewTransactionPrompt'),
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).textTheme.bodySmall?.color,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                Center(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pushReplacementNamed(context, '/transact');
                    },
                    icon: const Icon(Icons.add),
                    label: Text(context.tr('transaction.newTransaction')),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                      textStyle: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        fontFamily:
                            Theme.of(context).textTheme.bodyMedium?.fontFamily,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: _fetchTransactionDetails,
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: filteredGroups.length,
            itemBuilder: (context, index) {
              final group = filteredGroups[index];
              return _buildGroupedTransactionCard(group);
            },
          ),
        );
      },
    );
  }

  Widget _buildGroupedTransactionCard(GroupedTransaction group) {
    final isSelected = _selectedGroupedTransaction?.workflowInstanceId ==
        group.workflowInstanceId;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: isSelected ? 3 : 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isSelected
            ? BorderSide(color: Theme.of(context).colorScheme.primary, width: 2)
            : BorderSide.none,
      ),
      child: InkWell(
        onTap: () => _selectGroupedTransaction(group),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Status indicator
                  Container(
                    width: 12,
                    height: 12,
                    margin: const EdgeInsets.only(top: 4, right: 8),
                    decoration: BoxDecoration(
                      color: _getStatusColor(group.status),
                      shape: BoxShape.circle,
                    ),
                  ),
                  // Transaction details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          group.displayName,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          context.tr('transaction.objectiveId', args: {
                            'id': group.workflowInstanceId.substring(0, 8)
                          }),
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          context.tr('transaction.workerId',
                              args: {'id': group.workflowInstanceId}),
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // LO count chip
                  Container(
                    margin: const EdgeInsets.only(right: 8),
                    child: Chip(
                      label: Text(
                        context.tr('transaction.loCount', args: {
                          'count': group.loCount.toString(),
                          'plural': group.loCount > 1 ? 's' : ''
                        }),
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.tertiary,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                      backgroundColor:
                          Theme.of(context).colorScheme.tertiary.withAlpha(25),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 0),
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                  ),
                  // Status chip
                  Chip(
                    label: Text(
                      group.status,
                      style: TextStyle(
                        color: _getStatusColor(group.status),
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                    backgroundColor:
                        _getStatusColor(group.status).withAlpha(25),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    size: 14,
                    color:
                        Theme.of(context).colorScheme.onSurface.withAlpha(153),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    context.tr('transaction.formattedDate',
                        args: {'date': _formatDate(group.createdAt)}),
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Icon(
                    Icons.update,
                    size: 14,
                    color:
                        Theme.of(context).colorScheme.onSurface.withAlpha(153),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    context.tr('transaction.updatedDate',
                        args: {'date': _formatDate(group.updatedAt)}),
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                  const Spacer(),
                  // Resume button for pending transactions
                  if (group.status.toLowerCase() == 'pending')
                    Center(
                      child: ElevatedButton.icon(
                        onPressed: () => _resumeGroupedTransaction(group),
                        icon: const Icon(
                          Icons.play_arrow,
                          size: 16,
                          color: Colors.white,
                        ),
                        label: Text(context.tr('transaction.resume')),
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              Theme.of(context).colorScheme.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          textStyle: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            fontFamily: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.fontFamily,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGroupedTransactionDetailsPanel(GroupedTransaction group) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  group.displayName,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () {
                  setState(() {
                    _selectedTransaction = null;
                    _selectedGroupedTransaction = null;
                  });
                },
                tooltip: 'Close details',
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              // LO count chip
              Container(
                margin: const EdgeInsets.only(right: 8),
                child: Chip(
                  label: Text(
                    '${group.loCount} Local Objective${group.loCount > 1 ? 's' : ''}',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.tertiary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  backgroundColor:
                      Theme.of(context).colorScheme.tertiary.withAlpha(25),
                ),
              ),
              // Status chip
              Chip(
                label: Text(
                  group.status,
                  style: TextStyle(
                    color: _getStatusColor(group.status),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                backgroundColor: _getStatusColor(group.status).withAlpha(25),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Transaction details
          const Text(
            'Transaction Details',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildGroupDetailsTable(group),

          const SizedBox(height: 24),

          // Local Objectives
          const Text(
            'Local Objectives',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildLocalObjectivesTable(group),

          const SizedBox(height: 24),

          // Input attributes from the latest transaction
          const Text(
            'Latest Input Data',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildInputAttributesSection(group.latestTransaction),

          const SizedBox(height: 32),

          // Action buttons
          if (group.status.toLowerCase() == 'pending')
            Center(
              child: ElevatedButton.icon(
                onPressed: () => _resumeGroupedTransaction(group),
                icon: const Icon(Icons.play_arrow),
                label: const Text('Resume Transaction'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  textStyle: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    fontFamily:
                        Theme.of(context).textTheme.bodyMedium?.fontFamily,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTransactionDetailsPanel(TransactionDetail transaction) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  transaction.displayName,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () {
                  setState(() {
                    _selectedTransaction = null;
                    _selectedGroupedTransaction = null;
                  });
                },
                tooltip: 'Close details',
              ),
            ],
          ),
          const SizedBox(height: 8),
          Chip(
            label: Text(
              transaction.status,
              style: TextStyle(
                color: _getStatusColor(transaction.status),
                fontWeight: FontWeight.bold,
              ),
            ),
            backgroundColor: _getStatusColor(transaction.status).withAlpha(25),
          ),
          const SizedBox(height: 24),

          // Transaction details
          const Text(
            'Transaction Details',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildDetailsTable(transaction),

          const SizedBox(height: 32),

          // Input attributes
          const Text(
            'Workflow Details',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildInputAttributesSection(transaction),

          const SizedBox(height: 32),

          // Action buttons
          if (transaction.status.toLowerCase() == 'pending')
            Center(
              child: ElevatedButton.icon(
                onPressed: () => _resumeTransaction(transaction),
                icon: const Icon(Icons.play_arrow),
                label: const Text('Resume Transaction'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  textStyle: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    fontFamily:
                        Theme.of(context).textTheme.bodyMedium?.fontFamily,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildGroupDetailsTable(GroupedTransaction group) {
    return Table(
      columnWidths: const {
        0: FlexColumnWidth(1),
        1: FlexColumnWidth(2),
      },
      border: TableBorder.all(
        color: Colors.grey.shade200,
        width: 1,
      ),
      children: [
        _buildTableRow('Workflow ID', group.workflowInstanceId),
        _buildTableRow('Global Objective', group.goName),
        _buildTableRow('Local Objectives',
            '${group.loCount} LO${group.loCount > 1 ? 's' : ''}'),
        _buildTableRow('Created', _formatDate(group.createdAt)),
        _buildTableRow('Last Updated', _formatDate(group.updatedAt)),
        _buildTableRow('Status', group.status),
      ],
    );
  }

  Widget _buildLocalObjectivesTable(GroupedTransaction group) {
    if (group.transactions.isEmpty) {
      return const Text('No local objectives available');
    }

    // Sort transactions by updatedAt (newest first)
    final sortedTransactions = List<TransactionDetail>.from(group.transactions);
    sortedTransactions.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

    return Table(
      columnWidths: const {
        0: FlexColumnWidth(2), // Name
        1: FlexColumnWidth(1), // Status
        2: FlexColumnWidth(2), // Updated At
      },
      border: TableBorder.all(
        color: Colors.grey.shade200,
        width: 1,
      ),
      children: [
        // Header row
        TableRow(
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
          ),
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                'Local Objective',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                'Status',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                'Updated At',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
        // Data rows
        ...sortedTransactions.map((transaction) => TableRow(
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(transaction.loName ?? 'N/A'),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: _getStatusColor(transaction.status).withAlpha(25),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      transaction.status,
                      style: TextStyle(
                        color: _getStatusColor(transaction.status),
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(_formatDate(transaction.updatedAt)),
                ),
              ],
            )),
      ],
    );
  }

  Widget _buildDetailsTable(TransactionDetail transaction) {
    return Table(
      columnWidths: const {
        0: FlexColumnWidth(1),
        1: FlexColumnWidth(2),
      },
      border: TableBorder.all(
        color: Colors.grey.shade200,
        width: 1,
      ),
      children: [
        _buildTableRow('Workflow ID', transaction.workflowInstanceId),
        _buildTableRow('Global Objective', transaction.goName),
        _buildTableRow('Local Objective', transaction.loName ?? 'N/A'),
        _buildTableRow('Created', _formatDate(transaction.createdAt)),
        _buildTableRow('Updated', _formatDate(transaction.updatedAt)),
        _buildTableRow('Status', transaction.status),
      ],
    );
  }

  TableRow _buildTableRow(String label, String value) {
    return TableRow(
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(value),
        ),
      ],
    );
  }

  Widget _buildInputAttributesSection(TransactionDetail transaction) {
    final inputAttributes = transaction.getInputAttributes();

    if (inputAttributes.isEmpty) {
      return const Text('No input attributes available');
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: inputAttributes.map((attr) {
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  attr.attributeDisplayName,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  attr.value.toString(),
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('MMM d, yyyy h:mm a').format(date);
    } catch (e) {
      return dateString;
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return Theme.of(context).colorScheme.primary;
      case 'pending':
        return Theme.of(context).colorScheme.secondary;

      default:
        return Theme.of(context).colorScheme.onSurface.withAlpha(153);
    }
  }
}

// Helper class for input attributes
class InputAttribute {
  final String name;
  final dynamic value;

  InputAttribute({required this.name, required this.value});

  factory InputAttribute.fromJson(Map<String, dynamic> json) {
    return InputAttribute(
      name: json['name'] ?? '',
      value: json['value'],
    );
  }
}

// Extension method for TransactionDetail to get input attributes
extension TransactionDetailExtension on TransactionDetail {
  List<InputAttribute> getInputAttributes() {
    final List<InputAttribute> attributes = [];

    // Add basic attributes
    attributes
        .add(InputAttribute(name: 'Workflow ID', value: workflowInstanceId));
    attributes.add(InputAttribute(name: 'Global Objective', value: goName));
    if (loName != null) {
      attributes.add(InputAttribute(name: 'Local Objective', value: loName));
    }

    return attributes;
  }
}
