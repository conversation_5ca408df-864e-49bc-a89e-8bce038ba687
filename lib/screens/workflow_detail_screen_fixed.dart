import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/global_objective.dart';
import '../models/transaction_detail.dart';
import '../providers/workflow_provider.dart';
import '../models/workflow.dart';

import '../widgets/input_field_widget.dart';
import '../utils/logger.dart';
import '../models/input_value_store.dart';
import '../widgets/chat_text_field.dart';
import '../models/message.dart';
import '../services/speech_service.dart';
import '../services/workflow_service.dart';

class WorkflowDetailScreen extends StatefulWidget {
  final GlobalObjective objective;
  final TransactionDetail? transaction; // Optional transaction for resuming
  final bool isResuming; // Flag to indicate if we're resuming a transaction
  final String? transactionId; // Optional transaction ID for resuming

  const WorkflowDetailScreen({
    super.key,
    required this.objective,
    this.transaction,
    this.isResuming = false,
    this.transactionId,
  });

  @override
  State<WorkflowDetailScreen> createState() => _WorkflowDetailScreenState();
}

class _WorkflowDetailScreenState extends State<WorkflowDetailScreen> {
  bool _isInitialized = false;
  final SpeechService _speechService = SpeechService();
  final WorkflowService _workflowService = WorkflowService();

  // Flag to toggle between form view and chat view
  bool _showChat = false; // false = form view, true = chat view

  // Flag to control whether to show all LOs or only the current LO
  // Set to true to show only the current LO, false to show all LOs
  bool _showOnlyCurrentLO = true;

  // Flag to control whether to show all messages or only current section messages in chat view
  // Set to true to show only current section messages, false to show all messages
  final bool _showOnlyCurrentSectionMessages = true;
  // List to store input sections
  final List<Widget> _inputSections = [];

  // Map to store input field references
  final Map<String, GlobalKey<InputFieldWidgetState>> _inputFieldKeys = {};

  // Get the global input value store
  final _inputStore = InputValueStore();

  // Map to track expanded state of each section
  final Map<String, bool> _expandedSections = {};

  // Map to track submitted state of each section
  final Map<String, bool> _submittedSections = {};

  // Map to track submitted chat messages
  final Map<String, bool> _submittedChatSections = {};

  // Loading dialog controller
  bool _isDialogShowing = false;

  // Text controller for chat field
  final TextEditingController _textController = TextEditingController();

  // Chat view state variables
  final List<Message> _chatMessages = [];
  final ScrollController _chatScrollController = ScrollController();
  bool _waitingForUserInput = false;
  InputField? _currentChatInputField;
  int _currentInputIndex = 0;
  bool _processingDependentFields = false;
  DateTime? _processingDependentFieldsStartTime;

  @override
  void initState() {
    super.initState();
    _initializeSpeechService();
  }

  Future<void> _initializeSpeechService() async {
    await _speechService.initialize();
    Logger.info('Speech service initialized in WorkflowDetailScreen');
  }

  Future<void> _speakMessage(String message) async {
    // Check microphone permission first (needed for some devices)
    bool hasPermission = await _speechService.checkMicrophonePermission();
    if (!hasPermission) {
      // Request permission
      hasPermission = await _speechService.requestMicrophonePermission();
      if (!hasPermission) {
        // Show error message if permission denied
        _showPermissionDeniedDialog();
        return;
      }
    }

    Logger.info(
        'Speaking message: ${message.substring(0, message.length > 50 ? 50 : message.length)}...');
    await _speechService.speak(message);
  }

  void _showPermissionDeniedDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Microphone Permission Required'),
          content: Text(
              'This app needs microphone access for text-to-speech functionality. '
              'Please grant microphone permission in your device settings.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('OK'),
            ),
          ],
        );
      },
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Schedule initialization for after the build is complete
    if (!_isInitialized) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _initializeWorkflow();
          _isInitialized = true;
        }
      });
    }
  }

  @override
  void dispose() {
    // Make sure to hide any showing dialog before disposing
    if (_isDialogShowing) {
      _hideLoadingDialog();
    }
    // Dispose of the text controller
    _textController.dispose();
    super.dispose();
  }

  // Helper method to get the index of the last section
  int _getLastSectionIndex() {
    final workflowProvider =
        Provider.of<WorkflowProvider>(context, listen: false);
    final allInputs = workflowProvider.allWorkflowInputs;
    return allInputs.isEmpty ? -1 : allInputs.length - 1;
  }

  // Helper method to log the state of all input fields
  void _logInputFieldsState() {
    Logger.info('Input fields state:');
    for (final entry in _inputFieldKeys.entries) {
      final fieldId = entry.key;
      final fieldState = entry.value.currentState;
      if (fieldState != null) {
        final value = fieldState.getValue();
        Logger.info('  $fieldId: $value');
      } else {
        Logger.info('  $fieldId: <no state>');
      }
    }
  }

  // Handle input field value changes
  void _handleInputValueChanged(
      String sectionId, String attFieldId, dynamic value, String inFieldId) {
    Logger.info('Value changed for $sectionId:$attFieldId: $value');

    // Store the value in both input stores
    final workflowProvider =
        Provider.of<WorkflowProvider>(context, listen: false);
    workflowProvider.inputValueStore.setValue(sectionId, inFieldId, value);
    _inputStore.setValue(
        sectionId, attFieldId, value); // Also store in local input store

    // Verify that the value was stored correctly
    final storedValue =
        workflowProvider.inputValueStore.getValue(sectionId, inFieldId);
    Logger.info(
        'Verified stored value for $sectionId:$inFieldId: $storedValue');

    // If the value wasn't stored correctly, try again with a different approach
    if (storedValue == null) {
      Logger.warning('Value was not stored correctly, trying again');
      // Try storing directly in the singleton instance
      workflowProvider.inputValueStore.setValue(sectionId, inFieldId, value);
      _inputStore.setValue(sectionId, attFieldId, value);

      // Verify again
      final verifiedValue =
          workflowProvider.inputValueStore.getValue(sectionId, inFieldId);
      Logger.info(
          'Re-verified stored value for $sectionId:$inFieldId: $verifiedValue');
    }

    // Check if this field is a parent for any dependent fields
    _checkAndUpdateDependentFields(sectionId, inFieldId, value);

    // Force a rebuild of the section to ensure visibility is updated
    // This is important for dependent fields that might need to be shown/hidden
    _rebuildSection(sectionId);
  }

  // Check and update dependent fields when a parent field changes
  Future<void> _checkAndUpdateDependentFields(
      String sectionId, String fieldId, dynamic value) async {
    final workflowProvider =
        Provider.of<WorkflowProvider>(context, listen: false);
    final allInputs = workflowProvider.allWorkflowInputs;

    // Get the section index from the sectionId
    int sectionIndex = -1;
    if (sectionId.startsWith('section_')) {
      sectionIndex = int.tryParse(sectionId.split('_')[1]) ?? -1;
    }

    if (sectionIndex < 0 || sectionIndex >= allInputs.length) {
      Logger.warning('Invalid section index: $sectionIndex');
      return;
    }

    final section = allInputs[sectionIndex];
    Logger.info(
        'Checking dependent fields for parent field $fieldId with value $value');
    Logger.info(
        'Section has ${section.dependentInputs.length} dependent fields');

    // Process all dependent fields that depend on this field
    List<InputField> affectedDependentFields = [];
    bool needsRebuild = false;

    // First, check if there are any dependent fields that depend on this field
    for (var inputField in section.dependentInputs) {
      if ((inputField.dependencies ?? []).contains(fieldId)) {
        affectedDependentFields.add(inputField);
        Logger.info(
            'Found dependent field: ${inputField.displayName} (${inputField.attributeId}) with dependency type: ${inputField.dependencyType}');
      }
    }

    // If there are no dependent fields, just rebuild the section once
    if (affectedDependentFields.isEmpty) {
      Logger.info('No dependent fields found for parent field $fieldId');
      // Only rebuild if there are no dependent fields to update
      Logger.info(
          'Rebuilding section $sectionId after parent field $fieldId changed to $value');
      _rebuildSection(sectionId);
      return;
    }

    Logger.info(
        'Found ${affectedDependentFields.length} dependent fields for parent field $fieldId');

    // Process each dependent field
    for (var inputField in affectedDependentFields) {
      // Handle different dependency types
      if (inputField.dependencyType == 'calculation') {
        // For calculation dependencies (like Number of Days)
        bool shouldRebuild =
            await _handleCalculationDependency(sectionId, inputField);
        if (shouldRebuild) {
          needsRebuild = true;
        }
      } else if (inputField.dependencyType == 'dropdown') {
        // For dropdown dependencies (like Leave Sub-Type)
        await _fetchDependentDropdownOptions(sectionId, inputField);
        // Don't set needsRebuild here as _fetchDependentDropdownOptions already rebuilds the section
      }
    }

    // Only rebuild if needed and not already done by _fetchDependentDropdownOptions
    if (needsRebuild) {
      Logger.info(
          'Rebuilding section $sectionId after processing dependent fields');
      _rebuildSection(sectionId);
    }
  }

  // Handle calculation dependencies (like Number of Days)
  Future<bool> _handleCalculationDependency(
      String sectionId, InputField dependentField) async {
    // Check if we have all parent values
    if (dependentField.dependencies == null ||
        dependentField.dependencies!.isEmpty) {
      Logger.warning(
          'No parent IDs for calculation dependency: ${dependentField.displayName}');
      // Reset the processing flags to ensure we don't get stuck
      _processingDependentFields = false;
      _processingDependentFieldsStartTime = null;
      Logger.info('Reset processing flags due to missing dependencies');
      return false;
    }

    if (checkAllParentValuesHaveData(dependentField.dependencies, sectionId)) {
      // Get parent values
      final parentValues =
          _getParentValues(sectionId, dependentField.dependencies!);
      Logger.info(
          'Parent values for ${dependentField.displayName}: $parentValues');
      final workflowProvider =
          Provider.of<WorkflowProvider>(context, listen: false);
      final String? instanceId = workflowProvider.workflowInstance?.instanceId;
      if (instanceId == null) {
        Logger.warning('No workflow instance ID available');
        // Reset the processing flags to ensure we don't get stuck
        _processingDependentFields = false;
        _processingDependentFieldsStartTime = null;
        Logger.info('Reset processing flags due to missing instance ID');
        return false;
      }

      _showLoadingDialog();

      try {
        // Build query parameters from parent values
        final queryParams = <String, dynamic>{};
        for (final entry in parentValues.entries) {
          queryParams[entry.key] = entry.value;
        }
        queryParams[dependentField.inputId] = dependentField.displayName;

        Logger.info(
            'Fetching calculation dependency with params: $queryParams');

        // Make API call to fetch dependent dropdown options
        final result = await _workflowService.fetchWorkflowInputs(
          instanceId: instanceId,
          queryParams: queryParams,
        );

        // Process the result
        if (result != null &&
            result is Map &&
            result.containsKey('dependent_inputs')) {
          final updatedData = result['dependent_inputs'] as List;
          Logger.info(
              'Received ${updatedData.length} dependent inputs from API');

          // Track if we found and updated any fields
          bool updatedAnyField = false;

          // Update fields with the new data
          for (final input in updatedData) {
            if (input is Map) {
              final fieldId = input['attribute_id'];
              final value = input['input_value'];

              // Update the workflow provider with the new dependent inputs
              workflowProvider
                  .updateDependentInput(Map<String, dynamic>.from(input));

              // Always store the value in both input stores, even if the field isn't visible yet
              _inputStore.setValue(sectionId, fieldId, value);
              workflowProvider.inputValueStore
                  .setValue(sectionId, fieldId, value);
              Logger.info(
                  'Stored value for $fieldId in both input stores: $value');

              // Update the UI if the field exists
              if (_inputFieldKeys.containsKey('$sectionId:$fieldId')) {
                _updateDependentFieldValue(sectionId, fieldId, value);
                Logger.info('Updated field UI for $fieldId with value $value');
              } else {
                Logger.info(
                    'Field $fieldId not found in UI, but value stored in input stores');
              }

              updatedAnyField = true;
            }
          }

          // Only rebuild if we actually updated any fields
          if (updatedAnyField) {
            Logger.info(
                'Rebuilding section $sectionId after updating calculation fields');
            // Reset the processing flags to ensure we don't get stuck
            _processingDependentFields = false;
            _processingDependentFieldsStartTime = null;
            Logger.info(
                'Reset processing flags after updating calculation fields');
            return true; // Signal that we need to rebuild
          }
        } else {
          Logger.warning(
              'No dependent_inputs found in API response for calculation');
        }
      } finally {
        // Hide loading indicator
        _hideLoadingDialog();

        // Reset the processing flags to ensure we don't get stuck
        _processingDependentFields = false;
        _processingDependentFieldsStartTime = null;
        Logger.info('Reset processing flags in finally block');
      }
    } else {
      // Reset the processing flags to ensure we don't get stuck
      _processingDependentFields = false;
      _processingDependentFieldsStartTime = null;
      Logger.info('Reset processing flags in else block');
    }

    return false; // No rebuild needed
  }

  // Fetch dependent dropdown options from API
  Future<void> _fetchDependentDropdownOptions(
      String sectionId, InputField dependentField) async {
    try {
      // Check if we have all parent values
      if (dependentField.dependencies == null ||
          dependentField.dependencies!.isEmpty) {
        Logger.warning(
            'No parent IDs for dropdown dependency: ${dependentField.displayName}');
        // Reset the processing flags to ensure we don't get stuck
        _processingDependentFields = false;
        _processingDependentFieldsStartTime = null;
        Logger.info('Reset processing flags due to missing parent IDs');
        return;
      }

      // Get parent values
      final parentValues =
          _getParentValues(sectionId, dependentField.dependencies!);
      Logger.info(
          'Parent values for ${dependentField.displayName}: $parentValues');

      // Check if we have the workflow instance ID
      final workflowProvider =
          Provider.of<WorkflowProvider>(context, listen: false);
      final instanceId = workflowProvider.workflowInstance?.instanceId;

      if (instanceId == null) {
        Logger.warning('No workflow instance ID available');
        return;
      }

      // Show loading indicator
      _showLoadingDialog();

      try {
        // Build query parameters from parent values
        final queryParams = <String, dynamic>{};
        for (final entry in parentValues.entries) {
          queryParams[entry.key] = entry.value;
        }

        // NOTE: We're not adding dependent_input_id as it causes a 500 error
        // The API doesn't expect this parameter in the current implementation
        // queryParams['dependent_input_id'] = dependentField.attributeId;

        // Log the query parameters for debugging
        Logger.info('Query parameters for dependent dropdown options:');
        queryParams.forEach((key, value) {
          Logger.info('  $key: $value');
        });

        Logger.info(
            'Fetching dependent dropdown options with params: $queryParams');

        // Make API call to fetch dependent dropdown options
        final result = await _workflowService.fetchWorkflowInputs(
          instanceId: instanceId,
          queryParams: queryParams,
        );

        // Process the result
        if (result != null &&
            result is Map &&
            result.containsKey('dependent_inputs')) {
          final dependentInputs = result['dependent_inputs'] as List;
          Logger.info(
              'Received ${dependentInputs.length} dependent inputs from API');

          // Track if we found and updated the specific dependent field we're looking for
          bool foundTargetField = false;

          // Find the updated dependent field
          for (final input in dependentInputs) {
            if (input is Map) {
              Logger.info(
                  'Processing dependent input: ${input['attribute_id']}');

              // Update the workflow provider with the new dependent inputs
              // This ensures that the dependent inputs are properly stored in the provider
              workflowProvider
                  .updateDependentInput(Map<String, dynamic>.from(input));

              // If this is the specific dependent field we're looking for
              if (input['attribute_id'] == dependentField.attributeId) {
                foundTargetField = true;

                // Extract dropdown options
                List<DropdownOption> dropdownOptions = [];
                if (input.containsKey('dropdown_options') &&
                    input['dropdown_options'] is List) {
                  dropdownOptions = (input['dropdown_options'] as List)
                      .map((x) =>
                          DropdownOption.fromJson(x as Map<String, dynamic>))
                      .toList();
                }

                // Store any input_value that came with the dependent field
                if (input.containsKey('input_value')) {
                  final value = input['input_value'];
                  if (value != null) {
                    // Store the value in both input stores
                    _inputStore.setValue(
                        sectionId, dependentField.attributeId, value);
                    workflowProvider.inputValueStore
                        .setValue(sectionId, dependentField.attributeId, value);
                    Logger.info(
                        'Stored value for ${dependentField.attributeId} in both input stores: $value');
                  }
                }

                // Update the dependent field with new dropdown options
                _updateDependentFieldDropdownOptions(
                    sectionId, dependentField.attributeId, dropdownOptions);
                Logger.info(
                    'Updated dropdown options for ${dependentField.displayName}: ${dropdownOptions.length} options');
              }
            }
          }

          // Only rebuild the section if we found and updated the target field
          if (foundTargetField) {
            Logger.info(
                'Rebuilding section $sectionId after updating dependent field ${dependentField.attributeId}');
            _rebuildSection(sectionId);
          } else {
            Logger.warning(
                'Target dependent field ${dependentField.attributeId} not found in API response');
          }
        } else {
          Logger.warning('No dependent_inputs found in API response');
        }
      } finally {
        // Hide loading indicator
        _hideLoadingDialog();

        // Reset the processing flags to ensure we don't get stuck
        _processingDependentFields = false;
        _processingDependentFieldsStartTime = null;
        Logger.info(
            'Reset processing flags in finally block of fetchDependentDropdownOptions');
      }
    } catch (e) {
      Logger.error('Error fetching dependent dropdown options: $e');
      // Hide loading indicator if still showing
      if (_isDialogShowing) {
        _hideLoadingDialog();
      }

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(
                  'Error fetching dependent options. Using empty options list.')),
        );
      }

      // Even if there's an error, update the field with empty options
      // This ensures the field is properly hidden if it should be
      _updateDependentFieldDropdownOptions(
          sectionId, dependentField.attributeId, []);
      Logger.info(
          'Set empty dropdown options for ${dependentField.attributeId} due to API error');

      // Force a rebuild of the section to ensure visibility is updated
      _rebuildSection(sectionId);

      // Reset the processing flags to ensure we don't get stuck
      _processingDependentFields = false;
      _processingDependentFieldsStartTime = null;
      Logger.info(
          'Reset processing flags in catch block of fetchDependentDropdownOptions');
    }
  }

  // Update dependent field value
  void _updateDependentFieldValue(
      String sectionId, String fieldId, dynamic value) {
    // Get the workflow provider to access the input store
    final workflowProvider =
        Provider.of<WorkflowProvider>(context, listen: false);

    // Store the value in both input stores
    _inputStore.setValue(sectionId, fieldId, value);
    workflowProvider.inputValueStore.setValue(sectionId, fieldId, value);
    Logger.info('Stored value for $fieldId in both input stores: $value');

    // Update the UI
    final fieldKey = '$sectionId:$fieldId';
    if (_inputFieldKeys.containsKey(fieldKey)) {
      final fieldState = _inputFieldKeys[fieldKey]?.currentState;
      if (fieldState != null) {
        fieldState.setValue(value);
        Logger.info('Updated UI for dependent field $fieldKey: $value');
      } else {
        Logger.info(
            'Field state not found for $fieldKey, but value stored in input stores');
      }
    } else {
      Logger.info(
          'Field key not found for $fieldKey, but value stored in input stores');
    }
  }

  // Update dependent field dropdown options
  void _updateDependentFieldDropdownOptions(
      String sectionId, String fieldId, List<DropdownOption> options) {
    // Log the options for debugging
    Logger.info(
        'Updating dropdown options for $sectionId:$fieldId with ${options.length} options');
    if (options.isEmpty) {
      Logger.info('Dropdown options are empty for $fieldId');
    } else {
      for (int i = 0; i < options.length; i++) {
        Logger.info('  Option $i: ${options[i].value} - ${options[i].label}');
      }
    }

    // Update the UI
    final fieldKey = '$sectionId:$fieldId';
    if (_inputFieldKeys.containsKey(fieldKey)) {
      final fieldState = _inputFieldKeys[fieldKey]?.currentState;
      if (fieldState != null) {
        // Update the dropdown options in the field
        setState(() {
          fieldState.updateDropdownOptions(options);
        });
        Logger.info('Updated dropdown options for $fieldKey');

        // Force a rebuild of the section to ensure visibility is updated
        _rebuildSection(sectionId);
      } else {
        Logger.warning('Field state not found for $fieldKey');
      }
    } else {
      Logger.warning('Field key not found for $fieldKey');
    }
  }

  // Helper method to check if a section with the given ID already exists
  bool _sectionExists(String sectionId) {
    return _inputSections.any((widget) {
      final keyString = (widget as KeyedSubtree).key.toString();
      if (sectionId.startsWith('section_direct_')) {
        // For direct sections, check if the exact ID exists
        return keyString.contains(sectionId);
      } else {
        // For regular sections, check if the base ID exists (e.g., 'section_0')
        final match = RegExp(r'keyed_(section_\d+)').firstMatch(keyString);
        final baseSectionId = match?.group(1) ?? '';
        return baseSectionId == sectionId;
      }
    });
  }

  // Helper method to build a field widget
  Widget _buildFieldWidget(
      InputField field, String sectionId, bool isSubmitted) {
    // Check if field should be displayed as read-only based on field.readOnly property
    // or if it's a calculation dependent field
    final isReadOnlyField = field.readOnly ||
        field.dependencyType == 'calculation' ||
        field.metadata.isInformational;

    if (isReadOnlyField) {
      // Display read-only fields as labels
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            field.displayName,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 14,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.grey[800]
                  : Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              // Display the input_value if available, otherwise leave empty
              field.inputValue != null ? field.inputValue.toString() : '',
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),
        ],
      );
    } else {
      // Display user input fields with interactive widgets
      final fieldId = '$sectionId:${field.attributeId}';

      // Reuse existing key if it exists, otherwise create a new one
      GlobalKey<InputFieldWidgetState> key;
      if (_inputFieldKeys.containsKey(fieldId)) {
        key = _inputFieldKeys[fieldId]!;
        Logger.info('Reusing existing key for field $fieldId');
      } else {
        key = GlobalKey<InputFieldWidgetState>();
        _inputFieldKeys[fieldId] = key;
        Logger.info('Creating new key for field $fieldId');
      }

      // Check if this is a dependent field
      final isDependentField =
          field.dependencyType != null && field.dependencyType!.isNotEmpty;

      if (isDependentField) {
        // For dependent fields, wrap in Visibility widget
        final parentIds = field.parentIds ?? [];
        final isDropdownOrCombobox = field.uiControl == 'oj-select-single' ||
            field.uiControl == 'oj-combobox-one';

        // Determine visibility based on parent values
        bool shouldShow = true;

        // If there are no parent IDs, always show the field
        if (parentIds.isNotEmpty) {
          // Check if all parent fields have values
          bool allParentsHaveValues =
              checkAllParentValuesHaveData(parentIds, sectionId);

          // First check if parent fields have values - this is a prerequisite
          if (!allParentsHaveValues) {
            shouldShow = false;
            Logger.info(
                'Field ${field.attributeId} hidden because parent fields don\'t have values');
          } else {
            // Check if we have a value in either input store
            final hasValueInLocalStore =
                _inputStore.hasValue(sectionId, field.attributeId);
            final hasValueInProviderStore =
                Provider.of<WorkflowProvider>(context, listen: false)
                    .inputValueStore
                    .hasValue(sectionId, field.attributeId);

            // If we have a value in either store, show the field
            if (hasValueInLocalStore || hasValueInProviderStore) {
              shouldShow = true;
              Logger.info(
                  'Field ${field.attributeId} has value in input store, showing it');
            }
            // Otherwise, apply the normal filtering logic
            else if (field.inputValue == null) {
              if (isDropdownOrCombobox) {
                // For dropdown/combobox, hide if dropdown options are empty
                shouldShow = field.dropdownOptions != null &&
                    field.dropdownOptions!.isNotEmpty;
                Logger.info(
                    'Field ${field.attributeId} is dropdown with ${field.dropdownOptions?.length ?? 0} options, shouldShow=$shouldShow');
              } else {
                // For non-dropdown/combobox, hide if inputValue is null
                shouldShow = false;
                Logger.info(
                    'Field ${field.attributeId} is not dropdown and has null inputValue, hiding it');
              }
            }
          }
        }

        Logger.info(
            'Dependent field ${field.displayName} (${field.attributeId}): '
            'UI=${field.uiControl}, '
            'isDropdown=$isDropdownOrCombobox, '
            'parentIds=${parentIds.join(", ")}, '
            'shouldShow=$shouldShow');

        return Visibility(
          visible: shouldShow,
          maintainState: true,
          maintainAnimation: true,
          maintainSize: false,
          child: InputFieldWidget(
            key: key,
            field: field,
            sectionId: sectionId,
            readOnly: isSubmitted,
            onValueChanged: _handleInputValueChanged,
          ),
        );
      } else {
        // For regular fields, no Visibility wrapper needed
        return InputFieldWidget(
          key: key,
          field: field,
          sectionId: sectionId,
          readOnly: isSubmitted,
          onValueChanged: _handleInputValueChanged,
        );
      }
    }
  }

  // Helper method to get parent values for a dependent field
  Map<String, dynamic> _getParentValues(
      String sectionId, List<String> parentIds) {
    final Map<String, dynamic> parentValues = {};

    WorkflowProvider workflowProvider =
        Provider.of<WorkflowProvider>(context, listen: false);

    final InputValueStore providerInputStore = workflowProvider.inputValueStore;

    for (final parentId in parentIds) {
      final key = '$sectionId:$parentId';

      // Check both local and provider input stores
      if (_inputStore.hasValue(sectionId, parentId)) {
        parentValues[parentId] = _inputStore.getValue(sectionId, parentId);
        Logger.info(
            'Retrieved parent value from local store for $key: ${_inputStore.getValue(sectionId, parentId)}');
      } else if (providerInputStore.hasValue(sectionId, parentId)) {
        parentValues[parentId] =
            providerInputStore.getValue(sectionId, parentId);
        Logger.info(
            'Retrieved parent value from provider store for $key: ${providerInputStore.getValue(sectionId, parentId)}');
      }
    }

    return parentValues;
  }

  // Helper method to force a rebuild of a specific section
  void _rebuildSection(String sectionId, {bool isInputsRefresh = false}) {
    // Log the current state of input fields for this section
    Logger.info('Rebuilding section $sectionId');
    Logger.info('Current input field values for section $sectionId:');
    final sectionKeys = _inputFieldKeys.entries
        .where((entry) => entry.key.startsWith('$sectionId:'))
        .toList();

    for (final entry in sectionKeys) {
      final fieldKey = entry.key;
      final fieldState = entry.value.currentState;
      if (fieldState != null) {
        final value = fieldState.getValue();
        Logger.info('  $fieldKey: $value');
      } else {
        Logger.info('  $fieldKey: <no state>');
      }
    }

    // Find the index of the section in the _inputSections list
    final index = _inputSections.indexWhere((widget) {
      final keyString = (widget as KeyedSubtree).key.toString();
      return keyString.contains('keyed_$sectionId') &&
          !keyString.contains('keyed_${sectionId}_direct');
    });

    if (index != -1) {
      // Get the inputs for this section
      final workflowProvider =
          Provider.of<WorkflowProvider>(context, listen: false);
      WorkflowInputs? inputs;

      // Try to find the inputs in allWorkflowInputs
      if (sectionId.startsWith('section_') && sectionId.contains('_')) {
        final sectionIndex = int.tryParse(sectionId.split('_')[1]);
        if (sectionIndex != null &&
            sectionIndex < workflowProvider.allWorkflowInputs.length) {
          inputs = workflowProvider.allWorkflowInputs[sectionIndex];
        }
      }

      // If not found, use the current inputs
      if (isInputsRefresh) {
        inputs = workflowProvider.workflowInputs;
      } else {
        inputs ??= workflowProvider.workflowInputs;
      }

      if (inputs != null) {
        // Important: DO NOT clear the input field keys for this section
        // This ensures that the input values are preserved when rebuilding the section

        // Rebuild the section with the updated submission state
        final sectionWidget = _buildInputSection(inputs, sectionId);
        setState(() {
          _inputSections[index] = KeyedSubtree(
              key: ValueKey(
                  'keyed_${sectionId}_${DateTime.now().millisecondsSinceEpoch}_submitted_${_submittedSections[sectionId] ?? false}'),
              child: sectionWidget);
        });

        // Restore the values of input fields after rebuilding
        Logger.info(
            'Restoring input field values after rebuilding section $sectionId:');
        for (final entry in sectionKeys) {
          final fieldKey = entry.key;
          final oldFieldState = entry.value.currentState;
          final newFieldState = _inputFieldKeys[fieldKey]?.currentState;

          if (oldFieldState != null && newFieldState != null) {
            final value = oldFieldState.getValue();
            if (value != null) {
              // Set the value in the new field state
              newFieldState.setValue(value);
              Logger.info('  Restored $fieldKey: $value');
            }
          } else {
            Logger.info(
                '  Could not restore $fieldKey: oldState=${oldFieldState != null}, newState=${newFieldState != null}');
          }
        }
      }
    }
  }

  // Handle chat message submission
  void _handleChatSubmit(String message) {
    if (message.trim().isEmpty) return;

    // Here you can implement the logic to handle the chat message
    // For example, you could send it to an API or process it locally
    Logger.info('Chat message submitted: $message');

    // Show a snackbar to acknowledge the message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Message sent: $message')),
    );

    // Clear the text field
    _textController.clear();
  }

  // Handle chat request cancellation
  void _handleChatCancel() {
    // Implement cancellation logic if needed
    Logger.info('Chat request cancelled');
  }

  // Show loading dialog
  void _showLoadingDialog() {
    if (_isDialogShowing) return;

    _isDialogShowing = true;
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );
  }

  // Hide loading dialog
  void _hideLoadingDialog() {
    if (!_isDialogShowing) return;

    _isDialogShowing = false;
    if (mounted && Navigator.canPop(context)) {
      Navigator.of(context).pop();
    }
  }

  // Show debug information
  // Method to update input sections safely outside the build phase
  void _updateInputSections() {
    if (!mounted) return;

    // If we're resuming, clear all existing input sections and keys
    if (widget.isResuming) {
      Logger.info(
          'Resuming workflow - clearing all existing input sections and keys');
      _inputSections.clear();
      _inputFieldKeys.clear();
      _expandedSections.clear();
      _submittedSections.clear();
    }

    // Check if we need to create new input sections
    final workflowProvider =
        Provider.of<WorkflowProvider>(context, listen: false);
    final allInputs = workflowProvider.allWorkflowInputs;
    final currentInputs = workflowProvider.workflowInputs;

    Logger.info(
        '_updateInputSections called: hasNewInputs=${workflowProvider.hasNewInputs}, sections=${_inputSections.length}, inputs=${allInputs.length}');

    // IMPORTANT: Don't clear existing sections - this is what's causing values to be lost
    // Instead, only add new sections that don't already exist

    // Check if we have any inputs at all
    if (allInputs.isEmpty && currentInputs != null && _inputSections.isEmpty) {
      // If allInputs is empty but we have currentInputs, use that
      Logger.info(
          'No inputs in allWorkflowInputs but we have currentInputs with ${currentInputs.inputFields.length} fields');

      final sectionId = 'section_current';
      final sectionWidget = _buildInputSection(currentInputs, sectionId);

      setState(() {
        _inputSections.add(KeyedSubtree(
            key: ValueKey(
                'keyed_${sectionId}_${DateTime.now().millisecondsSinceEpoch}_submitted_${_submittedSections[sectionId] ?? false}'),
            child: sectionWidget));
      });
      return;
    }

    // Create sections for all inputs
    if (allInputs.isNotEmpty) {
      List<Widget> newSections = [];

      // First, get a list of existing section base IDs (without the timestamp and submission state)
      final existingSectionBaseIds = _inputSections.map((widget) {
        final keyString = (widget as KeyedSubtree).key.toString();
        // Extract the base section ID (e.g., 'section_0', 'section_1')
        final match = RegExp(r'keyed_(section_\d+)').firstMatch(keyString);
        return match?.group(1) ?? '';
      }).toList();

      Logger.info('Existing section base IDs: $existingSectionBaseIds');

      for (int i = 0; i < allInputs.length; i++) {
        final inputs = allInputs[i];
        final sectionId = 'section_$i';

        // Check if this section already exists using our helper method
        if (!_sectionExists(sectionId)) {
          Logger.info(
              'Creating new section $sectionId with ${inputs.inputFields.length} fields');

          // Create the section widget
          final sectionWidget = _buildInputSection(inputs, sectionId);
          newSections.add(KeyedSubtree(
              key: ValueKey(
                  'keyed_${sectionId}_${DateTime.now().millisecondsSinceEpoch}_submitted_${_submittedSections[sectionId] ?? false}'),
              child: sectionWidget));
        } else {
          Logger.info('Section $sectionId already exists, skipping');
        }
      }

      // Add all new sections
      if (newSections.isNotEmpty) {
        Logger.info('Adding ${newSections.length} new sections to UI');
        setState(() {
          _inputSections.addAll(newSections);

          // Make sure the last section is expanded and others are collapsed
          // If there's only one section, always keep it expanded
          final isSingleSection = allInputs.length <= 1;

          for (int i = 0; i < allInputs.length; i++) {
            final sectionId = 'section_$i';
            if (isSingleSection) {
              _expandedSections[sectionId] = true;
              Logger.info('Keeping single section $sectionId expanded');
            } else {
              _expandedSections[sectionId] = (i == allInputs.length - 1);
              Logger.info(
                  'Setting section $sectionId expanded state to ${_expandedSections[sectionId]}');
            }
          }
        });
      }
    } else {
      Logger.warning('No inputs available to create sections');
    }

    // Reset the new inputs flag
    if (workflowProvider.hasNewInputs) {
      workflowProvider.resetNewInputsFlag();
    }
  }

  Future<void> _initializeWorkflow() async {
    try {
      // Always clear the input store when initializing a workflow (new or resumed)
      _inputStore.clear();
      Logger.info(
          'Local input store cleared for ${widget.isResuming ? "resumed" : "new"} workflow');

      // Get the provider and completely clear its data
      final workflowProvider =
          Provider.of<WorkflowProvider>(context, listen: false);

      // Call clearWorkflowData to reset all state in the provider
      workflowProvider.clearWorkflowData();
      Logger.info(
          'WorkflowProvider data cleared for ${widget.isResuming ? "resumed" : "new"} workflow');

      // Set the section visibility based on whether we're resuming or creating a new transaction
      setState(() {
        // When resuming a transaction, show all sections
        // When creating a new transaction, show only the current section
        _showOnlyCurrentLO = !widget.isResuming;
        Logger.info(
            'Setting _showOnlyCurrentLO to $_showOnlyCurrentLO based on isResuming=$widget.isResuming');
      });

      // Show loading indicator
      _showLoadingDialog();
      workflowProvider.setSelectedObjective(widget.objective);

      if (widget.isResuming && widget.transaction != null) {
        // Resuming an existing transaction
        Logger.info(
            'Resuming transaction: ${widget.transaction!.workflowInstanceId}');

        // Create a mock workflow instance from the transaction
        final workflowInstance = WorkflowInstance(
          instanceId: widget.transaction!.workflowInstanceId,
          goId: widget.transaction!.goId,
          tenantId: widget.transaction!.tenantId,
          status: widget.transaction!.status,
          startedBy: widget.transaction!.userId,
          startedAt: widget.transaction!.createdAt,
          instanceData: {}, // Empty map as default
          isTest: false,
          version: '1.0',
        );

        // Set the workflow instance in the provider
        workflowProvider.setWorkflowInstance(workflowInstance);

        // Fetch inputs directly
        await workflowProvider.fetchWorkflowInputsDirectly(
            widget.transaction!.workflowInstanceId);
      } else {
        // Starting a new workflow
        Logger.info(
            'Starting new workflow for objective: ${widget.objective.objectiveId}');
        await workflowProvider.processWorkflow(widget.objective);
      }

      // Hide loading indicator
      if (mounted) {
        _hideLoadingDialog();
      }

      // Update input sections
      _updateInputSections();
    } catch (e) {
      // Hide loading indicator
      if (mounted) {
        _hideLoadingDialog();

        // Clear the input store when an error occurs
        _inputStore.clear();

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error initializing workflow: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    Logger.info(
        'Building WorkflowDetailScreen with ${_inputSections.length} input sections');
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, result) {
        // Clear all data when navigating back
        if (didPop) {
          _inputStore.clear();
          // Also clear the provider's data
          Provider.of<WorkflowProvider>(context, listen: false)
              .clearWorkflowData();
          Logger.info('All workflow data cleared when navigating back');
        }
      },
      child: SafeArea(
        child: Scaffold(
          backgroundColor: Colors.white,
          appBar: AppBar(
            title: Text(widget.isResuming
                ? 'Resume: ${widget.objective.name}'
                : widget.objective.name),
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Theme.of(context).colorScheme.onPrimary,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () {
                // Clear all data when navigating back
                _inputStore.clear();
                // Also clear the provider's data
                Provider.of<WorkflowProvider>(context, listen: false)
                    .clearWorkflowData();
                Logger.info(
                    'All workflow data cleared when navigating back via back button');
                Navigator.of(context).pop();
              },
            ),
            actions: [
              // View toggle popup menu button
              PopupMenuButton<String>(
                icon: Icon(Icons.more_vert),
                tooltip: 'View options',
                onSelected: (value) {
                  if (value == 'form') {
                    setState(() {
                      _showChat = false;
                      // Clear submitted chat sections when switching back to form view
                      _submittedChatSections.clear();
                    });
                  } else if (value == 'chat') {
                    setState(() {
                      _showChat = true;

                      // Initialize chat state when switching to chat view
                      // Clear existing messages to start fresh
                      _chatMessages.clear();

                      // Add system messages for each input section
                      final workflowProvider =
                          Provider.of<WorkflowProvider>(context, listen: false);
                      final allInputs = workflowProvider.allWorkflowInputs;

                      for (int i = 0; i < allInputs.length; i++) {
                        // Add a section header
                        // _chatMessages.add(Message(
                        //   content:
                        //       'Section ${i + 1}: ${section.localObjective}',
                        //   role: MessageRole.assistant,
                        // ));

                        // // Add a separator
                        // _chatMessages.add(Message(
                        //   content: 'System Information',
                        //   role: MessageRole.assistant,
                        // ));
                      }

                      // Reset navigation state
                      _currentSectionId = '';
                      _currentInputIndex = 0;
                      _waitingForUserInput = false;
                      _currentChatInputField = null;
                    });

                    // Initialize chat view after state update
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (mounted) {
                        // Start processing inputs
                        _processNextInputField();
                      }
                    });
                  } else if (value == 'toggle_section_view') {
                    // setState(() {
                    //   if (_showChat) {
                    //     _showOnlyCurrentSectionMessages =
                    //         !_showOnlyCurrentSectionMessages;
                    //   } else {
                    //     _showOnlyCurrentLO = !_showOnlyCurrentLO;
                    //     // Force rebuild all sections when the toggle changes
                    //     _rebuildAllSections();
                    //   }
                    // });
                  }
                },
                itemBuilder: (context) => [
                  PopupMenuItem<String>(
                    value: 'form',
                    enabled: _showChat, // Only enable if currently in chat view
                    child: Row(
                      children: [
                        Icon(
                          Icons.text_snippet,
                          color: _showChat
                              ? Theme.of(context).colorScheme.primary
                              : Colors.grey,
                        ),
                        SizedBox(width: 8),
                        Text('Form View'),
                      ],
                    ),
                  ),
                  PopupMenuItem<String>(
                    value: 'chat',
                    enabled:
                        !_showChat, // Only enable if currently in form view
                    child: Row(
                      children: [
                        Icon(
                          Icons.forum_outlined,
                          color: !_showChat
                              ? Theme.of(context).colorScheme.primary
                              : Colors.grey,
                        ),
                        SizedBox(width: 8),
                        Text('Chat View'),
                      ],
                    ),
                  ),
                  // PopupMenuItem<String>(
                  //   value: 'toggle_section_view',
                  //   child: Row(
                  //     children: [
                  //       Icon(
                  //         _showChat
                  //             ? (_showOnlyCurrentSectionMessages
                  //                 ? Icons.filter_1
                  //                 : Icons.filter_none)
                  //             : (_showOnlyCurrentLO
                  //                 ? Icons.filter_1
                  //                 : Icons.filter_none),
                  //         color: Theme.of(context).colorScheme.primary,
                  //       ),
                  //       SizedBox(width: 8),
                  //       Text(_showChat
                  //           ? (_showOnlyCurrentSectionMessages
                  //               ? 'Show All Messages'
                  //               : 'Show Current Section Only')
                  //           : (_showOnlyCurrentLO
                  //               ? 'Show All Sections'
                  //               : 'Show Current Section Only')),
                  //     ],
                  //   ),
                  // ),
                ],
              ),
            ],
          ),
          // Show chat view or form view directly based on _showChat flag
          body: _showChat ? chatBody(context) : formBody(context),
        ),
      ),
    );
  }

  // Helper method to determine if a message belongs to the current section
  bool _isMessageFromCurrentSection(Message message, int currentSectionIndex) {
    // If currentSectionIndex is invalid, show all messages
    if (currentSectionIndex < 0) {
      return true;
    }

    // Get the workflow provider
    final workflowProvider =
        Provider.of<WorkflowProvider>(context, listen: false);

    // Check if we're viewing the latest section
    final isLatestSection =
        currentSectionIndex == workflowProvider.allWorkflowInputs.length - 1;

    // Initial welcome messages are only shown when viewing the first section
    if (message.content.startsWith('Welcome to the chat') ||
        message.content.startsWith('We\'re starting a new workflow') ||
        message.content.startsWith('You are resuming a transaction')) {
      return currentSectionIndex == 0;
    }

    // System information header should be shown for each section
    if (message.content == 'System Information:') {
      // Find the index of this message
      final messageIndex = _chatMessages.indexOf(message);

      // Look for section-specific messages after this header
      if (messageIndex >= 0 && messageIndex < _chatMessages.length - 1) {
        // Check if this is the system information header for the current section
        // by looking at the messages that follow it
        bool foundSectionSpecificMessage = false;

        // Look at the next few messages to determine which section this header belongs to
        for (int i = messageIndex + 1;
            i < _chatMessages.length && i < messageIndex + 10;
            i++) {
          final nextMessage = _chatMessages[i];

          // If we hit another system information header or a non-system message, stop looking
          if (nextMessage.content == 'System Information:' ||
              (!nextMessage.content.contains(': ') &&
                  !nextMessage.content.startsWith('_system_field_'))) {
            break;
          }

          // Check if this message contains a field from the current section
          if (nextMessage.content.contains(': ')) {
            // Check if this is a system field value for the current section
            if (currentSectionIndex <
                workflowProvider.allWorkflowInputs.length) {
              final currentSection =
                  workflowProvider.allWorkflowInputs[currentSectionIndex];

              // Check both systemInputs and inputFieldsSystem for backward compatibility
              // Use a map to avoid duplicates based on attributeId
              final Map<String, InputField> systemFieldsMap = {};

              // Add fields from systemInputs
              for (final field in currentSection.systemInputs) {
                systemFieldsMap[field.attributeId] = field;
              }

              // Add fields from inputFieldsSystem if they don't already exist
              for (final field in currentSection.inputFieldsSystem) {
                if (!systemFieldsMap.containsKey(field.attributeId)) {
                  systemFieldsMap[field.attributeId] = field;
                }
              }

              // Convert map to list
              final allSystemFields = systemFieldsMap.values.toList();

              for (final field in allSystemFields) {
                if (nextMessage.content.startsWith('${field.displayName}: ')) {
                  foundSectionSpecificMessage = true;
                  break;
                }
              }
            }
          }

          if (foundSectionSpecificMessage) break;
        }

        return foundSectionSpecificMessage;
      }

      // If we couldn't determine which section this header belongs to, default to showing it
      // only in the first section
      return currentSectionIndex == 0;
    }

    // System field values should only be shown for the current section
    if (message.content.contains(': ')) {
      // Check if this is a system field value message by checking if it matches any system field
      if (currentSectionIndex < workflowProvider.allWorkflowInputs.length) {
        final currentSection =
            workflowProvider.allWorkflowInputs[currentSectionIndex];

        // Check both systemInputs and inputFieldsSystem for backward compatibility
        // Use a map to avoid duplicates based on attributeId
        final Map<String, InputField> systemFieldsMap = {};

        // Add fields from systemInputs
        for (final field in currentSection.systemInputs) {
          systemFieldsMap[field.attributeId] = field;
        }

        // Add fields from inputFieldsSystem if they don't already exist
        for (final field in currentSection.inputFieldsSystem) {
          if (!systemFieldsMap.containsKey(field.attributeId)) {
            systemFieldsMap[field.attributeId] = field;
          }
        }

        // Convert map to list
        final allSystemFields = systemFieldsMap.values.toList();

        for (final field in allSystemFields) {
          if (message.content.startsWith('${field.displayName}: ')) {
            // This is a system field value for the current section
            return true;
          }
        }
      }

      // If this is a system field value for another section, don't show it
      for (int i = 0; i < workflowProvider.allWorkflowInputs.length; i++) {
        if (i == currentSectionIndex) continue; // Skip current section

        final section = workflowProvider.allWorkflowInputs[i];

        // Check both systemInputs and inputFieldsSystem for backward compatibility
        // Use a map to avoid duplicates based on attributeId
        final Map<String, InputField> systemFieldsMap = {};

        // Add fields from systemInputs
        for (final field in section.systemInputs) {
          systemFieldsMap[field.attributeId] = field;
        }

        // Add fields from inputFieldsSystem if they don't already exist
        for (final field in section.inputFieldsSystem) {
          if (!systemFieldsMap.containsKey(field.attributeId)) {
            systemFieldsMap[field.attributeId] = field;
          }
        }

        // Convert map to list
        final allSystemFields = systemFieldsMap.values.toList();

        for (final field in allSystemFields) {
          if (message.content.startsWith('${field.displayName}: ')) {
            // This is a system field value for another section
            return false;
          }
        }
      }
    }

    // Transition messages are only shown when viewing the latest section
    if (message.content.startsWith('Step completed successfully') ||
        message.content.startsWith('Moving to the next step') ||
        message.content.startsWith('New information received') ||
        message.content.startsWith('There are more steps to complete')) {
      return isLatestSection;
    }

    // Completion messages are only shown in the latest section
    if (message.content.startsWith('All steps completed successfully')) {
      return isLatestSection;
    }

    // Error messages are shown in all sections
    if (message.content.startsWith('Error:')) {
      return true;
    }

    // "Please provide information" prompt is only shown for the current section
    if (message.content == 'Please provide the following information:') {
      // For the first section
      if (currentSectionIndex == 0) {
        return true;
      }
      // For later sections, only show when viewing that specific section
      return isLatestSection;
    }

    // For user messages, check if they correspond to a field in the current section
    if (message.role == MessageRole.user) {
      // Special case for submit confirmation messages - only show in latest section
      if (message.content == 'Yes, submit the workflow' ||
          message.content == 'No, I need to make changes') {
        return isLatestSection;
      }

      // For other user messages, determine which section they belong to
      // by finding the corresponding field they're responding to

      // First, try to find which section this message belongs to
      int messageSectionIndex = -1;

      // Find the index of this message
      final messageIndex = _chatMessages.indexOf(message);

      // Look for the assistant message that came before this user message
      if (messageIndex > 0) {
        for (int i = messageIndex - 1; i >= 0; i--) {
          final prevMessage = _chatMessages[i];

          // Check if this is a prompt message (not a special message)
          if (prevMessage.role == MessageRole.assistant &&
              !prevMessage.content.startsWith('_')) {
            // Try to find which section this prompt belongs to
            for (int sectionIdx = 0;
                sectionIdx < workflowProvider.allWorkflowInputs.length;
                sectionIdx++) {
              final section = workflowProvider.allWorkflowInputs[sectionIdx];

              // Check if any field in this section matches the prompt
              for (final field in section.inputFields) {
                if (prevMessage.content
                    .toLowerCase()
                    .contains(field.displayName.toLowerCase())) {
                  messageSectionIndex = sectionIdx;
                  break;
                }
              }

              if (messageSectionIndex >= 0) break;
            }

            // If we found a section, stop looking at previous messages
            if (messageSectionIndex >= 0) break;
          }
        }
      }

      // If we couldn't determine the section, default to not showing the message
      if (messageSectionIndex < 0) return false;

      // Only show the message if it belongs to the current section
      return messageSectionIndex == currentSectionIndex;
    }

    // For assistant messages, check if they correspond to a field in the current section
    if (message.role == MessageRole.assistant) {
      // For field prompts, determine which section they belong to
      if (!message.content.startsWith('_') &&
          !message.content.startsWith('Welcome') &&
          !message.content.startsWith('Error:') &&
          !message.content.startsWith('Step completed') &&
          !message.content.startsWith('Moving to') &&
          !message.content.startsWith('New information') &&
          !message.content.startsWith('There are more') &&
          !message.content.startsWith('All steps completed') &&
          message.content != 'System Information:' &&
          message.content != 'Please provide the following information:') {
        // Try to find which section this prompt belongs to
        int messageSectionIndex = -1;

        for (int sectionIdx = 0;
            sectionIdx < workflowProvider.allWorkflowInputs.length;
            sectionIdx++) {
          final section = workflowProvider.allWorkflowInputs[sectionIdx];

          // Check if any field in this section matches the message
          for (final field in section.inputFields) {
            if (message.content
                .toLowerCase()
                .contains(field.displayName.toLowerCase())) {
              messageSectionIndex = sectionIdx;
              break;
            }
          }

          if (messageSectionIndex >= 0) break;
        }

        // If we couldn't determine the section, default to showing it only in the latest section
        if (messageSectionIndex < 0) return isLatestSection;

        // Only show the message if it belongs to the current section
        return messageSectionIndex == currentSectionIndex;
      }
    }

    // For interactive controls, check if they belong to the current section
    if (message.content.startsWith('_interactive_control_')) {
      final fieldId = message.content.substring('_interactive_control_'.length);

      // Find which section this field belongs to
      for (int i = 0; i < workflowProvider.allWorkflowInputs.length; i++) {
        final section = workflowProvider.allWorkflowInputs[i];

        // Check if the field is in this section
        for (final field in section.inputFields) {
          if (field.attributeId == fieldId) {
            // Show this control only when viewing its section
            return i == currentSectionIndex;
          }
        }
      }
      return false;
    }

    // For system fields, check if they belong to the current section
    if (message.content.startsWith('_system_field_')) {
      final fieldId = message.content.substring('_system_field_'.length);

      // Check if this system field belongs to the current section
      if (currentSectionIndex < workflowProvider.allWorkflowInputs.length) {
        final currentSection =
            workflowProvider.allWorkflowInputs[currentSectionIndex];

        // Check both systemInputs and inputFieldsSystem for backward compatibility
        // Use a map to avoid duplicates based on attributeId
        final Map<String, InputField> systemFieldsMap = {};

        // Add fields from systemInputs
        for (final field in currentSection.systemInputs) {
          systemFieldsMap[field.attributeId] = field;
        }

        // Add fields from inputFieldsSystem if they don't already exist
        for (final field in currentSection.inputFieldsSystem) {
          if (!systemFieldsMap.containsKey(field.attributeId)) {
            systemFieldsMap[field.attributeId] = field;
          }
        }

        // Convert map to list
        final allSystemFields = systemFieldsMap.values.toList();

        // Check if the field is in the current section
        for (final field in allSystemFields) {
          if (field.attributeId == fieldId) {
            return true;
          }
        }
      }

      // If this system field belongs to another section, don't show it
      return false;
    }

    // For submit section messages, check if they belong to the current section
    if (message.content.startsWith('_submit_section_')) {
      final sectionId = message.content.substring('_submit_section_'.length);
      final sectionIndex = int.tryParse(sectionId.split('_').last) ?? -1;
      return sectionIndex == currentSectionIndex;
    }

    // For submit buttons message, show only in the last section
    if (message.content == '_submit_buttons_') {
      return currentSectionIndex ==
          workflowProvider.allWorkflowInputs.length - 1;
    }

    // By default, don't show the message when filtering
    return false;
  }

  // Method to scroll to the bottom of the chat
  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_chatScrollController.hasClients) {
        _chatScrollController.animateTo(
          _chatScrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  String _currentSectionId = '';

  // Process the next input field in the workflow
  void _processNextInputField() {
    Logger.info(
        'Processing next input field: section $_currentSectionId, index $_currentInputIndex');

    final workflowProvider =
        Provider.of<WorkflowProvider>(context, listen: false);
    final allInputs = workflowProvider.allWorkflowInputs;

    Logger.info('Number of input sections: ${allInputs.length}');

    // Check if _currentSectionId is valid
    if (_currentSectionId.isEmpty || !_currentSectionId.contains('_')) {
      // If not valid and we have inputs, set it to the first section
      if (allInputs.isNotEmpty) {
        _currentSectionId = 'section_0';
        _currentInputIndex = 0;
        _waitingForUserInput = false;
        Logger.info('Reset to first section due to invalid section ID');
      } else {
        Logger.warning('No input sections available');
        return;
      }
    }

    // Check if all fields are completed
    if (_areAllFieldsCompleted()) {
      Logger.info('All fields in section $_currentSectionId are completed');
      _waitingForUserInput = false;

      // Show completion message if not already shown
      bool hasCompletionMessage = _chatMessages.any((message) =>
          message.content ==
              'All fields completed. Would you like to submit this section?' &&
          message.role == MessageRole.assistant);

      if (!hasCompletionMessage) {
        setState(() {
          _chatMessages.add(Message(
            content:
                'All fields completed. Would you like to submit this section?',
            role: MessageRole.assistant,
          ));

          // Add Yes/No buttons
          _chatMessages.add(Message(
            content: '_submit_section_$_currentSectionId',
            role: MessageRole.assistant,
          ));
        });
      }
      return;
    }

    if (_waitingForUserInput) {
      // Don't process the next field if we're waiting for user input
      Logger.info('Waiting for user input, not processing next field');
      return;
    }

    if (_processingDependentFields) {
      // Don't process the next field if we're processing dependent fields
      Logger.info('Processing dependent fields, not processing next field yet');
      return;
    }

    // Check if we have new inputs from the API
    if (workflowProvider.hasNewInputs) {
      Logger.info('New inputs detected from API, resetting state');

      // Reset the flag
      workflowProvider.resetNewInputsFlag();

      // Set the current section to the last section (newest one)
      if (allInputs.isNotEmpty) {
        _currentSectionId = 'section_${allInputs.length - 1}';
        _currentInputIndex = 0;
        _waitingForUserInput = false;
        _currentChatInputField = null;
        Logger.info('Reset to the newest section: $_currentSectionId');
      }
    }

    // Check if we already have messages in the chat
    // This helps prevent duplicate section headers when switching to chat view
    bool hasInputMessages = _chatMessages.any((message) =>
        message.content.startsWith('_interactive_control_') ||
        (message.role == MessageRole.assistant &&
            !message.content.startsWith('_')));

    Logger.info('Chat already has input messages: $hasInputMessages');

    if (allInputs.isEmpty) {
      // No inputs available yet
      setState(() {
        _chatMessages.add(Message(
          content:
              'I\'m preparing the workflow inputs. Please wait a moment...',
          role: MessageRole.assistant,
        ));
      });
      return;
    }

    // Find the next section and input field to process
    if (_currentSectionId.isEmpty && allInputs.isNotEmpty) {
      _currentSectionId = 'section_0';
      _currentInputIndex = 0;

      // If we're switching to chat view, always start from the beginning
      // to ensure all fields (including system fields) are properly displayed
      if (hasInputMessages) {
        Logger.info('Chat view initialized, starting from the first field');

        // Always start from the first field when switching to chat view
        _currentSectionId = 'section_0';
        _currentInputIndex = 0;

        // Don't clear existing chat messages - preserve the conversation
        // Just log that we're continuing with the existing chat
        Logger.info('Continuing with existing chat conversation');

        // Make sure system fields are displayed
        final workflowProvider =
            Provider.of<WorkflowProvider>(context, listen: false);
        if (workflowProvider.allWorkflowInputs.isNotEmpty) {
          // Check if we already have a system information header
          bool hasSystemHeader = _chatMessages.any((message) =>
              message.content == 'System Information:' &&
              message.role == MessageRole.assistant);

          if (!hasSystemHeader) {
            // Add a header for system fields
            setState(() {
              _chatMessages.add(Message(
                content: 'System Information:',
                role: MessageRole.assistant,
              ));

              // Get all system fields from all sections
              for (final section in workflowProvider.allWorkflowInputs) {
                // Process system inputs - check both systemInputs and inputFieldsSystem
                final hasSystemFields = section.systemInputs.isNotEmpty ||
                    section.inputFieldsSystem.isNotEmpty;

                if (hasSystemFields) {
                  // Combine both system input collections for backward compatibility
                  // Use a map to avoid duplicates based on attributeId
                  final Map<String, InputField> systemFieldsMap = {};

                  // Add fields from systemInputs
                  for (final field in section.systemInputs) {
                    systemFieldsMap[field.attributeId] = field;
                  }

                  // Add fields from inputFieldsSystem if they don't already exist
                  for (final field in section.inputFieldsSystem) {
                    if (!systemFieldsMap.containsKey(field.attributeId)) {
                      systemFieldsMap[field.attributeId] = field;
                    }
                  }

                  // Convert map to list and sort
                  final allSystemFields = systemFieldsMap.values.toList();
                  allSystemFields
                      .sort((a, b) => a.attributeId.compareTo(b.attributeId));

                  // Add each system field
                  for (final field in allSystemFields) {
                    final fieldValue = field.inputValue != null
                        ? field.inputValue.toString()
                        : '';

                    // Add the field as a regular message
                    _chatMessages.add(Message(
                      content: '${field.displayName}: $fieldValue',
                      role: MessageRole.assistant,
                    ));

                    Logger.info(
                        'Added system field ${field.attributeId} with value $fieldValue');
                  }
                }

                // Process info inputs
                if (section.infoInputs.isNotEmpty) {
                  // Add a header for info fields if not already added
                  bool hasInfoHeader = _chatMessages.any((message) =>
                      message.content == 'Information:' &&
                      message.role == MessageRole.assistant);

                  if (!hasInfoHeader) {
                    _chatMessages.add(Message(
                      content: 'Information:',
                      role: MessageRole.assistant,
                    ));
                  }

                  // Sort info fields by attribute_id
                  final sortedInfoFields =
                      List<InputField>.from(section.infoInputs);
                  sortedInfoFields
                      .sort((a, b) => a.attributeId.compareTo(b.attributeId));

                  // Add each info field
                  for (final field in sortedInfoFields) {
                    final fieldValue = field.inputValue != null
                        ? field.inputValue.toString()
                        : '';

                    // Add the field as a regular message
                    _chatMessages.add(Message(
                      content: '${field.displayName}: $fieldValue',
                      role: MessageRole.assistant,
                    ));

                    Logger.info(
                        'Added info field ${field.attributeId} with value $fieldValue');
                  }
                }
              }

              // Add a separator
              _chatMessages.add(Message(
                content: 'Please provide the following information:',
                role: MessageRole.assistant,
              ));
            });
          }
        }
      }
    } else if (_currentInputIndex >=
        allInputs[int.parse(_currentSectionId.split('_')[1])]
                .inputFields
                .length +
            allInputs[int.parse(_currentSectionId.split('_')[1])]
                .inputFieldsSystem
                .length) {
      // Move to the next section if available
      int nextSectionIndex = int.parse(_currentSectionId.split('_')[1]) + 1;
      if (nextSectionIndex < allInputs.length) {
        _currentSectionId = 'section_$nextSectionIndex';
        _currentInputIndex = 0;
      } else {
        // All inputs processed - verify that all required fields have values
        final workflowProvider =
            Provider.of<WorkflowProvider>(context, listen: false);
        final allInputs = workflowProvider.allWorkflowInputs;
        final inputStore = workflowProvider.inputValueStore;
        bool allRequiredFieldsHaveValues = true;

        // Check all required fields
        for (int i = 0; i < allInputs.length; i++) {
          final sectionId = 'section_$i';
          final section = allInputs[i];

          // Check user inputs
          for (final field in section.userInputs) {
            if (field.required) {
              if (!inputStore.hasValue(sectionId, field.attributeId)) {
                allRequiredFieldsHaveValues = false;
                Logger.warning(
                    'Required user field ${field.attributeId} in section $sectionId has no value');
              }
            }
          }

          // Check dependent inputs
          for (final field in section.dependentInputs) {
            if (field.required) {
              if (!inputStore.hasValue(sectionId, field.attributeId)) {
                allRequiredFieldsHaveValues = false;
                Logger.warning(
                    'Required dependent field ${field.attributeId} in section $sectionId has no value');
              }
            }
          }
        }

        if (allRequiredFieldsHaveValues) {
          // All required fields have values, show submit buttons
          setState(() {
            _chatMessages.add(Message(
              content:
                  'Great! All inputs have been collected. Would you like to submit this workflow?',
              role: MessageRole.assistant,
            ));

            // Add a special message for buttons
            _chatMessages.add(Message(
              content: '_submit_buttons_',
              role: MessageRole.assistant,
            ));
          });
          _waitingForUserInput =
              false; // Set to false to avoid inconsistent state
          _currentChatInputField = null;
        } else {
          // Some required fields are missing, go back to the first missing field
          setState(() {
            _chatMessages.add(Message(
              content:
                  'Some required information is missing. Let me help you complete the form.',
              role: MessageRole.assistant,
            ));
          });

          // Reset to the beginning and start over
          _currentSectionId = 'section_0';
          _currentInputIndex = 0;
          _waitingForUserInput = false;

          // Process the next field after a short delay
          Future.delayed(const Duration(milliseconds: 800), () {
            if (mounted) {
              _processNextInputField();
            }
          });
        }
        return;
      }
    }

    // Check if _currentSectionId is valid
    if (_currentSectionId.isEmpty || !_currentSectionId.contains('_')) {
      // If not valid, set it to the first section
      if (allInputs.isNotEmpty) {
        _currentSectionId = 'section_0';
        _currentInputIndex = 0;
        Logger.info('Reset to first section due to invalid section ID');
      } else {
        Logger.warning('No input sections available');
        return;
      }
    }

    try {
      // Get the current section index
      final sectionIndex = int.parse(_currentSectionId.split('_')[1]);

      // Check if the section index is valid
      if (sectionIndex < 0 || sectionIndex >= allInputs.length) {
        // If not valid, set it to the first section
        _currentSectionId = 'section_0';
        _currentInputIndex = 0;
        Logger.info('Reset to first section due to invalid section index');
      }

      // Get the current section
      final currentSection =
          allInputs[int.parse(_currentSectionId.split('_')[1])];

      // Combine system fields and user input fields
      final List<InputField> allFields = [];

      // In chat view, we need to handle system and info fields differently
      if (_showChat) {
        // Include system and info fields in chat view
        Logger.info('Including system fields in chat view');
        allFields.addAll(currentSection.systemInputs);

        Logger.info('Including info fields in chat view');
        allFields.addAll(currentSection.infoInputs);

        Logger.info('Including user input fields in chat view');
        allFields.addAll(currentSection.userInputs);

        // Log dependent inputs for debugging
        Logger.info(
            'Processing dependent inputs for chat view section $_currentSectionId');
        Logger.info(
            'Total dependent inputs: ${currentSection.dependentInputs.length}');

        // Add all dependent fields to the list - visibility will be controlled when presenting
        allFields.addAll(currentSection.dependentInputs);

        // Log each dependent field for debugging
        for (final field in currentSection.dependentInputs) {
          final parentIds = field.parentIds ?? [];

          Logger.info(
              'Chat view - Including dependent field ${field.displayName} (${field.attributeId}): '
              'UI=${field.uiControl}, '
              'parentIds=${parentIds.join(", ")}, '
              'value=${field.inputValue}, '
              'options=${field.dropdownOptions?.length ?? 0}');
        }
      } else {
        // In form view, include all types of fields
        allFields.addAll(currentSection.systemInputs);
        allFields.addAll(currentSection.infoInputs);
        allFields.addAll(currentSection.userInputs);

        // Log dependent inputs for debugging
        Logger.info(
            'Processing dependent inputs for form view section $_currentSectionId');
        Logger.info(
            'Total dependent inputs: ${currentSection.dependentInputs.length}');

        // Add all dependent fields to the list - visibility will be controlled when presenting
        allFields.addAll(currentSection.dependentInputs);

        // Log each dependent field for debugging
        for (final field in currentSection.dependentInputs) {
          final parentIds = field.parentIds ?? [];

          Logger.info(
              'Form view - Including dependent field ${field.displayName} (${field.attributeId}): '
              'UI=${field.uiControl}, '
              'parentIds=${parentIds.join(", ")}, '
              'value=${field.inputValue}, '
              'options=${field.dropdownOptions?.length ?? 0}');
        }
      }

      // Sort all fields by attribute_id
      allFields.sort((a, b) => a.attributeId.compareTo(b.attributeId));

      // Check if the input index is valid
      if (_currentInputIndex < 0 || _currentInputIndex >= allFields.length) {
        // If not valid, set it to the first input field
        _currentInputIndex = 0;
        Logger.info('Reset to first input field due to invalid input index');
      }

      // Log all fields for debugging
      Logger.info('All fields in section $_currentSectionId:');
      for (int i = 0; i < allFields.length; i++) {
        final field = allFields[i];
        Logger.info(
            '  Field $i: ${field.displayName} (${field.attributeId}), type: ${field.sourceType}, dependency: ${field.dependencyType}');
      }

      if (_currentInputIndex < allFields.length) {
        _currentChatInputField = allFields[_currentInputIndex];
        Logger.info(
            'Current field index: $_currentInputIndex, field: ${_currentChatInputField!.displayName} (${_currentChatInputField!.attributeId})');

        // Skip showing section header with local objective name in chat view
        // Just log it for debugging purposes
        if (_currentInputIndex == 0 &&
            currentSection.localObjective.isNotEmpty) {
          Logger.info(
              'Processing section with objective: ${currentSection.localObjective}');
        }

        // Present the input field to the user
        _presentInputField(_currentChatInputField!, _currentSectionId);

        // Only wait for user input if it's not a system, info, or calculation field
        final isSystemField =
            _currentChatInputField!.sourceType.toLowerCase() == 'system';
        final isInfoField =
            _currentChatInputField!.sourceType.toLowerCase() == 'information' ||
                _currentChatInputField!.metadata.isInformational;
        final isCalculationField =
            _currentChatInputField!.dependencyType == 'calculation';

        _waitingForUserInput =
            !isSystemField && !isInfoField && !isCalculationField;

        // Present the field based on its type
        if (isSystemField || isInfoField) {
          // For system and info fields, show them and automatically proceed
          // Increment the index here since we're handling it directly
          _currentInputIndex++;
          _presentSystemInputField(_currentChatInputField!, _currentSectionId);
        } else if (isCalculationField) {
          // For calculation fields, show them and automatically proceed
          // Increment the index here since we're handling it directly
          _currentInputIndex++;
          _presentCalculationField(_currentChatInputField!, _currentSectionId);
        }
      } else {
        Logger.warning('No more input fields in current section');

        // Check if we need to move to the next section
        int nextSectionIndex = int.parse(_currentSectionId.split('_')[1]) + 1;
        if (nextSectionIndex < allInputs.length) {
          // Move to the next section
          _currentSectionId = 'section_$nextSectionIndex';
          _currentInputIndex = 0;
          _waitingForUserInput = false;

          Logger.info('Moving to next section: $_currentSectionId');

          // Process the next field after a short delay
          Future.delayed(const Duration(milliseconds: 800), () {
            if (mounted) {
              _processNextInputField();
            }
          });
        } else {
          // We've reached the end of all sections and fields
          Logger.info('All sections and fields have been processed');

          // Show a completion message if not already shown
          bool hasCompletionMessage = _chatMessages.any((message) =>
              message.content ==
                  'All fields completed. Would you like to submit this section?' &&
              message.role == MessageRole.assistant);

          if (!hasCompletionMessage) {
            setState(() {
              _chatMessages.add(Message(
                content:
                    'All fields completed. Would you like to submit this section?',
                role: MessageRole.assistant,
              ));

              // Add Yes/No buttons
              _chatMessages.add(Message(
                content: '_submit_section_$_currentSectionId',
                role: MessageRole.assistant,
              ));
            });
          }

          // Reset the waiting flag to false since we're done with all fields
          _waitingForUserInput = false;
        }
      }
    } catch (e) {
      Logger.error('Error processing next input field: $e');
      // Reset to a safe state
      if (allInputs.isNotEmpty) {
        _currentSectionId = 'section_0';
        _currentInputIndex = 0;
        Logger.info('Reset to first section due to error');

        // Try again after resetting
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted) {
            _processNextInputField();
          }
        });
      }
    }
  }

  // Present an input field to the user in the chat
  void _presentInputField(InputField field, String sectionId) {
    Logger.info(
        'Presenting input field: ${field.attributeId} in section: $sectionId');
    Logger.info('Field display name: ${field.displayName}');
    Logger.info('Field UI control: ${field.uiControl}');
    Logger.info('Field source type: ${field.sourceType}');

    // Check if this is a dependent field
    final isDependentField =
        field.parentIds != null && field.parentIds!.isNotEmpty;

    // Check if this is a calculation field
    final isCalculationField = field.dependencyType == 'calculation';

    // Show calculation fields with a message and immediately proceed to next field
    if (isCalculationField) {
      Logger.info(
          'Showing calculation field ${field.attributeId} in chat view');
      _presentCalculationField(field, sectionId);
      return;
    }

    if (isDependentField) {
      // For dependent fields, check if it should be shown
      final parentIds = field.parentIds ?? [];
      final isDropdownOrCombobox = field.uiControl == 'oj-select-single' ||
          field.uiControl == 'oj-combobox-one';

      // Determine visibility based on parent values
      bool shouldShow = true;

      // First check if all parent fields have values - this is a prerequisite
      bool allParentsHaveValues =
          checkAllParentValuesHaveData(parentIds, sectionId);

      // If parent fields don't have values, don't show the dependent field
      if (!allParentsHaveValues) {
        shouldShow = false;
        Logger.info(
            'Field ${field.attributeId} hidden because parent fields don\'t have values in chat view');
      } else {
        // Check if we have a value in either input store
        final hasValueInLocalStore =
            _inputStore.hasValue(sectionId, field.attributeId);
        final hasValueInProviderStore =
            Provider.of<WorkflowProvider>(context, listen: false)
                .inputValueStore
                .hasValue(sectionId, field.attributeId);

        // If we have a value in either store, show the field
        if (hasValueInLocalStore || hasValueInProviderStore) {
          shouldShow = true;
          Logger.info(
              'Field ${field.attributeId} has value in input store, showing it in chat view');
        }
        // Otherwise, apply the normal filtering logic
        else if (field.inputValue == null) {
          if (isDropdownOrCombobox) {
            // For dropdown/combobox, hide if dropdown options are empty
            shouldShow = field.dropdownOptions != null &&
                field.dropdownOptions!.isNotEmpty;

            Logger.info(
                'Field ${field.attributeId} is dropdown with ${field.dropdownOptions?.length ?? 0} options in chat view, shouldShow=$shouldShow');
          } else {
            // For non-dropdown/combobox, hide if inputValue is null
            shouldShow = false;
            Logger.info(
                'Field ${field.attributeId} is not dropdown and has null inputValue in chat view, hiding it');
          }
        }
      }

      Logger.info(
          'Dependent field ${field.displayName} (${field.attributeId}): '
          'UI=${field.uiControl}, '
          'isDropdown=$isDropdownOrCombobox, '
          'parentIds=${parentIds.join(", ")}, '
          'shouldShow=$shouldShow');

      // If the field shouldn't be shown, skip it and move to the next field
      if (!shouldShow) {
        Logger.info(
            'Skipping dependent field ${field.attributeId} as it should not be shown');
        _currentInputIndex++;

        // Make sure we're not in the middle of processing dependent fields
        if (!_processingDependentFields) {
          Logger.info('Moving to next field immediately');
          _processNextInputField();
        } else {
          Logger.info(
              'Will process next field after dependent field processing completes');
          // The next field will be processed after the dependent field processing completes
          // in _handleUserInputForField
        }
        return;
      }
    }

    // Handle system and information fields differently
    if (field.sourceType.toLowerCase() == 'system' ||
        field.sourceType.toLowerCase() == 'information' ||
        field.metadata.isInformational) {
      _presentSystemInputField(field, sectionId);
      return;
    }

    // Handle regular user input fields
    String message = 'Please provide ${field.displayName}';

    // Add information about the field type
    if (field.required) {
      message += ' (required)';
    }

    // Add information about allowed values if available
    // if (field.allowedValues != null && field.allowedValues!.isNotEmpty) {
    //   message += '\nAllowed values: ${field.allowedValues!.join(', ')}';
    // }

    // Check if we already have this input field message in the chat
    bool hasInputFieldMessage = _chatMessages.any((chatMessage) =>
        chatMessage.content == message &&
        chatMessage.role == MessageRole.assistant);

    // Add the message to the chat if it doesn't already exist
    if (!hasInputFieldMessage) {
      setState(() {
        _chatMessages.add(Message(
          content: message,
          role: MessageRole.assistant,
        ));
      });
      Logger.info('Added input field message: "$message"');
    } else {
      Logger.info('Input field message "$message" already exists, skipping');
    }

    // For non-text inputs, add interactive buttons if they don't already exist
    if (field.uiControl != 'oj-input-text' &&
        field.uiControl != 'oj-text-area') {
      // Check if we already have this interactive control in the chat
      bool hasInteractiveControl = _chatMessages.any((message) =>
          message.content == '_interactive_control_${field.attributeId}');

      if (!hasInteractiveControl) {
        setState(() {
          _chatMessages.add(Message(
            content: '_interactive_control_${field.attributeId}',
            role: MessageRole.assistant,
          ));
        });
        Logger.info('Added interactive control for ${field.attributeId}');
      } else {
        Logger.info(
            'Interactive control for ${field.attributeId} already exists, skipping');
      }
    }
  }

  // Present a calculation field in the chat
  void _presentCalculationField(InputField field, String sectionId) {
    Logger.info(
        'Presenting calculation field: ${field.attributeId} with value: ${field.inputValue}');

    // Create a message to display the field directly
    String message = '${field.displayName}: ';

    // Add the value if available
    if (field.inputValue != null) {
      message += field.inputValue.toString();
    } else {
      message += 'Calculating...';
    }

    // Check if we already have this field message in the chat
    bool hasFieldMessage = _chatMessages.any((chatMessage) =>
        chatMessage.content == message &&
        chatMessage.role == MessageRole.assistant);

    // Add the message to the chat if it doesn't already exist
    if (!hasFieldMessage) {
      // Check if we need to add the appropriate header
      String headerText = 'Calculated Values:';
      bool hasHeader = _chatMessages.any((chatMessage) =>
          chatMessage.content == headerText &&
          chatMessage.role == MessageRole.assistant);

      setState(() {
        // Add the header if it doesn't exist
        if (!hasHeader) {
          _chatMessages.add(Message(
            content: headerText,
            role: MessageRole.assistant,
          ));
        }

        // Add the field message
        _chatMessages.add(Message(
          content: message,
          role: MessageRole.assistant,
        ));
      });

      Logger.info('Added calculation field message: "$message"');
    } else {
      Logger.info(
          'Calculation field message "$message" already exists, skipping');
    }

    // Store the value if available
    if (field.inputValue != null) {
      final workflowProvider =
          Provider.of<WorkflowProvider>(context, listen: false);
      _inputStore.setValue(sectionId, field.attributeId, field.inputValue);
      workflowProvider.inputValueStore
          .setValue(sectionId, field.attributeId, field.inputValue);
      Logger.info(
          'Stored calculation value for ${field.attributeId}: ${field.inputValue}');
    }

    // IMPORTANT: Don't increment _currentInputIndex here
    // We'll let _processNextInputField handle it
    // This ensures we don't skip any fields

    // Make sure we're not waiting for user input
    _waitingForUserInput = false;

    // Process the next field immediately
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        Logger.info('Moving to next field after showing calculation field');
        // Set a flag to indicate we're done processing this calculation field
        _processingDependentFields = false;
        _processingDependentFieldsStartTime = null;

        // Get the current section
        final workflowProvider =
            Provider.of<WorkflowProvider>(context, listen: false);
        final allInputs = workflowProvider.allWorkflowInputs;
        final sectionIndex = int.parse(sectionId.split('_')[1]);

        if (sectionIndex < allInputs.length) {
          final currentSection = allInputs[sectionIndex];

          // Get all fields in this section
          final allFields = <InputField>[];
          allFields.addAll(currentSection.systemInputs);
          allFields.addAll(currentSection.infoInputs);
          allFields.addAll(currentSection.userInputs);
          allFields.addAll(currentSection.dependentInputs);

          // Log the current state
          Logger.info(
              'Current index: $_currentInputIndex, total fields: ${allFields.length}');

          // Check if we're at the last field
          if (_currentInputIndex >= allFields.length) {
            Logger.info('Reached the end of all fields in section $sectionId');
          }
        }

        _processNextInputField();
      }
    });
  }

  // Present a system or info input field in the chat
  void _presentSystemInputField(InputField field, String sectionId) {
    final isSystemField = field.sourceType.toLowerCase() == 'system';
    final isInfoField = field.sourceType.toLowerCase() == 'information' ||
        field.metadata.isInformational;

    Logger.info(
        'Presenting ${isSystemField ? "system" : "info"} field: ${field.attributeId} with value: ${field.inputValue}');

    // Create a message to display the field directly
    String message = '${field.displayName}: ';

    // Add the value if available (only for system fields)
    if (isSystemField && field.inputValue != null) {
      message += field.inputValue.toString();
    } else if (isInfoField && field.inputValue != null) {
      message += field.inputValue.toString();
    } else if (isSystemField) {
      message += 'Not available';
    }

    // Check if we already have this field message in the chat
    bool hasFieldMessage = _chatMessages.any((chatMessage) =>
        chatMessage.content == message &&
        chatMessage.role == MessageRole.assistant);

    // Add the message to the chat if it doesn't already exist
    if (!hasFieldMessage) {
      // Check if we need to add the appropriate header
      String headerText =
          isSystemField ? 'System Information:' : 'Information:';
      bool hasHeader = _chatMessages.any((chatMessage) =>
          chatMessage.content == headerText &&
          chatMessage.role == MessageRole.assistant);

      setState(() {
        // Add the header if it doesn't exist
        if (!hasHeader) {
          _chatMessages.add(Message(
            content: headerText,
            role: MessageRole.assistant,
          ));
        }

        // Add the field message
        _chatMessages.add(Message(
          content: message,
          role: MessageRole.assistant,
        ));
      });

      Logger.info(
          'Added ${isSystemField ? "system" : "info"} field message: "$message"');
    } else {
      Logger.info(
          '${isSystemField ? "System" : "Info"} field message "$message" already exists, skipping');
    }

    // IMPORTANT: Don't increment _currentInputIndex here
    // We'll let _processNextInputField handle it
    // This ensures we don't skip any fields

    // Make sure we're not waiting for user input
    _waitingForUserInput = false;

    // Process the next field immediately
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        Logger.info(
            'Moving to next field after showing ${isSystemField ? "system" : "info"} field');
        // Set a flag to indicate we're done processing this system/info field
        _processingDependentFields = false;
        _processingDependentFieldsStartTime = null;

        // Get the current section
        final workflowProvider =
            Provider.of<WorkflowProvider>(context, listen: false);
        final allInputs = workflowProvider.allWorkflowInputs;
        final sectionIndex = int.parse(sectionId.split('_')[1]);

        if (sectionIndex < allInputs.length) {
          final currentSection = allInputs[sectionIndex];

          // Get all fields in this section
          final allFields = <InputField>[];
          allFields.addAll(currentSection.systemInputs);
          allFields.addAll(currentSection.infoInputs);
          allFields.addAll(currentSection.userInputs);
          allFields.addAll(currentSection.dependentInputs);

          // Log the current state
          Logger.info(
              'Current index: $_currentInputIndex, total fields: ${allFields.length}');

          // Check if we're at the last field
          if (_currentInputIndex >= allFields.length) {
            Logger.info('Reached the end of all fields in section $sectionId');
          }
        }

        _processNextInputField();
      }
    });
  }

  // Handle editing a user input
  void _handleEditUserInput(
      Message message, InputField? field, String? sectionId) {
    // Safety check
    if (field == null || sectionId == null) {
      Logger.warning('Cannot edit: field or sectionId is null');
      return;
    }
    Logger.info(
        'Editing input for field: ${field.attributeId} in section: $sectionId');
    Logger.info('Current value: ${message.content}');

    // Set the current field and section
    _currentChatInputField = field;
    _currentSectionId = sectionId;
    _waitingForUserInput = true;

    // Pre-fill the text controller with the current value
    _textController.text = message.content;

    // Add a message to indicate editing
    setState(() {
      _chatMessages.add(Message(
        content: 'Editing ${field.displayName}. Please provide a new value.',
        role: MessageRole.assistant,
      ));
    });

    // For non-text inputs, add the interactive control again
    if (field.uiControl != 'oj-input-text' &&
        field.uiControl != 'oj-text-area') {
      setState(() {
        _chatMessages.add(Message(
          content: '_interactive_control_${field.attributeId}',
          role: MessageRole.assistant,
        ));
      });
    }

    // Scroll to the bottom to show the edit message
    _scrollToBottom();
  }

  // Handle user input for the current field
  void _handleUserInputForField(String userInput) {
    Logger.info('Handling user input: "$userInput"');

    if (!_waitingForUserInput || _currentChatInputField == null) {
      // Not waiting for input or no current field
      Logger.warning('Not waiting for input or no current field');
      Logger.warning('_waitingForUserInput: $_waitingForUserInput');
      Logger.warning(
          '_currentChatInputField: ${_currentChatInputField?.attributeId}');
      return;
    }

    Logger.info(
        'Processing input for field: ${_currentChatInputField!.attributeId} in section: $_currentSectionId');

    // Validate the input
    String? validationError =
        _validateInput(_currentChatInputField!, userInput);
    if (validationError != null) {
      // Show validation error
      setState(() {
        _chatMessages.add(Message(
          content: 'Error: $validationError. Please try again.',
          role: MessageRole.assistant,
        ));
      });
      return;
    }

    // Store the value directly in the local InputValueStore to ensure it's available
    _inputStore.setValue(
        _currentSectionId, _currentChatInputField!.attributeId, userInput);
    _inputStore.setValue(
        _currentSectionId, _currentChatInputField!.inputId, userInput);

    // Also store in the workflow provider's input store
    final workflowProvider =
        Provider.of<WorkflowProvider>(context, listen: false);
    workflowProvider.inputValueStore.setValue(
        _currentSectionId, _currentChatInputField!.attributeId, userInput);
    workflowProvider.inputValueStore.setValue(
        _currentSectionId, _currentChatInputField!.inputId, userInput);

    Logger.info(
        'Stored value in both input stores: $_currentSectionId:${_currentChatInputField!.attributeId} = $userInput');

    // Add additional debug logging
    Logger.info('After storing value, InputValueStore contains:');
    workflowProvider.inputValueStore.debugPrintValues();

    // Show a loading message while we check for dependent fields
    setState(() {
      _chatMessages.add(Message(
        content: 'Processing your input...',
        role: MessageRole.assistant,
      ));
    });

    // Set a flag to indicate we're processing dependent fields
    _processingDependentFields = true;
    _processingDependentFieldsStartTime = DateTime.now();
    Logger.info(
        'Started dependent field processing at $_processingDependentFieldsStartTime');

    // Use _handleInputValueChanged to check for dependent fields
    // This will trigger API calls for dependent fields if needed
    _handleInputValueChanged(
        _currentSectionId,
        _currentChatInputField!.attributeId,
        userInput,
        _currentChatInputField!.inputId);

    // Check if any of the dependent fields are calculation fields
    final sectionIndex = int.parse(_currentSectionId.split('_')[1]);
    final section = workflowProvider.allWorkflowInputs[sectionIndex];

    // Log if we have any calculation fields
    final calculationFields = section.dependentInputs
        .where((field) => field.dependencyType == 'calculation')
        .toList();

    if (calculationFields.isNotEmpty) {
      Logger.info(
          'Found ${calculationFields.length} calculation fields that may be updated');
      for (final field in calculationFields) {
        Logger.info(
            'Calculation field: ${field.displayName} (${field.attributeId})');
      }

      // Set a flag to indicate we're processing dependent fields
      _processingDependentFields = true;
      _processingDependentFieldsStartTime = DateTime.now();
      Logger.info('Set processing flags for calculation fields');
    }

    // Wait for dependent field API calls to complete
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        // Remove the loading message
        setState(() {
          _chatMessages.removeWhere((message) =>
              message.role == MessageRole.assistant &&
              message.content == 'Processing your input...');

          // Add confirmation message
          _chatMessages.add(Message(
            content:
                'Thank you! ${_currentChatInputField!.displayName} has been set to "$userInput".',
            role: MessageRole.assistant,
          ));
        });

        Logger.info('Resetting _processingDependentFields flag to false');
        // Reset the flags
        _processingDependentFields = false;
        _processingDependentFieldsStartTime = null;

        // Log the current state of the workflow provider
        Logger.info(
            'After processing dependent fields, workflow provider state:');
        workflowProvider.inputValueStore.debugPrintValues();
        Logger.info(
            'Current section: $_currentSectionId, current index: $_currentInputIndex');
        Logger.info(
            'Total sections: ${workflowProvider.allWorkflowInputs.length}');

        if (workflowProvider.allWorkflowInputs.isNotEmpty) {
          final currentSection = workflowProvider
              .allWorkflowInputs[int.parse(_currentSectionId.split('_')[1])];
          Logger.info(
              'Current section has ${currentSection.inputFields.length} fields');

          // Get all fields in this section
          final allFields = <InputField>[];
          allFields.addAll(currentSection.systemInputs);
          allFields.addAll(currentSection.infoInputs);
          allFields.addAll(currentSection.userInputs);
          allFields.addAll(currentSection.dependentInputs);

          // Check if this is the last field in the current section
          bool isLastField = _currentInputIndex >= allFields.length - 1;
          Logger.info(
              'Is last field: $isLastField (current index: $_currentInputIndex, total fields: ${allFields.length})');

          if (isLastField) {
            // If this is the last field, check if all required fields have values before showing submit button
            final missingRequiredFields =
                _checkMissingRequiredFields(_currentSectionId);

            Logger.info(
                'Last field check - Missing required fields: ${missingRequiredFields.isEmpty ? "None" : missingRequiredFields.join(", ")}');

            // Always show the submit buttons, but we'll check again when the user clicks Yes
            setState(() {
              _chatMessages.add(Message(
                content:
                    'All fields completed. Would you like to submit this section?',
                role: MessageRole.assistant,
              ));

              // Add Yes/No buttons
              _chatMessages.add(Message(
                content: '_submit_section_$_currentSectionId',
                role: MessageRole.assistant,
              ));
            });

            // Don't move to the next input field yet
            _waitingForUserInput = false;

            // If there are missing fields, also show a warning
            if (missingRequiredFields.isNotEmpty) {
              // Some required fields are missing, warn the user
              setState(() {
                _chatMessages.add(Message(
                  content:
                      'Note: Some required information is missing: ${missingRequiredFields.join(', ')}. You can provide this information before submitting.',
                  role: MessageRole.assistant,
                ));
              });
            }
          } else {
            // Use our helper method to process the next field
            _processNextFieldAfterDependentProcessing();
          }
        }
      }
    });
  }

  // Check for missing required fields in a section
  List<String> _checkMissingRequiredFields(String sectionId) {
    final workflowProvider =
        Provider.of<WorkflowProvider>(context, listen: false);
    final inputStore = workflowProvider.inputValueStore;
    final allInputs = workflowProvider.allWorkflowInputs;
    final missingFields = <String>[];

    Logger.info('Checking missing required fields for section: $sectionId');
    Logger.info('InputValueStore current state:');
    inputStore.debugPrintValues();

    // Parse the section index from the sectionId
    final sectionIndex = int.parse(sectionId.split('_')[1]);

    // Check if the section index is valid
    if (sectionIndex < 0 || sectionIndex >= allInputs.length) {
      Logger.warning('Invalid section index: $sectionIndex');
      return missingFields;
    }

    // Get the section
    final section = allInputs[sectionIndex];
    Logger.info(
        'Checking section: ${section.localObjective} with ${section.userInputs.length} user fields and ${section.dependentInputs.length} dependent fields');

    // Check user input fields
    for (final field in section.userInputs) {
      Logger.info(
          'Checking user field: ${field.displayName} (${field.attributeId}), required: ${field.required}');

      if (field.required) {
        // Check if the field has a value
        final hasValue = inputStore.hasValue(sectionId, field.attributeId);
        final value = inputStore.getValue(sectionId, field.attributeId);
        Logger.info(
            'Field ${field.displayName} has value: $hasValue, value: $value');

        if (!hasValue) {
          // Add the display name to the list of missing fields
          missingFields.add(field.displayName);
          Logger.warning(
              'Required user field ${field.displayName} (${field.attributeId}) has no value');
        }
      }
    }

    // Check dependent input fields
    for (final field in section.dependentInputs) {
      Logger.info(
          'Checking dependent field: ${field.displayName} (${field.attributeId}), required: ${field.required}');

      if (field.required) {
        // Check if the field has a value
        final hasValue = inputStore.hasValue(sectionId, field.attributeId);
        final value = inputStore.getValue(sectionId, field.attributeId);
        Logger.info(
            'Field ${field.displayName} has value: $hasValue, value: $value');

        if (!hasValue) {
          // Add the display name to the list of missing fields
          missingFields.add(field.displayName);
          Logger.warning(
              'Required dependent field ${field.displayName} (${field.attributeId}) has no value');
        }
      }
    }

    Logger.info(
        'Missing fields for section $sectionId: ${missingFields.isEmpty ? "None" : missingFields.join(", ")}');
    return missingFields;
  }

  // Validate user input for a field
  String? _validateInput(InputField field, String value) {
    // Check if required field is empty
    if (field.required && value.trim().isEmpty) {
      return '${field.displayName} is required';
    }

    // Check if value is in allowed values list
    if (field.allowedValues != null && field.allowedValues!.isNotEmpty) {
      if (!field.allowedValues!.contains(value)) {
        return 'Value must be one of: ${field.allowedValues!.join(', ')}';
      }
    }

    // Check validations if available
    if (field.validations != null) {
      for (var validation in field.validations!) {
        if (validation != null) {
          // Implement validation logic based on validation type
          if (validation['type'] == 'regex' && validation['pattern'] != null) {
            final pattern = RegExp(validation['pattern']);
            if (!pattern.hasMatch(value)) {
              return validation['message'] ?? 'Invalid format';
            }
          }

          // Add more validation types as needed
        }
      }
    }

    return null; // No validation errors
  }

  // Process the next field after dependent field processing
  void _processNextFieldAfterDependentProcessing() {
    Logger.info('Processing next field after dependent field processing');

    // Make sure we're not still processing dependent fields
    if (!_processingDependentFields) {
      // Move to the next input field
      _currentInputIndex++;
      _waitingForUserInput = false;

      Logger.info('Moving to next input field, new index: $_currentInputIndex');

      // Process the next field after a short delay
      Future.delayed(const Duration(milliseconds: 800), () {
        if (mounted) {
          Logger.info('Processing next field after delay');
          _processNextInputField();
        }
      });
    } else {
      Logger.info('Still processing dependent fields, will try again');
      // Try again after another delay, but with a timeout to prevent getting stuck
      Future.delayed(const Duration(milliseconds: 800), () {
        if (mounted) {
          // Check if we've been stuck for too long (more than 5 seconds)
          // This is a safety mechanism to prevent getting stuck in an infinite loop
          if (_processingDependentFieldsStartTime != null &&
              DateTime.now()
                      .difference(_processingDependentFieldsStartTime!)
                      .inSeconds >
                  5) {
            Logger.warning(
                'Dependent field processing timeout exceeded, forcing continuation');
            _processingDependentFields = false;
            _currentInputIndex++;
            _waitingForUserInput = false;
            _processNextInputField();
          } else {
            _processNextFieldAfterDependentProcessing();
          }
        }
      });
    }
  }

  // Check if all fields in the current section are completed
  bool _areAllFieldsCompleted() {
    try {
      final workflowProvider =
          Provider.of<WorkflowProvider>(context, listen: false);
      final allInputs = workflowProvider.allWorkflowInputs;

      if (allInputs.isEmpty) {
        Logger.info('No inputs available, returning false');
        return false;
      }

      // Check if _currentSectionId is valid
      if (_currentSectionId.isEmpty || !_currentSectionId.contains('_')) {
        Logger.info('Invalid section ID: $_currentSectionId, returning false');
        return false;
      }

      // Get the current section index
      final sectionIndex = int.parse(_currentSectionId.split('_')[1]);
      if (sectionIndex >= allInputs.length) {
        Logger.info(
            'Section index $sectionIndex out of bounds, returning false');
        return false;
      }

      // Get the current section
      final section = allInputs[sectionIndex];

      // Get all fields in this section
      final allFields = <InputField>[];

      // Include all field types
      allFields.addAll(section.systemInputs);
      allFields.addAll(section.infoInputs);
      allFields.addAll(section.userInputs);
      allFields.addAll(section.dependentInputs);

      if (allFields.isEmpty) {
        Logger.info('No fields in section $sectionIndex, returning true');
        return true;
      }

      Logger.info(
          'Current index: $_currentInputIndex, total fields: ${allFields.length}');
      Logger.info(
          'System fields: ${section.systemInputs.length}, Info fields: ${section.infoInputs.length}, User fields: ${section.userInputs.length}, Dependent fields: ${section.dependentInputs.length}');

      // Check if we've processed all fields in this section
      return _currentInputIndex >= allFields.length;
    } catch (e) {
      Logger.error('Error in _areAllFieldsCompleted: $e');
      return false;
    }
  }

  // Build an interactive control for a field
  Widget _buildInteractiveControl(InputField field, String sectionId) {
    // Use InputFieldWidget for all UI controls
    return InputFieldWidget(
      field: field,
      sectionId: sectionId,
      isChatView: true,
      onValueChanged:
          _handleInputValueChanged, // Add this to ensure values are stored in the provider
      onChatValueSubmitted: (value) {
        // Add user message with the selected value
        setState(() {
          _chatMessages.add(Message(
            content: value,
            role: MessageRole.user,
          ));
        });

        // Handle the input
        _handleUserInputForField(value);
      },
    );
  }

  Widget chatBody(BuildContext context) {
    // Get the workflow provider
    final workflowProvider =
        Provider.of<WorkflowProvider>(context, listen: false);

    // Adjust font size based on screen size
    final fontSize = MediaQuery.of(context).size.width <= 600 ? 14.0 : 16.0;

    // Initialize chat if empty
    if (_chatMessages.isEmpty) {
      Logger.info('Initializing chat view with welcome messages');

      // Reset the current section and index to ensure we start from the beginning
      _currentSectionId = 'section_0';
      _currentInputIndex = 0;
      _waitingForUserInput = false;
      _currentChatInputField = null;

      // Add system fields first if available
      if (workflowProvider.allWorkflowInputs.isNotEmpty) {
        final firstSection = workflowProvider.allWorkflowInputs[0];

        // Check both systemInputs and inputFieldsSystem for backward compatibility
        final hasSystemFields = firstSection.systemInputs.isNotEmpty ||
            firstSection.inputFieldsSystem.isNotEmpty;

        if (hasSystemFields) {
          // Add a header for system fields
          _chatMessages.add(Message(
            content: 'System Information:',
            role: MessageRole.assistant,
          ));

          // Combine both system input collections for backward compatibility
          // Use a map to avoid duplicates based on attributeId
          final Map<String, InputField> systemFieldsMap = {};

          // Add fields from systemInputs
          for (final field in firstSection.systemInputs) {
            systemFieldsMap[field.attributeId] = field;
          }

          // Add fields from inputFieldsSystem if they don't already exist
          for (final field in firstSection.inputFieldsSystem) {
            if (!systemFieldsMap.containsKey(field.attributeId)) {
              systemFieldsMap[field.attributeId] = field;
            }
          }

          // Convert map to list
          final allSystemFields = systemFieldsMap.values.toList();

          // Sort system fields by attribute_id
          allSystemFields
              .sort((a, b) => a.attributeId.compareTo(b.attributeId));

          // Add each system field
          for (final field in allSystemFields) {
            // If value is null, leave it empty instead of showing 'Not available'
            final fieldValue =
                field.inputValue != null ? field.inputValue.toString() : '';

            // Add the field as a regular message
            _chatMessages.add(Message(
              content: '${field.displayName}: $fieldValue',
              role: MessageRole.assistant,
            ));

            Logger.info(
                'Added system field ${field.attributeId} with value $fieldValue');
          }

          // Add a separator
          _chatMessages.add(Message(
            content: 'Please provide the following information:',
            role: MessageRole.assistant,
          ));
        }
      }

      // Process the first input field after a short delay
      Future.delayed(const Duration(milliseconds: 1000), () {
        if (mounted) {
          _processNextInputField();
        }
      });
    }

    // Log the current state of the InputValueStore
    Logger.info('Current InputValueStore state:');
    workflowProvider.inputValueStore.debugPrintValues();

    // Start processing inputs immediately
    _processNextInputField();

    return Column(
      children: [
        // Chat messages area
        Expanded(child: Builder(builder: (context) {
          // Get the current section index
          final currentSectionIndex = _getLastSectionIndex();

          // Filter messages based on the toggle state
          final filteredMessages = _showOnlyCurrentSectionMessages
              ? _chatMessages
                  .where((message) => _isMessageFromCurrentSection(
                      message, currentSectionIndex))
                  .toList()
              : _chatMessages;

          return ListView.builder(
            controller: _chatScrollController,
            padding: const EdgeInsets.all(16),
            itemCount: filteredMessages.length,
            itemBuilder: (context, index) {
              final message = filteredMessages[index];

              // Scroll to bottom when new messages are added
              if (index == filteredMessages.length - 1) {
                _scrollToBottom();
              }

              // User message bubble
              if (message.role == MessageRole.user) {
                // Find the index of this message to determine if it's the most recent user message
                final messageIndex = _chatMessages.indexOf(message);

                // Find the corresponding input field for this message
                InputField? correspondingField;
                String? correspondingSectionId;

                // For debugging
                Logger.info(
                    'Finding corresponding field for user message: ${message.content}');

                // Look for the assistant message that came before this user message
                if (messageIndex > 0) {
                  for (int i = messageIndex - 1; i >= 0; i--) {
                    final prevMessage = _chatMessages[i];
                    Logger.info(
                        'Checking previous message: ${prevMessage.content}');

                    // Check if this is a prompt message (not a special message)
                    if (prevMessage.role == MessageRole.assistant &&
                        !prevMessage.content.startsWith('_')) {
                      Logger.info(
                          'Found potential prompt message: ${prevMessage.content}');

                      // This is likely the prompt for the user's input
                      // Find the field that matches this prompt
                      for (final section
                          in workflowProvider.allWorkflowInputs) {
                        final sectionIndex =
                            workflowProvider.allWorkflowInputs.indexOf(section);
                        final sectionId = 'section_$sectionIndex';

                        Logger.info(
                            'Checking section with ${section.inputFields.length} fields');

                        for (final field in section.inputFields) {
                          Logger.info(
                              'Checking if prompt contains field: ${field.displayName}');

                          // More lenient check - just see if the field name appears anywhere in the prompt
                          if (prevMessage.content
                              .toLowerCase()
                              .contains(field.displayName.toLowerCase())) {
                            correspondingField = field;
                            correspondingSectionId = sectionId;
                            Logger.info(
                                'Found matching field: ${field.displayName} in section $sectionId');
                            break;
                          }
                        }
                        if (correspondingField != null) break;
                      }

                      // If we found a field, stop looking at previous messages
                      if (correspondingField != null) break;
                    }
                  }
                }

                // For testing, always set a field for now
                if (correspondingField == null &&
                    workflowProvider.allWorkflowInputs.isNotEmpty) {
                  final section = workflowProvider.allWorkflowInputs[0];
                  if (section.inputFields.isNotEmpty) {
                    correspondingField = section.inputFields[0];
                    correspondingSectionId = 'section_0';
                    Logger.info(
                        'Using default field for testing: ${correspondingField.displayName}');
                  }
                }

                // Log whether we found a field
                Logger.info(
                    'Found field: ${correspondingField != null}, section: $correspondingSectionId');

                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.end,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Edit button - always show it for now
                        IconButton(
                          icon: Icon(
                            Icons.edit,
                            size: 20,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          onPressed: () {
                            // Only handle edit if we have a field
                            if (correspondingField != null &&
                                correspondingSectionId != null) {
                              _handleEditUserInput(message, correspondingField,
                                  correspondingSectionId);
                            } else {
                              // Show a message that this field can't be edited
                              setState(() {
                                _chatMessages.add(Message(
                                  content: 'This message cannot be edited.',
                                  role: MessageRole.assistant,
                                ));
                              });
                              _scrollToBottom();
                            }
                          },
                          constraints:
                              BoxConstraints(minWidth: 40, minHeight: 40),
                          padding: EdgeInsets.zero,
                          splashRadius: 24,
                          tooltip: 'Edit message',
                        ),
                        // Message container
                        Container(
                          constraints: BoxConstraints(
                            maxWidth: MediaQuery.of(context).size.width *
                                0.65, // Smaller to accommodate edit button
                          ),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Color(
                                0xFFF0EEE8), // User message background color
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            message.content,
                            style: TextStyle(
                              fontFamily: 'SFProText',
                              fontSize: fontSize,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }

              // Check if this is a submit buttons message
              if (message.content == '_submit_buttons_') {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Container(
                      constraints: BoxConstraints(
                        maxWidth: MediaQuery.of(context).size.width * 0.75,
                      ),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.grey[800]
                            : Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Theme.of(context).dividerColor,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Yes button
                          ElevatedButton(
                            onPressed: () async {
                              // Add user confirmation message
                              setState(() {
                                _chatMessages.add(Message(
                                  content: 'Yes, submit the workflow',
                                  role: MessageRole.user,
                                ));
                              });

                              // Find the last section ID
                              final lastSectionId =
                                  'section_${workflowProvider.allWorkflowInputs.length - 1}';

                              // Check for missing required fields across all sections
                              bool allRequiredFieldsHaveValues = true;
                              List<String> missingFields = [];

                              // Check each section
                              for (int i = 0;
                                  i < workflowProvider.allWorkflowInputs.length;
                                  i++) {
                                final sectionId = 'section_$i';
                                final sectionMissingFields =
                                    _checkMissingRequiredFields(sectionId);

                                if (sectionMissingFields.isNotEmpty) {
                                  allRequiredFieldsHaveValues = false;
                                  missingFields.addAll(sectionMissingFields);
                                }
                              }

                              if (allRequiredFieldsHaveValues) {
                                // Show processing message
                                setState(() {
                                  _chatMessages.add(Message(
                                    content: 'Processing your submission...',
                                    role: MessageRole.assistant,
                                  ));
                                });

                                // Reset the waiting state before submitting
                                setState(() {
                                  _waitingForUserInput = false;
                                  _currentChatInputField = null;
                                });

                                // Log the InputValueStore state before execution
                                Logger.info(
                                    'InputValueStore state before execution:');
                                workflowProvider.inputValueStore
                                    .debugPrintValues();

                                // Use the same approach as form view by calling _handleNextButtonPressed
                                // This ensures consistency between form view and chat view
                                _handleNextButtonPressed(lastSectionId);
                              } else {
                                // Some required fields are missing, ask for them first
                                setState(() {
                                  _chatMessages.add(Message(
                                    content:
                                        'Before submitting, please provide the following required information: ${missingFields.join(', ')}',
                                    role: MessageRole.assistant,
                                  ));
                                });

                                // Reset to the first section to process missing fields
                                _currentSectionId = 'section_0';
                                _currentInputIndex = 0;
                                _waitingForUserInput = false;

                                // Process the next field after a short delay
                                Future.delayed(
                                    const Duration(milliseconds: 800), () {
                                  if (mounted) {
                                    _processNextInputField();
                                  }
                                });
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor:
                                  Theme.of(context).colorScheme.primary,
                              foregroundColor:
                                  Theme.of(context).colorScheme.onPrimary,
                            ),
                            child: const Text('Yes'),
                          ),
                          const SizedBox(width: 12),
                          // No button
                          OutlinedButton(
                            onPressed: () {
                              // Add user rejection message
                              setState(() {
                                _chatMessages.add(Message(
                                  content: 'No, I need to make changes',
                                  role: MessageRole.user,
                                ));

                                // Add assistant response
                                _chatMessages.add(Message(
                                  content:
                                      'No problem. You can review and edit your inputs. Let me know if you need any help.',
                                  role: MessageRole.assistant,
                                ));
                              });
                            },
                            child: const Text('No'),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }

              // Check if this is an interactive control message
              if (message.content.startsWith('_interactive_control_')) {
                final fieldId =
                    message.content.substring('_interactive_control_'.length);

                // Find the field in all sections
                InputField? field;
                String sectionId = '';

                // Check if _currentSectionId is valid
                if (_currentSectionId.isNotEmpty &&
                    _currentSectionId.contains('_')) {
                  try {
                    final sectionIndex =
                        int.parse(_currentSectionId.split('_')[1]);
                    if (sectionIndex <
                        workflowProvider.allWorkflowInputs.length) {
                      final section =
                          workflowProvider.allWorkflowInputs[sectionIndex];
                      sectionId = _currentSectionId;

                      // Try to find the field in this section
                      for (final f in section.inputFields) {
                        if (f.attributeId == fieldId) {
                          field = f;
                          break;
                        }
                      }
                    }
                  } catch (e) {
                    Logger.warning('Error parsing section index: $e');
                  }
                }

                // If field not found in current section, search all sections
                if (field == null) {
                  for (int i = 0;
                      i < workflowProvider.allWorkflowInputs.length;
                      i++) {
                    final section = workflowProvider.allWorkflowInputs[i];
                    for (final f in section.inputFields) {
                      if (f.attributeId == fieldId) {
                        field = f;
                        sectionId = 'section_$i';
                        break;
                      }
                    }
                    if (field != null) break;
                  }
                }

                // If still not found, use a default field
                if (field == null) {
                  Logger.warning('Field not found: $fieldId');
                  if (workflowProvider.allWorkflowInputs.isNotEmpty &&
                      workflowProvider
                          .allWorkflowInputs[0].inputFields.isNotEmpty) {
                    field =
                        workflowProvider.allWorkflowInputs[0].inputFields.first;
                    sectionId = 'section_0';
                  } else {
                    return const Text('Field not found');
                  }
                }

                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Container(
                      constraints: BoxConstraints(
                        maxWidth: MediaQuery.of(context).size.width * 0.75,
                      ),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.grey[800]
                            : Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.grey[300]!,
                          width: 1,
                        ),
                      ),
                      child: _buildInteractiveControl(field, sectionId),
                    ),
                  ),
                );
              }

              // Check if this is a system information label
              if (message.content == 'System Information:' ||
                  message.content ==
                      'Please provide the following information:') {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8, top: 16),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color:
                            Theme.of(context).colorScheme.primary.withAlpha(25),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        message.content,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ),
                  ),
                );
              }

              // Check if this is a system field message
              if (message.content.startsWith('_system_field_')) {
                final fieldId =
                    message.content.substring('_system_field_'.length);
                Logger.info('Rendering system field: $fieldId');

                // Find the field in all sections
                InputField? field;

                final workflowProvider =
                    Provider.of<WorkflowProvider>(context, listen: false);
                final allInputs = workflowProvider.allWorkflowInputs;

                // Search for the field in all sections
                for (int i = 0; i < allInputs.length; i++) {
                  final section = allInputs[i];
                  for (final f in section.inputFieldsSystem) {
                    if (f.attributeId == fieldId) {
                      field = f;
                      break;
                    }
                  }
                  if (field != null) break;
                }

                if (field == null) {
                  return const SizedBox.shrink();
                }

                // Display the system field in a styled container
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Container(
                      constraints: BoxConstraints(
                        maxWidth: MediaQuery.of(context).size.width * 0.75,
                      ),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.grey[800]
                            : Colors.grey[200],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.grey[300]!,
                          width: 1,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Field label
                          Text(
                            field.displayName,
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 14,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(height: 4),
                          // Field value
                          Text(
                            // If value is null, leave it empty instead of showing 'Not available'
                            field.inputValue != null
                                ? field.inputValue.toString()
                                : '',
                            style: TextStyle(
                              fontSize: 16,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }

              // Check if this is a submit section message
              if (message.content.startsWith('_submit_section_')) {
                final sectionId =
                    message.content.substring('_submit_section_'.length);
                Logger.info('Rendering submit buttons for section: $sectionId');

                // Check if this section has already been submitted
                if (_submittedChatSections.containsKey(sectionId) &&
                    _submittedChatSections[sectionId] == true) {
                  Logger.info(
                      'Section $sectionId has already been submitted, hiding buttons');
                  // Return an empty container instead of buttons
                  return const SizedBox.shrink();
                }

                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Container(
                      constraints: BoxConstraints(
                        maxWidth: MediaQuery.of(context).size.width * 0.75,
                      ),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.grey[800]
                            : Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.grey[300]!,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          ElevatedButton(
                            onPressed: () {
                              // Add user message first
                              setState(() {
                                _chatMessages.add(Message(
                                  content: 'Yes',
                                  role: MessageRole.user,
                                ));
                              });

                              // Check for missing required fields before submitting
                              final missingRequiredFields =
                                  _checkMissingRequiredFields(sectionId);

                              Logger.info(
                                  'Submit button clicked - Missing fields: ${missingRequiredFields.isEmpty ? "None" : missingRequiredFields.join(", ")}');

                              if (missingRequiredFields.isEmpty) {
                                // All required fields have values
                                Logger.info(
                                    'All required fields have values, proceeding with submission');

                                // Mark this section as submitted
                                setState(() {
                                  _submittedChatSections[sectionId] = true;
                                });

                                // Handle the submission
                                _handleNextButtonPressed(sectionId);
                              } else {
                                // Some required fields are missing, ask for them first
                                Logger.info(
                                    'Missing required fields, prompting user');

                                setState(() {
                                  _chatMessages.add(Message(
                                    content:
                                        'Before submitting, please provide the following required information: ${missingRequiredFields.join(', ')}',
                                    role: MessageRole.assistant,
                                  ));
                                });

                                // Find the first missing field
                                final section =
                                    workflowProvider.allWorkflowInputs[
                                        int.parse(sectionId.split('_')[1])];
                                int missingFieldIndex = -1;

                                // Find the index of the first missing required field
                                for (int i = 0;
                                    i < section.inputFields.length;
                                    i++) {
                                  final field = section.inputFields[i];
                                  if (field.required &&
                                      !workflowProvider.inputValueStore
                                          .hasValue(
                                              sectionId, field.attributeId)) {
                                    missingFieldIndex = i;
                                    Logger.info(
                                        'Found first missing field at index $i: ${field.displayName}');
                                    break;
                                  }
                                }

                                // Set the current section and index to the missing field
                                _currentSectionId = sectionId;
                                _currentInputIndex = missingFieldIndex >= 0
                                    ? missingFieldIndex
                                    : 0;
                                _waitingForUserInput = false;

                                Logger.info(
                                    'Set current section to $_currentSectionId, index to $_currentInputIndex');

                                // Process the next field after a short delay
                                Future.delayed(
                                    const Duration(milliseconds: 800), () {
                                  if (mounted) {
                                    _processNextInputField();
                                  }
                                });
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor:
                                  Theme.of(context).colorScheme.primary,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('Yes'),
                          ),
                          const SizedBox(width: 12),
                          TextButton(
                            onPressed: () {
                              // Add user message
                              setState(() {
                                _chatMessages.add(Message(
                                  content: 'No',
                                  role: MessageRole.user,
                                ));

                                // Add message about continuing
                                _chatMessages.add(Message(
                                  content:
                                      'You can continue editing the fields. Let me know when you\'re ready to submit.',
                                  role: MessageRole.assistant,
                                ));
                              });
                            },
                            child: const Text('No'),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }

              // NSL message bubble
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Container(
                    constraints: BoxConstraints(
                      maxWidth: MediaQuery.of(context).size.width * 0.75,
                    ),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.grey[800]
                          : Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.grey[300]!,
                        width: 1,
                      ),
                    ),
                    child: Text(
                      message.content,
                      style: TextStyle(
                        fontFamily: 'TestTiemposText',
                        fontSize: fontSize,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.white
                            : Colors.black87,
                      ),
                    ),
                  ),
                ),
              );
            },
          );
        })),

        // Chat input field
        ChatTextField(
          controller: _textController,
          hintText: _waitingForUserInput &&
                  _currentChatInputField?.uiControl == 'oj-input-text'
              ? 'Type ${_currentChatInputField?.displayName}...'
              : 'Type a message...',
          isLoading: workflowProvider.isLoading,
          onSubmitted: (message) {
            if (message.trim().isEmpty) return;

            // Add user message
            setState(() {
              _chatMessages.add(Message(
                content: message,
                role: MessageRole.user,
              ));
            });

            // If waiting for input for a text field, process it
            if (_waitingForUserInput && _currentChatInputField != null) {
              if (_currentChatInputField!.uiControl == 'oj-input-text' ||
                  _currentChatInputField!.uiControl == 'oj-text-area') {
                _handleUserInputForField(message);
              } else {
                // For non-text inputs, show a message
                setState(() {
                  _chatMessages.add(Message(
                    content:
                        'Please use the provided controls to enter ${_currentChatInputField!.displayName}.',
                    role: MessageRole.assistant,
                  ));
                });
              }
            } else if (message.toLowerCase().contains('submit') ||
                message.toLowerCase().contains('yes')) {
              // Handle submission request
              setState(() {
                _chatMessages.add(Message(
                  content: 'Processing your submission...',
                  role: MessageRole.assistant,
                ));

                // Reset the waiting state before submitting
                _waitingForUserInput = false;
                _currentChatInputField = null;
              });

              // Find the last section ID
              final lastSectionId =
                  'section_${workflowProvider.allWorkflowInputs.length - 1}';

              // Log the InputValueStore state before execution
              Logger.info('InputValueStore state before execution:');
              workflowProvider.inputValueStore.debugPrintValues();

              // Trigger the next button press for the last section
              _handleNextButtonPressed(lastSectionId);
            } else {
              // General message handling
              setState(() {
                _chatMessages.add(Message(
                  content:
                      'I\'m here to help you complete this workflow. Please follow the prompts to provide the required information.',
                  role: MessageRole.assistant,
                ));
              });
            }
          },
          onCancel: () {
            // Handle cancellation if needed
          },
        ),
      ],
    );
  }

  Widget formBody(context) {
    return Column(
      children: [
        Expanded(
          child: Consumer<WorkflowProvider>(
            builder: (context, workflowProvider, _) {
              if (workflowProvider.isLoading) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // CircularProgressIndicator(),
                      // SizedBox(height: 16),
                      // Text('Processing workflow...'),
                    ],
                  ),
                );
              }

              if (workflowProvider.error != null) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error_outline, color: Colors.red, size: 48),
                      SizedBox(height: 16),
                      Text(
                        'Error: ${workflowProvider.error}',
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 24),
                      ElevatedButton(
                        onPressed: () {
                          _initializeWorkflow();
                        },
                        child: Text('Retry'),
                      ),
                    ],
                  ),
                );
              }

              final workflowInstance = workflowProvider.workflowInstance;
              // We don't need workflowInputs here anymore as sections are created in didChangeDependencies

              if (workflowInstance == null) {
                return const Center(
                    child: Text('No workflow instance available'));
              }

              // Sections are created in didChangeDependencies

              return SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // const SizedBox(height: 24),
                    const SizedBox(height: 16),
                    // Display all input sections
                    if (_inputSections.isEmpty) ...[
                      const Padding(
                        padding: EdgeInsets.all(16.0),
                        child: Center(
                          child: Text(
                              'No input sections available. Please wait...'),
                        ),
                      ),
                      // Add a direct check for workflow inputs
                      if (workflowProvider.workflowInputs != null) ...[
                        Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: ElevatedButton(
                            onPressed: () {
                              // Create a section directly from current inputs
                              final inputs = workflowProvider.workflowInputs!;

                              // Generate a unique section ID based on timestamp
                              final sectionId =
                                  'section_direct_${DateTime.now().millisecondsSinceEpoch}';

                              // Check if this section already exists using our helper method
                              if (!_sectionExists(sectionId)) {
                                final sectionWidget =
                                    _buildInputSection(inputs, sectionId);
                                setState(() {
                                  _inputSections.add(KeyedSubtree(
                                      key: ValueKey(
                                          'keyed_${sectionId}_${DateTime.now().millisecondsSinceEpoch}_submitted_${_submittedSections[sectionId] ?? false}'),
                                      child: sectionWidget));
                                });
                              } else {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                      content: Text('Section already exists')),
                                );
                              }
                            },
                            child: const Text(
                                'Create Section from Current Inputs'),
                          ),
                        ),
                      ],
                    ] else
                      // When _showOnlyCurrentLO is true, show only the last section
                      // Otherwise, show all sections
                      ...(_showOnlyCurrentLO
                          ? _inputSections.where((section) {
                              // Extract the section ID from the key
                              final keyString =
                                  (section as KeyedSubtree).key.toString();
                              final match = RegExp(r'keyed_(section_\d+)')
                                  .firstMatch(keyString);
                              final sectionId = match?.group(1) ?? '';

                              // Check if this is the last section
                              final sectionIdPart = sectionId.split('_').last;
                              final sectionIndex =
                                  int.tryParse(sectionIdPart) ?? -1;
                              final isLastSection =
                                  sectionIndex == _getLastSectionIndex();

                              Logger.info(
                                  'Section $sectionId isLastSection: $isLastSection');
                              return isLastSection;
                            }).toList()
                          : _inputSections),
                  ],
                ),
              );
            },
          ),
        ),
        // Add ChatTextField at the bottom
        ChatTextField(
          controller: _textController,
          hintText: 'Type a message...',
          isLoading: false, // You can set this based on your loading state
          onSubmitted: _handleChatSubmit,
          onCancel: _handleChatCancel,
        ),
      ],
    );
  }

  // Build a new input section and add it to the list
  Widget _buildInputSection(WorkflowInputs inputs, String sectionId) {
    Logger.info(
        '_buildInputSection called for sectionId: $sectionId with ${inputs.inputFields.length} fields');
    Logger.info(
        'Input fields: ${inputs.inputFields.map((f) => '${f.attributeId}: ${f.displayName}').join(', ')}');

    // Debug log to verify the state when building the section
    Logger.info(
        'Section state: isSubmitted: ${_submittedSections[sectionId]}, isExpanded: ${_expandedSections[sectionId]}');

    // Log all existing sections
    Logger.info(
        'All sections: ${_inputSections.map((w) => (w as KeyedSubtree).key.toString()).join('\n')}');

    // Log the state of all input fields
    _logInputFieldsState();

    // Determine if this is the last section
    final sectionIdPart = sectionId.split('_').last;
    final isLastSection = sectionId.endsWith('_$sectionIdPart') &&
        (sectionIdPart.startsWith('direct') ||
            (int.tryParse(sectionIdPart) ?? -1) == _getLastSectionIndex());

    // Get the workflow provider to check the number of sections
    final workflowProvider =
        Provider.of<WorkflowProvider>(context, listen: false);
    final allInputs = workflowProvider.allWorkflowInputs;
    final isSingleSection = allInputs.length <= 1;

    // Set initial expanded state - if there's only one section, always keep it expanded
    // Otherwise, only the last section should be expanded by default
    if (!_expandedSections.containsKey(sectionId)) {
      _expandedSections[sectionId] = isSingleSection || isLastSection;
      Logger.info(
          'Setting initial expanded state for $sectionId to ${_expandedSections[sectionId]} (isSingleSection: $isSingleSection, isLastSection: $isLastSection)');
    } else if (isSingleSection) {
      // If there's only one section, always keep it expanded regardless of previous state
      _expandedSections[sectionId] = true;
      Logger.info(
          'Forcing expanded state for single section $sectionId to true');
    } else {
      Logger.info(
          'Current expanded state for $sectionId is ${_expandedSections[sectionId]}');
    }

    // Don't clear previous keys for this section if it's being rebuilt
    // This ensures that input values are preserved when the section is rebuilt
    // _inputFieldKeys.removeWhere((key, _) => key.startsWith('$sectionId:'));

    // Instead, log the existing keys for this section
    final existingKeys = _inputFieldKeys.entries
        .where((entry) => entry.key.startsWith('$sectionId:'))
        .map((entry) => entry.key)
        .toList();
    Logger.info(
        'Existing input field keys for section $sectionId: $existingKeys');

    // Get values from the InputValueStore
    final valueStoreProvider =
        Provider.of<WorkflowProvider>(context, listen: false);
    valueStoreProvider.inputValueStore.debugPrintValues();

    // Determine if this is the active section (last one)
    final isActiveSection = isLastSection;

    final sectionWidget = StatefulBuilder(
        // Add a key that includes the submission state to force rebuild when it changes
        key: ValueKey(
            'stateful_${sectionId}_submitted_${_submittedSections[sectionId] ?? false}'),
        builder: (context, setStateLocal) {
          final isExpanded = _expandedSections[sectionId] ?? false;
          final isSubmitted = _submittedSections[sectionId] ?? false;
          Logger.info(
              'Building section $sectionId with expansion state: $isExpanded, submission state: $isSubmitted, key: stateful_${sectionId}_submitted_${_submittedSections[sectionId] ?? false}');
          return Card(
            elevation: 2,
            margin: const EdgeInsets.only(bottom: 24.0),
            // Add a border to highlight the active section
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: BorderSide(
                color: isActiveSection
                    ? Theme.of(context).colorScheme.primary
                    : Colors.transparent,
                width: isActiveSection ? 1 : 0,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Expandable content
                if (_expandedSections[sectionId] ?? false)
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Builder(builder: (context) {
                      // Combine all field types
                      // Create a combined list of all fields
                      final List<InputField> allFields = [];
                      allFields.addAll(inputs.systemInputs);
                      allFields.addAll(inputs.infoInputs);
                      allFields.addAll(inputs.userInputs);

                      // Log dependent inputs for debugging
                      Logger.info(
                          'Processing dependent inputs for section $sectionId');
                      Logger.info(
                          'Total dependent inputs: ${inputs.dependentInputs.length}');

                      // Add all dependent fields to the list - visibility will be controlled by Visibility widget
                      allFields.addAll(inputs.dependentInputs);

                      // Log each dependent field for debugging
                      for (final field in inputs.dependentInputs) {
                        final parentIds = field.parentIds ?? [];

                        Logger.info(
                            'Including dependent field ${field.displayName} (${field.attributeId}): '
                            'UI=${field.uiControl}, '
                            'parentIds=${parentIds.join(", ")}, '
                            'value=${field.inputValue}, '
                            'options=${field.dropdownOptions?.length ?? 0}');
                      }

                      // Sort all fields by attribute_id
                      allFields.sort(
                          (a, b) => a.attributeId.compareTo(b.attributeId));

                      // Check if we're on web platform
                      final isWeb = MediaQuery.of(context).size.width > 600;

                      // Create a list to hold all the widgets
                      final List<Widget> fieldWidgets = [];

                      // If on web, group fields in pairs
                      if (isWeb) {
                        // Process fields in pairs for web
                        for (int i = 0; i < allFields.length; i += 2) {
                          // Create a row with two fields
                          if (i + 1 < allFields.length) {
                            // Two fields available
                            fieldWidgets.add(
                              Padding(
                                padding: const EdgeInsets.only(bottom: 16.0),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      child: _buildFieldWidget(
                                          allFields[i], sectionId, isSubmitted),
                                    ),
                                    const SizedBox(width: 16.0),
                                    Expanded(
                                      child: _buildFieldWidget(allFields[i + 1],
                                          sectionId, isSubmitted),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          } else {
                            // Only one field left, add it with an empty expanded SizedBox
                            fieldWidgets.add(
                              Padding(
                                padding: const EdgeInsets.only(bottom: 16.0),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      child: _buildFieldWidget(
                                          allFields[i], sectionId, isSubmitted),
                                    ),
                                    const SizedBox(width: 16.0),
                                    Expanded(
                                      child:
                                          const SizedBox(), // Empty SizedBox for the second column
                                    ),
                                  ],
                                ),
                              ),
                            );
                          }
                        }
                      } else {
                        // For mobile, process fields one by one
                        for (int i = 0; i < allFields.length; i++) {
                          fieldWidgets.add(_buildFieldWidget(
                              allFields[i], sectionId, isSubmitted));
                        }
                      }

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Add all field widgets
                          ...fieldWidgets,

                          // Add Next/Submit button
                          const SizedBox(height: 16),
                          Align(
                            alignment: Alignment.centerRight,
                            child: ElevatedButton(
                              // Only enable the button for the last section and if not already submitted
                              onPressed: (isLastSection && !isSubmitted)
                                  ? () => _handleNextButtonPressed(sectionId)
                                  : null,
                              style: ElevatedButton.styleFrom(
                                backgroundColor:
                                    Theme.of(context).colorScheme.primary,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 24, vertical: 12),
                                // Disabled style
                                disabledBackgroundColor: Colors.grey[300],
                                disabledForegroundColor: Colors.grey[600],
                              ),
                              // Show 'Submitted' for submitted sections, 'Submit' for the last section, 'Next' for others
                              child: Text(isSubmitted
                                  ? 'Submitted'
                                  : (isLastSection ? 'Submit' : 'Next')),
                            ),
                          ),
                        ],
                      );
                    }),
                  ),
              ],
            ),
          );
        });
    return sectionWidget;
  }

  // Handle Next button press
  Future<void> _handleNextButtonPressed(String sectionId) async {
    // Validate all fields in this section
    bool isValid = true;
    final inputData = <String, dynamic>{};

    // Get the workflow provider
    final workflowProvider =
        Provider.of<WorkflowProvider>(context, listen: false);

    // Get the global input value store
    final inputStore = InputValueStore();
    inputStore.debugPrintValues();

    // Check if we're in chat view or form view
    if (_showChat) {
      // In chat view, collect all values from the InputValueStore
      Logger.info('Collecting input data from chat view');
      Logger.info(
          'Current chat state: _currentSectionId=$_currentSectionId, _currentInputIndex=$_currentInputIndex');
      Logger.info(
          'Current chat input field: ${_currentChatInputField?.attributeId}');
      Logger.info('Waiting for user input: $_waitingForUserInput');

      // Force reset the waiting state to ensure we're not in an inconsistent state
      if (_waitingForUserInput) {
        Logger.info('Resetting waiting state before collecting input data');
        _waitingForUserInput = false;
        _currentChatInputField = null;
      }

      // Get all workflow inputs to find field names
      final allInputs = workflowProvider.allWorkflowInputs;

      // DIRECT APPROACH: Manually add the values we know should be there
      // This is a workaround to ensure the values are included in the API call
      Logger.info('Using direct approach to collect input data');

      // Get all values from both InputValueStore instances
      final localValues = inputStore.getAllValues();
      final providerValues = workflowProvider.inputValueStore.getAllValues();
      Logger.info('All values in local InputValueStore: $localValues');
      Logger.info('All values in provider InputValueStore: $providerValues');

      // Merge the values from both stores
      final allValues = <String, dynamic>{};
      allValues.addAll(localValues);
      allValues.addAll(providerValues);
      Logger.info('Merged values from both stores: $allValues');

      // Loop through all sections and their fields
      for (int i = 0; i < allInputs.length; i++) {
        final currentSectionId = 'section_$i';
        final section = allInputs[i];

        // Only process fields from the current section (matching the sectionId parameter)
        if (currentSectionId == sectionId) {
          Logger.info(
              'Collecting values only from section: $sectionId (${section.localObjective})');

          // Only process user input fields and dependent fields, not system or info fields
          // Process user inputs
          for (final field in section.userInputs) {
            // Skip system and info fields - they should not be sent to the API
            if (field.sourceType.toLowerCase() == 'system' ||
                field.sourceType.toLowerCase() == 'information' ||
                field.metadata.isInformational) {
              Logger.info(
                  'Skipping non-interactive field: ${field.attributeId} (${field.displayName})');
              continue;
            }

            if (field.required && field.sourceType != "system_dependent") {
              final key = '$sectionId:${field.attributeId}';

              // First try to get the value from the getAllValues result
              if (allValues.containsKey(key)) {
                final value = allValues[key];
                Logger.info('Direct approach: Using value for $key: $value');
                // Use displayName instead of attributeId to match form view behavior
                inputData[field.displayName] = value;
              }
              // Then try the normal way
              else if (inputStore.hasValue(sectionId, field.attributeId)) {
                final value = inputStore.getValue(sectionId, field.attributeId);
                Logger.info(
                    'Chat view: Using stored value for $sectionId:${field.attributeId}: $value');

                // Use displayName instead of attributeId to match form view behavior
                inputData[field.displayName] = value;
              } else {
                // If the field is required but has no value, mark as invalid
                if (field.required) {
                  isValid = false;
                  Logger.warning(
                      'Required field ${field.attributeId} has no value');
                }
              }
            }
          }

          // Process dependent inputs
          for (final field in section.dependentInputs) {
            // Skip system and info fields - they should not be sent to the API
            if (field.sourceType.toLowerCase() == 'system' ||
                field.sourceType.toLowerCase() == 'information' ||
                field.metadata.isInformational) {
              Logger.info(
                  'Skipping non-interactive field: ${field.attributeId} (${field.displayName})');
              continue;
            }

            final key = '$sectionId:${field.attributeId}';

            // First try to get the value from the getAllValues result
            if (allValues.containsKey(key)) {
              final value = allValues[key];
              Logger.info('Direct approach: Using value for $key: $value');
              // Use displayName instead of attributeId to match form view behavior
              inputData[field.displayName] = value;
            }
            // Then try the normal way
            else if (inputStore.hasValue(sectionId, field.attributeId)) {
              final value = inputStore.getValue(sectionId, field.attributeId);
              Logger.info(
                  'Chat view: Using stored value for $sectionId:${field.attributeId}: $value');

              // Use displayName instead of attributeId to match form view behavior
              inputData[field.displayName] = value;
            } else {
              // If the field is required but has no value, mark as invalid
              if (field.required) {
                isValid = false;
                Logger.warning(
                    'Required field ${field.attributeId} has no value');
              }
            }
          }
        } else {
          Logger.info(
              'Skipping section: $currentSectionId (not the current section)');
        }
      }
    } else {
      // In form view, use the input field widgets
      Logger.info('Collecting input data from form view');

      // Get all keys for this section
      final sectionKeys = _inputFieldKeys.entries
          .where((entry) => entry.key.startsWith('$sectionId:'))
          .toList();

      // Validate each field and collect values
      for (final entry in sectionKeys) {
        final fieldKey = entry.key;
        final fieldState = entry.value.currentState;

        if (fieldState != null) {
          // Skip system fields - they should not be sent to the API
          if (fieldState.field.sourceType == 'System') {
            Logger.info(
                'Skipping system field: ${fieldState.field.attributeId} (${fieldState.field.displayName})');
            continue;
          }

          final fieldValid = fieldState.validate();
          isValid = isValid && fieldValid;

          if (fieldValid) {
            // First try to get the value from the InputValueStore
            dynamic value;

            // Parse the fieldKey to get sectionId and fieldId
            final keyParts = fieldKey.split(':');
            if (keyParts.length == 2) {
              final sectionId = keyParts[0];
              final fieldId = keyParts[1];

              if (inputStore.hasValue(sectionId, fieldId)) {
                value = inputStore.getValue(sectionId, fieldId);
                Logger.info(
                    'Form view: Using stored value for $fieldKey: $value');
              } else {
                // Fall back to the field state
                value = fieldState.getValue();
                Logger.info(
                    'Form view: Using field state value for $fieldKey: $value');
              }
            } else {
              // Invalid key format, fall back to the field state
              value = fieldState.getValue();
              Logger.info(
                  'Form view: Using field state value (invalid key format) for $fieldKey: $value');
            }

            // Add to input data if valid
            inputData[fieldState.getFieldName()] = value;
          }
        }
      }
    }

    if (!isValid) {
      // Show error message if validation fails
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('Please fix the errors before proceeding')),
      );
      return;
    }

    // Get the workflow instance
    final workflowInstance = workflowProvider.workflowInstance;

    if (workflowInstance == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Workflow instance not found')),
      );
      return;
    }

    // Use a more reliable approach with a separate method for showing/hiding the loading dialog
    _showLoadingDialog();

    try {
      // Execute the workflow
      // Log the input data before executing the workflow
      Logger.info('Executing workflow with input data:');
      inputData.forEach((key, value) {
        Logger.info('  $key: $value');
      });

      // DIRECT APPROACH FOR CHAT VIEW: If we're in chat view and there are missing required fields,
      // try to add them directly from the InputValueStore
      if (_showChat) {
        // Get all values from the InputValueStore
        final allValues = workflowProvider.inputValueStore.getAllValues();
        Logger.info('All values in InputValueStore before execution:');
        allValues.forEach((key, value) {
          Logger.info('  $key: $value');
        });

        // Create a new map to store only the current section's values
        final currentSectionValues = <String, dynamic>{};

        // Loop through all keys and add only values from the current section
        allValues.forEach((key, value) {
          // Extract the field ID from the key (format: sectionId:fieldId)
          final parts = key.split(':');
          if (parts.length == 2) {
            final sectionIdPart = parts[0];
            final fieldId = parts[1];

            // Only process values from the current section
            if (sectionIdPart == sectionId) {
              Logger.info(
                  'Processing value from current section: $key = $value');

              // Find the corresponding field to get its display name
              final allInputs = workflowProvider.allWorkflowInputs;
              InputField? field;

              // Search through all sections and fields
              for (final section in allInputs) {
                for (final f in section.inputFields) {
                  if (f.attributeId == fieldId) {
                    field = f;
                    break;
                  }
                }
                if (field != null) break;
              }

              // Use display name if found, otherwise use field ID
              final displayName = field?.displayName ?? fieldId;

              // Add to the current section values
              currentSectionValues[displayName] = value;
              Logger.info(
                  '  Added field $displayName with value $value to current section values');
            } else {
              Logger.info(
                  'Skipping value from different section: $key = $value');
            }
          }
        });

        // Replace the inputData with only the current section's values
        inputData.clear();
        inputData.addAll(currentSectionValues);

        // Log the updated input data
        Logger.info(
            'Updated input data after direct approach (current section only):');
        inputData.forEach((key, value) {
          Logger.info('  $key: $value');
        });
      }

      final result = await workflowProvider.executeWorkflow(
        instanceId: workflowInstance.instanceId,
        inputData: inputData,
      );

      // Log the result for debugging
      Logger.info('Workflow execution result: $result');

      // Check if we have a next_lo_id in the result
      final hasNextLO = result.containsKey('next_lo_id') &&
          result['next_lo_id'] != null &&
          result['next_lo_id'].toString().isNotEmpty;

      // If we're in chat view and have new inputs, add the system fields from the new section
      if (_showChat && hasNextLO) {
        // Get the latest section (the one just added)
        final latestSectionIndex =
            workflowProvider.allWorkflowInputs.length - 1;
        if (latestSectionIndex >= 0) {
          final latestSection =
              workflowProvider.allWorkflowInputs[latestSectionIndex];

          // Check if it has system or info fields
          if (latestSection.systemInputs.isNotEmpty ||
              latestSection.infoInputs.isNotEmpty) {
            // Add a separator
            setState(() {
              _chatMessages.add(Message(
                content: 'New information received:',
                role: MessageRole.assistant,
              ));

              // Add a header for system fields
              _chatMessages.add(Message(
                content: 'System Information:',
                role: MessageRole.assistant,
              ));

              // Process system fields
              if (latestSection.systemInputs.isNotEmpty) {
                // Sort system fields by attribute_id
                final sortedSystemFields =
                    List<InputField>.from(latestSection.systemInputs);
                sortedSystemFields
                    .sort((a, b) => a.attributeId.compareTo(b.attributeId));

                // Add each system field
                for (final field in sortedSystemFields) {
                  // If value is null, leave it empty instead of showing 'Not available'
                  final fieldValue = field.inputValue != null
                      ? field.inputValue.toString()
                      : '';

                  // Add the field as a regular message
                  _chatMessages.add(Message(
                    content: '${field.displayName}: $fieldValue',
                    role: MessageRole.assistant,
                  ));

                  Logger.info(
                      'Added new system field ${field.attributeId} with value $fieldValue');
                }
              }

              // Process info fields
              if (latestSection.infoInputs.isNotEmpty) {
                // Add a header for info fields
                _chatMessages.add(Message(
                  content: 'Information:',
                  role: MessageRole.assistant,
                ));

                // Sort info fields by attribute_id
                final sortedInfoFields =
                    List<InputField>.from(latestSection.infoInputs);
                sortedInfoFields
                    .sort((a, b) => a.attributeId.compareTo(b.attributeId));

                // Add each info field
                for (final field in sortedInfoFields) {
                  // If value is null, leave it empty instead of showing 'Not available'
                  final fieldValue = field.inputValue != null
                      ? field.inputValue.toString()
                      : '';

                  // Add the field as a regular message
                  _chatMessages.add(Message(
                    content: '${field.displayName}: $fieldValue',
                    role: MessageRole.assistant,
                  ));

                  Logger.info(
                      'Added new info field ${field.attributeId} with value $fieldValue');
                }
              }

              // Add a prompt for the next step
              _chatMessages.add(Message(
                content: 'Please provide the following information:',
                role: MessageRole.assistant,
              ));
            });
          }
        }
      }

      Logger.info('Has next LO: $hasNextLO');

      // If we're in chat view, force a rebuild of the sections
      if (_showChat) {
        // Rebuild the UI to reflect the new inputs
        setState(() {
          // Don't clear any messages - keep the entire conversation history
          // Add a transition message to indicate we're moving to the next step
          if (hasNextLO) {
            _chatMessages.add(Message(
              content: 'Moving to the next step of the workflow...',
              role: MessageRole.assistant,
            ));
          }
        });
      }

      // Check if widget is still mounted before updating UI
      if (!mounted) return;

      // Hide loading dialog
      _hideLoadingDialog();

      // Store the current values of all input fields in this section
      final sectionValues = <String, dynamic>{};

      // Only process section keys in form view
      if (!_showChat) {
        // Get all keys for this section
        final sectionKeys = _inputFieldKeys.entries
            .where((entry) => entry.key.startsWith('$sectionId:'))
            .toList();

        for (final entry in sectionKeys) {
          final fieldKey = entry.key;
          final fieldState = entry.value.currentState;
          if (fieldState != null) {
            final value = fieldState.getValue();
            if (value != null) {
              sectionValues[fieldKey] = value;
              Logger.info('Stored value for $fieldKey: $value');
            }
          }
        }
      }

      // Mark this section as submitted
      setState(() {
        _submittedSections[sectionId] = true;

        // Also mark it as submitted in the chat view
        _submittedChatSections[sectionId] = true;

        // Check if there's only one section
        final workflowProvider =
            Provider.of<WorkflowProvider>(context, listen: false);
        final allInputs = workflowProvider.allWorkflowInputs;
        final isSingleSection = allInputs.length <= 1;

        // Collapse the section after submission only if there's more than one section
        if (!isSingleSection) {
          _expandedSections[sectionId] = false;
          Logger.info('Collapsing section $sectionId after submission');
        } else {
          // Keep single section expanded
          _expandedSections[sectionId] = true;
          Logger.info(
              'Keeping single section $sectionId expanded after submission');
        }
      });

      // Force a rebuild of the section with the updated submission state
      _rebuildSection(sectionId);

      // Restore the values after rebuilding (only in form view)
      if (!_showChat) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            for (final entry in sectionValues.entries) {
              final fieldKey = entry.key;
              final value = entry.value;
              final fieldState = _inputFieldKeys[fieldKey]?.currentState;
              if (fieldState != null) {
                fieldState.setValue(value);
                Logger.info(
                    'Restored value for $fieldKey after rebuild: $value');
              }
            }
          }
        });
      }

      // Debug log to verify the state
      Logger.info(
          'Section $sectionId submitted. _submittedSections: $_submittedSections, _expandedSections: $_expandedSections');
      Logger.info(
          'Section key after submission: keyed_${sectionId}_timestamp_submitted_${_submittedSections[sectionId] ?? false}');

      // Log the state of all input fields
      _logInputFieldsState();

      // Check if there's no next_lo_id, which means the objective is completed
      if (!result.containsKey('next_lo_id') ||
          result['next_lo_id'] == null ||
          result['next_lo_id'].toString().isEmpty) {
        // Show completion dialog
        if (mounted) {
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (BuildContext context) {
              return AlertDialog(
                title: const Text('Success',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.check_circle_outline,
                      color: Colors.green,
                      size: 64,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Objective completed successfully',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontSize: 16),
                    ),
                  ],
                ),
                actions: [
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                    ),
                    onPressed: () {
                      // Clear the input store when completing an objective
                      _inputStore.clear();
                      Navigator.of(context).pop(); // Close dialog
                      Navigator.of(context)
                          .pop(); // Return to transaction screen
                    },
                    child: const Text('Done'),
                  ),
                ],
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16)),
                contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 24),
                actionsPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              );
            },
          );
        }
        return; // Exit the method early
      }

      // If we have a next_lo_id, show success message for the step
      if (mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Step completed successfully')),
        );

        // In chat view, also add a message to the chat
        if (_showChat) {
          setState(() {
            _chatMessages.add(Message(
              content: 'Step completed successfully!',
              role: MessageRole.assistant,
            ));

            // Add message about next steps
            _chatMessages.add(Message(
              content: 'There are more steps to complete. Let\'s continue.',
              role: MessageRole.assistant,
            ));
          });

          // Reset chat state to process new inputs
          _currentSectionId = '';
          _currentInputIndex = 0;
          _waitingForUserInput = false;
          _currentChatInputField = null;

          // Force a rebuild of the UI to reflect the new inputs
          setState(() {});

          // Log the current state of the workflow provider
          Logger.info('After API execution, workflow provider state:');
          Logger.info(
              '  allWorkflowInputs.length: ${workflowProvider.allWorkflowInputs.length}');
          for (int i = 0; i < workflowProvider.allWorkflowInputs.length; i++) {
            final section = workflowProvider.allWorkflowInputs[i];
            Logger.info('  Section $i: ${section.localObjective}');
            Logger.info('  User inputs: ${section.userInputs.length}');
            Logger.info('  System inputs: ${section.systemInputs.length}');
            Logger.info('  Info inputs: ${section.infoInputs.length}');
            Logger.info(
                '  Dependent inputs: ${section.dependentInputs.length}');

            // Log user inputs
            for (final field in section.userInputs) {
              Logger.info(
                  '    User field: ${field.attributeId}: ${field.displayName}');
            }
          }

          // Process the next field after a short delay
          Future.delayed(const Duration(milliseconds: 1200), () {
            if (mounted) {
              // Check if there are any input sections available
              if (workflowProvider.allWorkflowInputs.isNotEmpty) {
                // Set the current section to the last section (newest one)
                _currentSectionId =
                    'section_${workflowProvider.allWorkflowInputs.length - 1}';
                _currentInputIndex = 0;
                _waitingForUserInput = false;
                _currentChatInputField = null;
                Logger.info(
                    'Setting current section to $_currentSectionId after API execution');

                // Process the next field
                _processNextInputField();
              } else {
                // No more sections, show completion message
                setState(() {
                  _chatMessages.add(Message(
                    content:
                        'All steps completed successfully! There are no more inputs required.',
                    role: MessageRole.assistant,
                  ));
                });
              }
            }
          });
        }

        // Schedule an update of the input sections (for both views)
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            // Update input sections
            _updateInputSections();

            // Make sure the last section is expanded and others are collapsed
            setState(() {
              final workflowProvider =
                  Provider.of<WorkflowProvider>(context, listen: false);
              final allInputs = workflowProvider.allWorkflowInputs;
              final isSingleSection = allInputs.length <= 1;

              for (int i = 0; i < allInputs.length; i++) {
                final sectionId = 'section_$i';
                if (isSingleSection) {
                  _expandedSections[sectionId] = true;
                  Logger.info(
                      'Keeping single section $sectionId expanded after workflow update');
                } else {
                  _expandedSections[sectionId] = (i == allInputs.length - 1);
                  Logger.info(
                      'Setting section $sectionId expanded state to ${_expandedSections[sectionId]} after workflow update');
                }
              }
            });
          }
        });
      }
    } catch (e) {
      // Check if widget is still mounted before updating UI
      if (!mounted) return;

      // Hide loading dialog
      _hideLoadingDialog();

      // Don't clear the input store here, as we want to preserve the values
      // for the user to correct any errors

      // Show error message
      if (mounted) {
        // Extract the error message from the exception
        String errorMessage = e.toString();

        // If it's a server error with detail, extract and show that
        if (errorMessage.contains('Server error:')) {
          errorMessage =
              errorMessage.replaceAll('Exception: Server error: ', '');
        } else if (errorMessage.contains('Exception:')) {
          errorMessage = errorMessage.replaceAll('Exception: ', '');
        }

        // Show a more prominent error dialog for better visibility
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Error'),
            content: Text(errorMessage),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
        );

        // In chat view, also add an error message to the chat
        if (_showChat) {
          setState(() {
            _chatMessages.add(Message(
              content: 'Error: $errorMessage',
              role: MessageRole.assistant,
            ));
          });
        }
      }
    }
  }

  bool checkAllParentValuesHaveData(List<String>? parentIds, String sectionId) {
    if (parentIds == null || parentIds.isEmpty) {
      return false;
    }

    final workflowProvider =
        Provider.of<WorkflowProvider>(context, listen: false);
    final InputValueStore providerInputStore = workflowProvider.inputValueStore;

    // Check if all parent fields have values
    for (final parentId in parentIds) {
      // Check both local and provider input stores
      final hasParentValueLocal = _inputStore.hasValue(sectionId, parentId);
      final hasParentValueProvider =
          providerInputStore.hasValue(sectionId, parentId);
      final hasParentValue = hasParentValueLocal || hasParentValueProvider;

      if (!hasParentValue) {
        Logger.info('Parent field $parentId does not have a value');
        return false;
      }
    }

    Logger.info('All parent fields have values');
    return true;
  }
}
